<div class="table-content">
  <div class="on-panel">
    <div class="on-panel-header">
        <div class="right-button-group">
            <div class="pull-right" *ngIf="permission('create')">
                <a nz-button disabled="true"
                   routerLink="../kafka-config">
                    <i nz-icon nzType="plus"
                       nzTheme="outline"></i>
                    创建Kafka
                </a>
            </div>
        </div>
    </div>
    <div class="on-panel-body">
      <div class="action-bar clearfix">
          <span class="title">Kafka
              <div class="danger" *ngIf="showResLimit">
                  <i class="fa fa-exclamation-circle"></i>
                  每个用户最多可免费创建1个Kafka集群，如有其它需求请提交工单联系！
              </div>
          </span>
      </div>
      <nz-table #tableList
                [nzItemRender]="renderItemTemplate"
                [nzFrontPagination]="false"
                [nzTotal]="page.total"
                [nzLoading]="tableLoading"
                [nzPageIndex]="page.current"
                [nzPageSize]="page.size"
                (nzPageIndexChange)="pageChange($event)"
                [nzData]="tableListData">
        <thead>
        <tr>
          <th width="15%">实例名称</th>
          <th width="15%">VPC</th>
          <th width="15%">子网</th>
          <th width="15%">状态</th>
          <th width="15%">创建时间</th>
          <th width="20%">操作</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let data of tableList.data">
          <td class="fixed-td">{{data.name}}</td>
          <td>{{data.vpcName ? data.vpcName : '无'}}</td>
          <td>{{data.subnetName ? data.subnetName : '无'}}</td>
          <td>
            <span class="dot {{ getStatusClass(data) }}">{{ data.kafkaStatus }}</span>
          </td>
          <td>{{getdate(data.createTm)}}</td>
          <!--更多操作-->
          <td>
            <div class="on-table-actions" *ngIf="permission('view')"
                  [hidden]="busyStatus[data.id]">
                <div class="on-table-action-item"
                  (click)="toKafkaDetails(data);"
                  [ngClass]="{'disabled': !canGoToDetail(data)}">
                  <i nzTitle="查看详情"
                      nzTooltipContent="bottom"
                      nz-tooltip
                      class="icon fa fa-search"></i>
                </div>
                <div class="on-table-action-item" *ngIf="permission('create')"
                    (click)="turnOn(data);"
                    [ngClass]="{'disabled': !canTurnOn(data)}">
                    <i nzTitle="启动"
                        nzTooltipContent="bottom"
                        nz-tooltip
                        class="icon fa fa-play-circle-o"></i>
                </div>
                <div class="on-table-action-item" *ngIf="permission('delete')"
                  nz-popconfirm
                  nzTooltipContent="top"
                  [nzCondition]="!canDelete(data)"
                  nzTitle="确定要删除该实例吗？"
                  (nzOnConfirm)="deleteKafka(data);"
                  [ngClass]="{'disabled': !canDelete(data)}">
                  <i nzTitle="删除"
                      nzTooltipContent="bottom"
                      nz-tooltip
                      class="icon fa fa-trash-o"></i>
                </div>
              </div>
              <div class="on-table-actions"
                  [hidden]="!busyStatus[data.id]">
                  <div
                      class="action-loading-placeholder">
                      <i class="icon" nz-icon
                          [nzType]="'loading'"></i>
                      {{ getBusyText(data) }}
                  </div>
              </div>
          </td>
        </tr>
        <tr [hidden]="tableList.data.length || !tableLoading"
            class="loading-placeholder">
            <td colspan="100%"></td>
        </tr>
        </tbody>
      </nz-table>
      <ng-template #renderItemTemplate let-type let-page="page">
        <a *ngIf="type === 'prev'">« 上一页</a>
        <a *ngIf="type === 'next'">下一页 »</a>
        <a *ngIf="type === 'page'">{{ page }}</a>
      </ng-template>
    </div>
    
  </div>
</div>
