<div class="table-content" style="margin-top: 12px;">
    <div class="on-panel">
        <div class="content-base">
            <div class="content-square gap_0">
                <div class="header mb_10" style="display: flex;justify-content: space-between; ">基本信息
                    <div>
<!--                        <a nz-button class="default" [routerLink]="['..']">-->
<!--                            返&nbsp;回-->
<!--                        </a>-->
                    </div>
                </div>
                <div class="row card">
                    <div class="column p_8 pl_20" style="flex: 1">
                        集群名称
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        <span style="color:#1890ff;">
                                {{ clusterInfo.name }}</span><br>{{clusterInfo.uid}}
                    </div>
                    <div class="column p_8 pl_20" style="flex: 1">
                        状态
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        {{ clusterInfo.phase }}
                    </div>
                </div>
                <div class="row card">
                    <div class="column pl_20 p_8" style="flex: 1;">
                        创建时间
                    </div>
                    <div class="bg-white pl_20 p_8 bg-white" style="flex: 3;">
                        {{ clusterInfo.creationTimestamp }}
                    </div>
                    <div class="column p_8 pl_20" style="flex: 1">
                        版本
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        {{ clusterInfo.platformVersion }}
                    </div>
                </div>
                <div class="row card">
                    <div class="column p_8 pl_20" style="flex: 1">
                        类型
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        {{clusterInfo.k8sClusterType }} - {{clusterInfo.k8sClusterSubType}}
                    </div>
                    <div class="column p_8 pl_20" style="flex: 1">
                        VPC
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        {{clusterInfo.spVpcName }}
                    </div>
                </div>
                <div class="row card">
                    <div class="column p_8 pl_20" style="flex: 1">
                        节点数量
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        {{clusterInfo.nodeList ? clusterInfo.nodeList.length : '-' }}
                    </div>
                    <div class="column p_8 pl_20" style="flex: 1">
                        子网
                    </div>
                    <div class="column p_8 pl_20 bg-white" style="flex: 3;">
                        {{clusterInfo.subnetName}}
                    </div>
                </div>
            </div>
        </div>

        <div class="row m_0 mt_12">
            <div class="flex_1 m_0 bg-white p_16 pie_chart">
                <app-wave-chart-template
                        [title]="clusterInfo.name"
                        [chartData]="[42, 45, 50, 55, 58, 56, 55, 54, 50, 55, 58, 57, 55, 53, 50, 52, 55, 58, 60, 55, 52, 48, 44, 38, 35, 33, 30, 32, 25, 28]"
                        [chartHeight]="'300px'"
                        [xAxisLabel]="'时间'"
                        [yAxisLabel]="'CPU负载'"
                        [unit]="'%'"
                        [showArea]="true"
                        [smooth]="true"
                        [showGrid]="true"
                        [colorConfig]="{
                        waveColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        textColor: '#666',
                        gridColor: '#eee'
                    }">
                </app-wave-chart-template>
            </div>
            <div class="flex_1 m_0 bg-white p_16 horizontal_column_chart">
                <app-wave-chart-template
                        [title]="clusterInfo.name"
                        [chartData]="[32, 35, 40, 35, 38, 36, 35, 34, 30, 28, 26, 27, 25, 23, 22, 26, 25, 28, 30, 34, 36, 38, 40, 38, 35, 33, 30, 32, 35, 38]"
                        [chartHeight]="'250px'"
                        [xAxisLabel]="'时间'"
                        [yAxisLabel]="'GPU负载'"
                        [unit]="'%'"
                        [showArea]="true"
                        [smooth]="true"
                        [showGrid]="true"
                        [colorConfig]="{
                        waveColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        textColor: '#666',
                        gridColor: '#eee'
                    }">
                </app-wave-chart-template>
            </div>
        </div>
        <div class="row m_0 mt_12">
            <div class="flex_1 m_0 bg-white p_16 pie_chart">
                <app-wave-chart-template
                        [title]="clusterInfo.name"
                        [chartData]="[42, 45, 50, 55, 58, 56, 55, 58, 60, 63, 66, 67, 65, 63, 62, 66, 69, 71, 70, 74, 76, 78, 74, 68, 65, 63, 60, 62, 66, 68]"
                        [chartHeight]="'250px'"
                        [xAxisLabel]="'时间'"
                        [yAxisLabel]="'内存'"
                        [unit]="'%'"
                        [showArea]="true"
                        [smooth]="true"
                        [showGrid]="true"
                        [colorConfig]="{
                        waveColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        textColor: '#666',
                        gridColor: '#eee'
                    }">
                </app-wave-chart-template>
            </div>
            <div class="flex_1 m_0 bg-white p_16 horizontal_column_chart">
                <app-wave-chart-template
                        [title]="clusterInfo.name"
                        [chartData]="[22, 25, 30, 35, 38, 36, 35, 28, 25, 23, 26, 27, 30, 33, 32, 36, 39, 41, 40, 44, 46, 48, 44, 38, 35, 33, 30, 32, 26,28]"
                        [chartHeight]="'250px'"
                        [xAxisLabel]="'时间'"
                        [yAxisLabel]="'存储'"
                        [unit]="'%'"
                        [showArea]="true"
                        [smooth]="true"
                        [showGrid]="true"
                        [colorConfig]="{
                        waveColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        textColor: '#666',
                        gridColor: '#eee'
                    }">
                </app-wave-chart-template>
            </div>
        </div>

        <div class="row m_0 mt_12">
            <div class="flex_1 m_0 bg-white p_16 pie_chart">
                <app-wave-chart-template
                        [title]="clusterInfo.name"
                        [chartData]="[22, 25, 30, 35, 38, 36, 35, 28, 25, 23, 26, 27, 30, 33, 32, 36, 39, 41, 40, 44, 46, 48, 44, 38, 35, 33, 30, 32, 26,28]"
                        [chartHeight]="'250px'"
                        [xAxisLabel]="'时间'"
                        [yAxisLabel]="'网络'"
                        [unit]="'%'"
                        [showArea]="true"
                        [smooth]="true"
                        [showGrid]="true"
                        [colorConfig]="{
                            waveColor: '#1890ff',
                            backgroundColor: 'rgba(24, 144, 255, 0.1)',
                            textColor: '#666',
                            gridColor: '#eee'
                        }">
                </app-wave-chart-template>
            </div>
        </div>
    </div>
</div>

<!--修改基本信息弹出层-->
<nz-modal [(nzVisible)]="showChangeBasicWindow" nzTitle="修改集群描述"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnCancel)="showChangeBasicWindow = false"
          (nzOnOk)="submitChangeBasic()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">集群描述：</span>
        <textarea style="width: 300px; height: 120px"
                  nz-input rows="2"
                  maxlength="200"
                  [(ngModel)]="des"
                  placeholder="请输入实例描述(长度200字符以内)">
        </textarea>
    </div>
    </ng-container>
</nz-modal>
