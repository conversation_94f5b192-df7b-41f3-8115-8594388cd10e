import {Component, HostListener, Input, OnChanges, OnDestroy} from '@angular/core';
import { EChartsOption, LineSeriesOption } from 'echarts';
import { ECharts } from 'echarts';
import ResizeObserver from 'resize-observer-polyfill';

export interface ChartColorConfig {
    seriesColors?: string[];      // 系列颜色数组
    axisColor?: string;           // 坐标轴颜色
    textColor?: string;           // 文本颜色
}

@Component({
    selector: 'app-line-chart-template',
    templateUrl: './line-chart-template.component.html',
    styleUrls: ['./line-chart-template.component.less']
})
export class LineChartTemplateComponent implements  OnChanges, OnDestroy{
    private chartInstance: ECharts;
    private resizeObserver: ResizeObserver;
    @Input() title: string;
    @Input() chartData: any[];
    @Input() colorConfig: ChartColorConfig = {};
    @Input() xAxisLabel = '时间';
    @Input() yAxisLabel = '使用率 (%)';
    @Input() chartWidth: string = '400px'; // 默认宽度
    @Input() chartHeight: string = '300px';
    @Input() unit: string = '';

    @HostListener('window:resize')
    onResize() {
        if (this.chartInstance) {
            this.chartInstance.resize();
        }
    }
    onChartInit(ec: ECharts) {
        this.chartInstance = ec;
        this.initResizeObserver();
    }

    // 使用ResizeObserver监听容器变化
    private initResizeObserver() {
        const container = document.querySelector('.chart-container');
        this.resizeObserver = new ResizeObserver(() => {
            this.resizeChart();
        });
        if (container) {
            this.resizeObserver.observe(container);
        }
    }

    ngOnDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
    }

    private resizeChart() {
        if (this.chartInstance) {
            this.chartInstance.resize({
                width: 'auto', // 自动适应容器
                height: typeof this.chartHeight === 'number' ? this.chartHeight : parseInt(String(this.chartHeight), 10)
            });
        }
    }

    chartOption: EChartsOption;

    // 默认颜色配置
    private defaultColors = {
        seriesColors: [
            '#5470C6', '#91CC75', '#FAC858',
            '#EE6666', '#73C0DE', '#3BA272'
        ],
        axisColor: '#666',
        textColor: '#666'
    };

    // 合并后的最终配置
    get mergedColors(): ChartColorConfig {
        return {
            seriesColors: this.colorConfig.seriesColors || this.defaultColors.seriesColors,
            axisColor: this.colorConfig.axisColor || this.defaultColors.axisColor,
            textColor: this.colorConfig.textColor || this.defaultColors.textColor,

        };
    }

    ngOnChanges() {
        this.initChart();
    }

    private getUnifiedTimeAxis(): string[] {
        const allTimes = new Set<string>();
        this.chartData?.forEach(series => {
            series.series.forEach((point: any) => {
                allTimes.add(point.name);
            });
        });
        return Array.from(allTimes).sort(); // 按时间排序
    }

    private initChart() {
        const timeAxis = this.getUnifiedTimeAxis();

        this.chartOption = {
            color: this.mergedColors.seriesColors, // 应用颜色配置
            tooltip: {
                trigger: 'axis',
                backgroundColor: '#FFF', // 背景色改为白色
                borderColor: '#DDD',     // 边框颜色
                borderWidth: 1,
                padding:10,
                textStyle: {
                    color: '#333',        // 文字颜色
                    fontSize: 10
                },
                axisPointer: {
                    type: 'line',        // 十字准星指示器
                },
                extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.1);', // 添加阴影
                formatter: (params: any) => this.tooltipFormatter(params) // 使用自定义格式化
            },
            legend: {
                bottom: 0,                 // 上移10px
                orient: 'horizontal',
                itemGap: 6,                // 缩小项间距
                itemHeight: 12,             // 缩小图例高度
                textStyle: {
                    color: this.mergedColors.textColor,
                    fontSize: 9             // 缩小字体
                }
            },
            grid: {
                top: '8%',
                bottom: '27%',             // 增加底部空间
                left: '3%',
                right: '4%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: timeAxis,
                axisLine: {
                    lineStyle: {
                        color: this.mergedColors.axisColor
                    }
                },
                axisLabel: {
                    color: this.mergedColors.textColor
                },
                // ...其他xAxis配置
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: {
                        color: this.mergedColors.axisColor
                    }
                },
                axisLabel: {
                    color: this.mergedColors.textColor
                },
                // ...其他yAxis配置
            },
            series: this.generateSeries(timeAxis)
            // ...其他配置
        };
    }

    private tooltipFormatter(params: any): string {
        const time = params[0].axisValue;
        let html = `<div style="font-weight: 500;">${time}</div>`;
        params.forEach((item: any) => {
            html += `
      <div style="display: flex; align-items: center; margin-top: 4px;">
        <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${item.color};margin-right:8px"></span>
        ${item.seriesName}: 
        <span style="font-weight:600;margin-left:4px">
          ${item.value}${this.unit}
        </span>
      </div>
    `;
        });

        return html;
    }
    private generateSeries(timeAxis: string[]) {
        return this.chartData?.map(item => {
            // 创建时间-值的映射
            const valueMap = new Map<string, number>();
            item.series.forEach((point: any) => {
                valueMap.set(point.name, point.value);
            });
            // 对齐到统一时间轴
            return {
                name: item.name,
                type: 'line',
                smooth: true,
                data: timeAxis.map(time => valueMap.get(time) ?? 0) // 将 null 替换为 0
            } as LineSeriesOption;
        }) || [];
    }

    private getTimeAxis() {
        return this.chartData?.[0]?.series?.map((point: any) => point.name) || [];
    }
}