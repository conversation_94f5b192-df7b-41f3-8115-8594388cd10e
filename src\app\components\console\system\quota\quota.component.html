<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <div class="right-button-group">
                <div class="pull-right">
                    <a nz-button
                       [ngClass]="{'disabled': resLimit}"
                       (click)="refresh()">刷&nbsp;&nbsp;新
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    资源总览
                </span>
            </div>
            <nz-table #tableList [nzLoading]="isLoading"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <th>资源类型</th>
                        <th>已使用</th>
                        <th>已分配</th>
                        <th>总配额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.resourceTypeText}}</td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-red': data.usedQuota/data.assignedQuota >= 1, 'dot-yellow': data.usedQuota/data.assignedQuota >= 0.8 && data.usedQuota/data.assignedQuota < 1, 'dot-green': data.usedQuota || data.usedQuota === 0}">
                                {{data.usedQuota}}
                            </span>
                        </td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-red': data.assignedQuota/data.quota >= 1, 'dot-yellow': data.assignedQuota/data.quota >= 0.8 && data.assignedQuota/data.quota < 1, 'dot-green': data.assignedQuota || data.assignedQuota === 0}">
                                {{data.assignedQuota}}
                            </span>
                        </td>
                        <td>{{data.quota}}</td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item"
                                     [routerLink]="['../app-system-quota', data.id]"
                                     [ngClass]="{'disabled': !canGoToDetail(data)}">
                                    <i nzTitle="查看详情"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-search"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[data.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
<!--                                    {{ getBusyText(data) }}-->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>
