import { Component, OnInit, Input, OnChanges, SimpleChanges, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import * as echarts from 'echarts';

@Component({
  selector: 'app-ring-chart-template',
  templateUrl: './ring-chart-template.component.html',
  styleUrls: ['./ring-chart-template.component.less']
})
export class RingChartTemplateComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() height: number = 300; // 默认高度
  @Input() title: string = ''; // 图表标题
  @Input() data: any[] = []; // 数据数组

  @ViewChild('chartContainer', { static: true }) chartContainer: ElementRef;
  chart: any;
  totalValue: number = 0;

  constructor() { }

  ngOnInit(): void {
    // 初始化其他非DOM相关的内容
    // console.log('ngOnInit called');
  }

  ngAfterViewInit(): void {
    // 在视图初始化后初始化图表
    // console.log('ngAfterViewInit called');
    setTimeout(() => {
      this.initChart();
    }, 100);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ((changes.data && !changes.data.firstChange) ||
        (changes.height && !changes.height.firstChange)) {
      this.initChart();
    }
  }

  initChart(): void {
    // console.log('initChart called, data:', this.data);

    if (!this.data || this.data.length === 0) {
      // console.warn('No data available for chart');
      return;
    }

    // 计算总值用于百分比
    this.totalValue = this.data.reduce((sum, item) => sum + item.value, 0);
    // console.log('Total value:', this.totalValue);

    // 如果图表已经存在，销毁它
    if (this.chart) {
      // console.log('Disposing existing chart');
      this.chart.dispose();
    }

    // 检查DOM元素
    if (!this.chartContainer || !this.chartContainer.nativeElement) {
      // console.error('Chart container not found');
      return;
    }

    // console.log('Chart container:', this.chartContainer.nativeElement);
    // console.log('Container dimensions:', this.chartContainer.nativeElement.offsetWidth, 'x', this.chartContainer.nativeElement.offsetHeight);

    try {
      // 初始化图表
      this.chart = echarts.init(this.chartContainer.nativeElement);

      // 准备数据
      let seriesData;

      // 检查是否所有值都为0
      const allZero = this.totalValue === 0;

      if (allZero) {
        // 如果所有值都为0，使用灰色显示整个环形图
        // console.log('All values are zero, showing disabled state');
        seriesData = [{
          value: 1,
          name: '禁用',
          itemStyle: {
            color: '#CCCCCC'
          }
        }];
      } else {
        // 正常数据
        seriesData = this.data.map(item => ({
          value: item.value,
          name: item.key,
          itemStyle: {
            color: item.color
          }
        }));
      }

      // console.log('Series data:', seriesData);

      // 设置图表选项
      const option = {
        title: {
          text: this.title,
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: this.title,
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: seriesData
          }
        ]
      };

      // 应用选项
      // console.log('Setting chart option:', option);
      this.chart.setOption(option);
      // console.log('Chart initialized successfully');

      // 响应窗口大小变化
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize();
        }
      });
    } catch (error) {
      console.error('Error initializing chart:', error);
    }
  }
}
