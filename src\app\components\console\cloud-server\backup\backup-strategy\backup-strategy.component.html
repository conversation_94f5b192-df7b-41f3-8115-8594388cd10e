<nz-modal [(nzVisible)]="isVisible"
    [nzTitle]="strategyId ? '备份策略详情' : '创建备份策略'"
    [nzOkText]="strategyId ? '删除' : '创建'"
    [nzOkLoading]="okLoading"
    [nzMaskClosable]="strategyId ? true : false"
    [nzBodyStyle]="{padding: '24px 8px 8px 8px'}"
    [nzWidth]="600"
    (nzAfterOpen)="strategyModalOpened()"
    (nzOnCancel)="handleCancel()" (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <nz-tabset nzType="card" *ngIf="!_readonly">
        <nz-tab nzTitle="立即备份"
            (nzClick)="changeBackup()">
        </nz-tab>
        <nz-tab nzTitle="备份策略"
        (nzClick)="changeBackupStrategy()">
        </nz-tab>
    </nz-tabset>
    <div class="form-container">
        <form *ngIf="!_readonly"
            [hidden]="!(currentTab === 'backup')"
            [formGroup]="backup" class="config-content sm">
            <div class="field-group">
                <div class="field-item"
                    [ngClass]="{'required': !_readonly}">
                    <label>
                        <span class="label-text">云服务器</span>
                        <nz-select
                            nzShowSearch
                            formControlName="backupVmUuid"
                            nzPlaceHolder="请选择要备份的云服务器">
                            <nz-option
                                *ngFor="let item of instanceList"
                                [nzValue]="item.spUuid"
                                [nzLabel]="item.name">
                            </nz-option>
                        </nz-select>
                    </label>
                    <div class="small tip">
                        <span class="fa fa-info-circle"></span>
                        创建备份需要一定的时间，可能不会立即出现在列表中，请稍后查看
                    </div>
                    <div *ngIf="isBackupFcInvalid(backup.get('backupVmUuid'))" class="form-hint error">
                        <div *ngIf="backup.get('backupVmUuid').hasError('required')">
                            请选择要备份的云服务器
                        </div>
                    </div>
                    
                </div>
            </div>
        </form>
        <form [hidden]="!(currentTab === 'backupStrategy')"
            [formGroup]="backupStrategy"
            class="config-content sm">
            <div class="field-group">
                <div class="field-item"
                    [ngClass]="{'required': !_readonly}">
                    <label>
                        <span class="label-text">云服务器</span>
                        <nz-select *ngIf="!_readonly"
                            nzShowSearch
                            formControlName="cloudServerUuid"
                            nzPlaceHolder="请选择要备份的云服务器">
                            <nz-option
                                *ngFor="let item of instanceList"
                                [nzValue]="item.spUuid"
                                [nzLabel]="item.name">
                            </nz-option>
                        </nz-select>
                        <span
                            *ngIf="_readonly">{{ cloudServer?.name }}</span>
                    </label>
                    <div *ngIf="!_readonly && isStrategyFcInvalid(backupStrategy.get('cloudServerUuid'))" class="form-hint error">
                        <div *ngIf="backupStrategy.get('cloudServerUuid').hasError('required')">
                            请选择要备份的云服务器
                        </div>
                    </div>
                </div>
                <div class="field-item"
                    [ngClass]="{'required': !_readonly}">
                    <label>
                        <span class="label-text">策略名称</span>
                        <input *ngIf="!_readonly" nz-input
                            maxlength="50"
                            type="text"
                            formControlName="name"
                            placeholder="请输入策略名称">
                        <span
                            *ngIf="_readonly">{{ backupStrategy.value.name }}</span>
                    </label>
                    <div *ngIf="!_readonly" class="small tip label-padding">
                            策略名称必须以字母开头，只能输入英文、数字、中划线
                    </div>
                    <div *ngIf="!_readonly && isStrategyFcInvalid(backupStrategy.get('name'))" class="form-hint error">
                        <div *ngIf="backupStrategy.get('name').hasError('required')">
                            请输入策略名称
                        </div>
                        <div *ngIf="backupStrategy.get('name').hasError('pattern')">
                            策略名称不符合规定格式
                        </div>
                        <div *ngIf="backupStrategy.get('name').hasError('maxlength')">
                            策略名称长度不能超过{{ backupStrategy.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item"
                    [ngClass]="{'required': !_readonly}">
                    <label>
                        <span class="label-text">备份日期</span>
                        <nz-radio-group *ngIf="!_readonly"
                            formControlName="type">
                            <label
                                *ngFor="let period of periodOptions"
                                nz-radio-button
                                [nzValue]="period.value">{{ period.label }}
                            </label>
                        </nz-radio-group>
                        <span *ngIf="_readonly">
                            {{ backupStrategy.value.type == 'date' ? backupStrategy.value.backupDate : getTypeText(backupStrategy.value.type)}}
                        </span>
                    </label>
                    <div class="period-panel label-padding">
                        <div class="month-panel"
                            *ngIf="backupStrategy.value.type === 'month'">
                            <nz-checkbox-group
                                [nzDisabled]="_readonly"
                                [(ngModel)]="datePoints"
                                (ngModelChange)="setDatePoints($event)"
                                [ngModelOptions]="{standalone: true}">
                            </nz-checkbox-group>
                        </div>
                        <div class="week-panel"
                            *ngIf="backupStrategy.value.type === 'week'">
                            <nz-checkbox-group
                                [nzDisabled]="_readonly"
                                [(ngModel)]="weekPoints"
                                (ngModelChange)="setWeekPoints($event)"
                                [ngModelOptions]="{standalone: true}">
                            </nz-checkbox-group>
                        </div>
                        <div class="date-panel"
                            *ngIf="backupStrategy.value.type === 'date'">
                            <nz-date-picker
                                *ngIf="!_readonly"
                                formControlName="backupDate"
                                nzFormat="yyyy-MM-dd"
                                [nzShowToday]="false"
                                [nzDisabledDate]="disabledDate">
                            </nz-date-picker>
                        </div>
                    </div>
                    <div *ngIf="!_readonly && isStrategyFcInvalid(backupStrategy.get('backupDate'))" class="form-hint error">
                        <div *ngIf="backupStrategy.get('backupDate').hasError('required')">
                            请选择备份日期
                        </div>
                    </div>
                    <div *ngIf="!_readonly && isStrategyFcInvalid(backupStrategy.get('backupDay'))" class="form-hint error">
                        <div *ngIf="backupStrategy.get('backupDay').hasError('required')">
                            请选择备份日期
                        </div>
                    </div>
                </div>
                <div class="field-item"
                    [ngClass]="{'required': !_readonly}">
                    <label>
                        <span class="label-text">创建时间</span>
                    </label>
                    <div class="label-padding time-points">
                        <nz-checkbox-group
                            [nzDisabled]="_readonly"
                            [(ngModel)]="timePoints"
                            (ngModelChange)="setTimePoints($event)"
                            [ngModelOptions]="{standalone: true}">
                        </nz-checkbox-group>
                    </div>
                    <div *ngIf="!_readonly && isStrategyFcInvalid(backupStrategy.get('backupTime'))" class="form-hint error">
                        <div *ngIf="backupStrategy.get('backupTime').hasError('required')">
                            请选择备份创建时间
                        </div>
                    </div>
                </div>
                <div class="field-item"
                    [ngClass]="{'required': !_readonly}">
                    <label>
                        <span class="label-text">保留个数</span>
                        <input *ngIf="!_readonly"
                            type="text"
                            formControlName="backupAmount"
                            placeholder="设定备份上限，超出将被自动清除">
                        <span
                            *ngIf="_readonly">{{ backupStrategy.value.backupAmount }}</span>
                    </label>
                    <div *ngIf="!_readonly && isStrategyFcInvalid(backupStrategy.get('backupAmount'))" class="form-hint error">
                        <div *ngIf="backupStrategy.get('backupAmount').hasError('required')">
                            请输入备份上限个数
                        </div>
                        <div *ngIf="backupStrategy.get('backupAmount').hasError('pattern')">
                            请输入正确的数字
                        </div>
                        <div *ngIf="backupStrategy.get('backupAmount').hasError('min')">
                            备份上限不能低于{{ backupStrategy.get('backupAmount').errors.min.min }}个
                        </div>
                        <div *ngIf="backupStrategy.get('backupAmount').hasError('max')">
                            备份上限不能超过{{ backupStrategy.get('backupAmount').errors.max.max }}个
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    </ng-container>
</nz-modal>