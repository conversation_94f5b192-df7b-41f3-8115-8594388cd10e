import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OfficeAgentRoutingModule } from './office-agent-routing.module';
import { OfficeAiAgentComponent} from 'src/app/components/console/office-agent/office-ai-agent.component';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';

@NgModule({
    declarations: [OfficeAiAgentComponent],
    imports: [CommonModule, SharedModule, OfficeAgentRoutingModule]
})
export class OfficeAgentModule {
    constructor(moduleRef: NgModuleRef<OfficeAgentModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
