<div class="detail-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="/console/redis">Redis数据库</a></li>-->
<!--        <li><span>实例详情</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <!-- <div class="on-panel-header">
            <h3 class="title">实例详情</h3>
        </div> -->
        <div class="on-panel-body">
            <div class="instance">
                <div class="instance-name">
                    {{ redis.name }}
                </div>
                <ul class="instance-info" *ngIf="redis.name">
                    <li>
                        <span class="dot {{ getRedisStatusClass(redis) }}">
                            {{ redis.redisStatus }}
                        </span>
                    </li>
                    <li *ngIf="redis.memoryGb">内存：{{ redis.memoryGb }}GB</li>
                    <li>{{ redis.redisType === 'cluster' ? '集群' : '标准'}}</li>
                    <li *ngIf="redis.urlList.length">
                        <span *ngIf="redis.urlList.length === 1">{{ redis.urlList[0] }}</span>
                        <a *ngIf="redis.urlList.length > 1" nz-popover nzPlacement="bottom" [nzContent]="urlListTemp">查看集群地址</a>
                        <ng-template #urlListTemp>
                            <ul class="cluster-url-list">
                                <li *ngFor="let urlItem of redis.urlList">
                                    {{ urlItem.url }}
                                    (<span [ngClass]="{master: urlItem.type === 'master', slave: urlItem.type === 'slave'}">
                                        {{ urlItem.type === 'master' ? '主' : '从' }}
                                    </span>)
                                </li>
                            </ul>
                        </ng-template>
                    </li>
                   <!-- <li>ID：{{ redis.instanceId }}</li>-->
                </ul>
            </div>

            <nz-tabset
                [nzAnimated]="{inkBar: true, tabPane: false}">
                <nz-tab nzTitle="基本信息">
                    <div class="on-desc">
                        <div class="on-desc-header">
                            基本信息
                        </div>
                        <div class="on-desc-body">
                            <div nz-row class="on-desc-row">
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        名称</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.name }}</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        云弹性缓存引擎</div>
                                    <div
                                        class="on-desc-content">
                                        Redis</div>
                                </div>
                               <!-- <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        ID</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.instanceId }}
                                    </div>
                                </div>-->
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        状态</div>
                                    <div
                                        class="on-desc-content">
                                        <span class="dot {{ getRedisStatusClass(redis) }}">
                                            {{ redis.redisStatus }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div nz-row>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        专有网络</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.ovdcNetworkName }}</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        子网</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.subnetName }}</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        创建时间</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.createTm | date: 'yyyy-MM-dd HH:mm:ss' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="on-desc">
                        <div class="on-desc-header">
                            配置信息
                        </div>
                        <div class="on-desc-body">
                            <div nz-row class="on-desc-row">
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        CPU</div>
                                    <div
                                        class="on-desc-content">
                                        2核</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        内存规格</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.memoryGb }}GB</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        磁盘规格</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.diskGb }}GB</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        最大内网带宽</div>
                                    <div
                                        class="on-desc-content">
                                        10Gbps</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="on-desc">
                        <div class="on-desc-header">
                            连接信息
                        </div>
                        <div class="on-desc-body">
                            <div nz-row class="on-desc-row">
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        免密访问</div>
                                    <div
                                        class="on-desc-content">
                                        否</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        内网IP</div>
                                    <div
                                        class="on-desc-content">
                                        {{ redis.bootStrapIps }}</div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                            class="on-desc-title">
                                        端口号</div>
                                    <div
                                            class="on-desc-content">
                                        <span>7000</span>
                                    </div>
                                </div>
                                <!--<div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        端口号</div>
                                    <div
                                        class="on-desc-content">
                                        <span>{{ redis.masterPort}}</span>
                                        <span *ngIf="redis.redisType === 'cluster'"> (主)，{{ redis.slavePort }} (从)</span>
                                    </div>
                                </div>
                                <div nz-col nzSpan="6">
                                    <div
                                        class="on-desc-title">
                                        最大连接数</div>
                                    <div
                                        class="on-desc-content">
                                        10000</div>
                                </div>-->
                            </div>
                        </div>
                    </div>
                </nz-tab>
                <!-- <nz-tab nzTitle="用量"
                    (nzSelect)="getRedisMetrics()"
                    (nzDeselect)="destroyChart()">
                    <ng-template nz-tab>
                        <nz-spin [nzSpinning]="isGettingChart" [nzDelay]="300">
                            <div class="action-bar">
                                <nz-select [(ngModel)]="checkTime"
                                    (ngModelChange)="checkTimeChange($event)"
                                    style="width: 200px;"
                                    nzPlaceHolder="请选择查询间隔时间">
                                    <nz-option
                                        *ngFor="let item of checkTimeList"
                                        [nzValue]="item.value"
                                        [nzLabel]="item.label">
                                    </nz-option>
                                </nz-select>
                                <span class="auto-refresh">
                                    自动刷新：
                                    <nz-switch [(ngModel)]="autoRefresh"
                                        (ngModelChange)="setAutoRefresh($event)">
                                    </nz-switch>
                                </span>
                                <div class="pull-right">
                                    <button nz-button
                                        nzType="primary"
                                        (click)="refreshChart();">
                                        <i nz-icon nzType="reload"
                                            nzTheme="outline"></i>
                                        刷新
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <h5 class="section-title">
                                    用量数据总览
                                </h5>
                                <div class="chart-list">
                                    <div class="chart-item-container"
                                        *ngFor="let item of dosageCharts"
                                        (click)="showChart(item)">
                                        <div class="chart-item" [ngStyle]="{'border-color': item.color || '#117dbb'}">
                                            <div echarts
                                                style="width: 230px;height: 150px;"
                                                [options]="item.option"
                                                (chartInit)="onChartInit($event)"
                                                class="on-chart chart-sm">
                                            </div>
                                        </div>
                                        <div class="chart-title"> -->
                                            <!-- <span class="on-badge" [ngStyle]="{'background-color': item.color || '#117dbb'}"></span> -->
                                            <!-- {{ item.title }}
                                        </div>
                                    </div>
                                </div>
                            </div> -->
                            <!-- <div class="chart-container">
                                <h5 class="section-title">
                                    运行用量数据
                                </h5>
                                <div class="chart-list">
                                    <div class="chart-item-container"
                                        *ngFor="let item of runCharts"
                                        (click)="showChart(item)">
                                        <div class="chart-item" [ngStyle]="{'border-color': item.color || '#117dbb'}">
                                            <div echarts
                                                style="width: 160px;height: 100px;"
                                                [options]="item.option"
                                                (chartInit)="onChartInit($event)"
                                                class="on-chart chart-sm">
                                            </div>
                                        </div>
                                        <div class="chart-title">
                                            {{ item.title }}
                                        </div>
                                    </div>
                                </div>
                            </div> -->
                        <!-- </nz-spin>
                    </ng-template>
                </nz-tab> -->
                <!-- <nz-tab nzTitle="参数设置"
                    (nzSelect)="getParamsList()">
                    <div class="table-content">
                        <nz-tabset nzTabPosition="top"
                            (nzSelectChange)="showNodeParams($event)"
                            nzType="card"
                            *ngIf="redis.redisType === 'cluster'">
                            <nz-tab *ngFor="let node of nodeList" [nzTitle]="node"></nz-tab>
                        </nz-tabset>
                        <nz-table #params nzSize="middle"
                            [nzShowPagination]="false"
                            [nzData]="paramsList">
                            <thead>
                                <tr>
                                    <th nzWidth="25%">参数</th>
                                    <th nzWidth="15%">参数默认值</th>
                                    <th nzWidth="15%">当前值</th>
                                    <th nzWidth="25%">参数范围</th>
                                    <th nzWidth="20%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    *ngFor="let item of params.data; trackBy: trackByName">
                                    <td>
                                        {{ item.name }}
                                        <i  class="tip"
                                            nz-icon
                                            nzType="question-circle"
                                            nzTheme="outline"
                                            nz-tooltip
                                            [nzTitle]="getParamDesc(item)"
                                            nzPlacement="top"></i>
                                    </td>
                                    <td>
                                        {{ item.defaultValue }}
                                    </td>
                                    <td>
                                        <ng-container
                                            *ngIf="!item.edit; else paramInputTpl">
                                            {{ item.now }}
                                        </ng-container>
                                        <ng-template
                                            #paramInputTpl>
                                            <nz-input-number
                                                style="width: 160px"
                                                *ngIf="item.valueType === 'int'"
                                                [nzMin]="item.range[0]"
                                                [nzMax]="item.range[1]" [nzStep]="1"
                                                [nzPrecision]="0"
                                                [nzDisabled]="!!busyStatus[item.name]"
                                                [(ngModel)]="currentParam[item.name]">
                                            </nz-input-number>
                                            <nz-select style="width: 160px" *ngIf="item.valueType === 'list'"
                                                [nzDisabled]="!!busyStatus[item.name]"
                                                [(ngModel)]="currentParam[item.name]" nzPlaceHolder="请选择参数值">
                                                <nz-option
                                                    *ngFor="let value of item.range"
                                                    [nzValue]="value" [nzLabel]="value"></nz-option>
                                            </nz-select>
                                        </ng-template>
                                    </td>
                                    <td>
                                        <span class="range-list" *ngIf="item.valueType === 'list'">
                                            {{ item.range.join(', ') }}
                                        </span>
                                        <span class="range" *ngIf="item.valueType === 'int'">
                                            {{ item.range.join(' ~ ') }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="on-table-actions"
                                            [hidden]="busyStatus[item.name]">
                                            <div class="on-table-action-item"
                                                *ngIf="!item.edit"
                                                (click)="editParam(item)">
                                                <i nzTitle="编辑"
                                                    nzPlacement="bottom"
                                                    nz-tooltip
                                                    class="icon fa fa-edit"></i>
                                            </div>
                                            <div class="on-table-action-item"
                                                *ngIf="!item.edit"
                                                (click)="restoreDefault(item)">
                                                <i nzTitle="恢复默认值"
                                                    nzPlacement="bottom"
                                                    nz-tooltip
                                                    class="icon fa fa-paste"></i>
                                            </div>
                                            <div class="on-table-action-item"
                                                *ngIf="item.edit"
                                                (click)="saveParam(item)">
                                                <i nzTitle="保存"
                                                    nzPlacement="bottom"
                                                    nz-tooltip
                                                    class="icon fa fa-save"></i>
                                            </div>
                                            <div class="on-table-action-item"
                                                *ngIf="item.edit"
                                                (click)="item.edit = false">
                                                <i nzTitle="取消"
                                                    nzPlacement="bottom"
                                                    nz-tooltip
                                                    class="icon fa fa-undo"></i>
                                            </div>
                                        </div>
                                        <div class="on-table-actions"
                                            [hidden]="!busyStatus[item.name]">
                                            <div
                                                class="action-loading-placeholder">
                                                <i class="icon" nz-icon
                                                    [nzType]="'loading'"></i>
                                                {{ getBusyText(item) }}
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </nz-table>
                    </div>
                </nz-tab> -->
                <!-- <nz-tab nzTitle="调试">
                    <div class="debug-container">
                        <nz-radio-group [(ngModel)]="debugType"
                            (ngModelChange)="debugTypeChange($event)">
                            <label nz-radio
                                nzValue="get">Get</label>
                            <label nz-radio
                                nzValue="set">Set</label>
                        </nz-radio-group>
                        <form nz-form nzLayout="inline"
                            class="debug-form"
                            [formGroup]="debug"
                            (ngSubmit)="debugType === 'get' ? debugGet() : debugSet()">
                            <nz-form-item>
                                <nz-form-label>Key</nz-form-label>
                                <nz-form-control>
                                    <nz-input-group>
                                        <input
                                            style="width: 240px"
                                            formControlName="key"
                                            nz-input
                                            maxlength="100"
                                            placeholder="请输入Key值" />
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                            <nz-form-item *ngIf="debugType === 'set'">
                                <nz-form-label>Value</nz-form-label>
                                <nz-form-control>
                                    <nz-input-group>
                                        <input
                                            style="width: 240px"
                                            formControlName="value"
                                            nz-input
                                            maxlength="500"
                                            placeholder="请输入要设置的Value值" />
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                            <nz-form-item>
                                <nz-form-control>
                                    <button nz-button
                                        type="submit"
                                        *ngIf="debugType === 'set'"
                                        nzType="primary">设置</button>
                                    <button nz-button
                                        type="submit"
                                        *ngIf="debugType === 'get'"
                                        nzType="primary">查询</button>
                                </nz-form-control>
                            </nz-form-item>
                        </form>
                        <div class="form-hint error" *ngIf="isInvalid(debug.get('key'))">
                            <div *ngIf="debug.get('key').hasError('required')">
                                Key不能为空
                            </div>
                            <div *ngIf="debug.get('key').hasError('maxlength')">
                                Key长度不能超过{{ debug.get('key').errors.maxlength.requiredLength }}个字符
                            </div>
                        </div>
                        <div class="form-hint error" *ngIf="!isInvalid(debug.get('key')) && isInvalid(debug.get('value'))">
                            <div *ngIf="debug.get('value').hasError('required')">
                                Value不能为空
                            </div>
                            <div *ngIf="debug.get('value').hasError('maxlength')">
                                Value长度不能超过{{ debug.get('value').errors.maxlength.requiredLength }}个字符
                            </div>
                        </div>
                        <div class="output-preview">
                            <div class="output-title">结果预览</div>
                            <div class="output-content">
                                {{ output }}
                            </div>
                        </div>
                    </div>
                </nz-tab> -->
            </nz-tabset>
        </div>
    </div>
</div>

<!-- 图表 -->
<nz-modal [nzWidth]="800" [(nzVisible)]="chartModalVisible"
    [nzTitle]="currentChart.title || '用量详情'" [nzFooter]="null"
    (nzOnCancel)="chartModalVisible = false"
    (nzOnOk)="chartModalVisible = false">
    <ng-container *nzModalContent>
    <div class="redis-dosage">
        <div class="action-bar clearfix">
            <div class="pull-right">
                <button nz-button nzType="primary"
                    (click)="refreshChartDetail();">
                    <i nz-icon nzType="reload"
                        nzTheme="outline"></i>
                    刷新
                </button>
            </div>
        </div>
        <nz-spin [nzSpinning]="isGettingChartDetail" [nzDelay]="300">
            <div
                echarts
                [options]="currentChart.option"
                class="detail-chart">
            </div>
        </nz-spin>
    </div>
    </ng-container>
</nz-modal>