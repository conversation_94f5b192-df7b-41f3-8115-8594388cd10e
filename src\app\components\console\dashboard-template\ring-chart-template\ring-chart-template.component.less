.ring-chart-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
}

.chart-title {
  text-align: center;
  font-size: 16px;
  margin-bottom: 10px;
}

.chart-content {
  display: flex;
  flex: 1;
  align-items: center;
  height: 100%;
}

.chart-area {
  flex: 2;
  height: 100%;
  min-height: 150px;
}

.legend-area {
  //width: 120px;
  padding-left: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex: 1;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.legend-text {
  display: flex;
  flex-direction: column;
}

.legend-key {
  font-size: 14px;
}

.legend-value {
  font-size: 14px;
  color: #1890ff;
}

.legend-percentage {
  font-size: 12px;
  color: #666;
}

.disabled-text {
  opacity: 0.5;
}

.disabled-legend {
  margin-bottom: 16px;
  .legend-key {
    color: #999;
  }
}
