import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { UsageRecordComponent} from 'src/app/components/console/cost/record/usage-record.component';
import { CostCenterComponent} from 'src/app/components/console/cost/cost-center/cost-center.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'cost-center',
        pathMatch: 'full',
    },
    {
        path: 'cost-center',
        component: CostCenterComponent,
    },
    {
        path: 'usage-record',
        component: UsageRecordComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CostRoutingModule {}
