import { Component, Input, OnInit, OnChanges, SimpleChanges, ElementRef, ChangeDetectorRef, OnDestroy } from '@angular/core';
// 1. 导入 ECharts 核心模块、图表和组件
import * as echarts from 'echarts/core';
import { Map<PERSON>hart, ScatterChart } from 'echarts/charts';
import {
    TitleComponent,
    TooltipComponent,
    GeoComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
// import chinaJson from 'echarts/map/json/china.js'; // 旧的导入方式
// import chinaJson from '../../../assets/china.json'; // 上次尝试的路径
const chinaJson = require('@assets/map/china.json'); // 尝试使用 require
import ResizeObserver from "resize-observer-polyfill";

// 2. 注册所需的模块和地图
echarts.use([
    TitleComponent,
    TooltipComponent,
    GeoComponent,
    MapChart, // 注册地图图表
    ScatterChart, // 注册散点图图表
    CanvasRenderer
]);
// 使用类型断言 (as any) 解决类型不匹配问题
echarts.registerMap('china', chinaJson as any); // 注册中国地图

// Interface for map points (ensure this is defined or imported correctly)
export interface MapPoint {
    name: string;
    value: [number, number]; // [longitude, latitude]
    details?: any;
}

// Type alias for ECharts option - Can be simplified or kept depending on complexity needed
// We'll keep it for now, but ensure types align with imported modules
import { TitleComponentOption, TooltipComponentOption, GeoComponentOption, ScatterSeriesOption } from 'echarts';
type ECOption = echarts.ComposeOption<
    | TitleComponentOption
    | TooltipComponentOption
    | GeoComponentOption
    | ScatterSeriesOption // Add ScatterSeriesOption if needed for scatter points
    // Add MapSeriesOption if you were using a map series directly, though scatter on geo is common
>;

@Component({
    selector: 'app-map-chart-template',
    templateUrl: './map-chart-template.component.html',
    styleUrls: ['./map-chart-template.component.less']
})
export class MapChartTemplateComponent implements OnInit, OnChanges, OnDestroy {

    @Input() title: string = 'World Map';
    @Input() chartHeight: number = 300;
    @Input() mapPoints: MapPoint[] = [];

    options: ECOption;
    private chartInstance: echarts.ECharts | null = null; // Initialize as null
    private resizeObserver: ResizeObserver | null = null;

    constructor(private elRef: ElementRef<HTMLElement>, private cdr: ChangeDetectorRef) { }

    ngOnInit(): void {
        // Use setTimeout to ensure the container element is ready after initial render
        setTimeout(() => {
            // No need to check worldJson anymore, map is registered above
            this.initChart();
            this.setupResizeObserver();
        }, 0);
    }

    ngOnChanges(changes: SimpleChanges): void {
        // Update chart if data or height changes and chart instance exists
        if (this.chartInstance && (changes.mapPoints || changes.chartHeight)) {
            this.updateChart();
        }
    }

    ngOnDestroy(): void {
        // Clean up chart instance and observer
        if (this.chartInstance) {
            this.chartInstance.dispose();
            this.chartInstance = null;
        }
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
    }

    private setupResizeObserver(): void {
        const chartContainer = this.elRef.nativeElement.querySelector<HTMLElement>('.chart-container');
        if (chartContainer) {
            this.resizeObserver = new ResizeObserver(() => {
                if (this.chartInstance) {
                    this.chartInstance.resize();
                }
            });
            this.resizeObserver.observe(chartContainer);
        }
    }

    private initChart(): void {
        // Explicitly query for HTMLDivElement
        const chartDom = this.elRef.nativeElement.querySelector<HTMLDivElement>('.chart-container');
        // Only initialize if the DOM element exists and chartInstance hasn't been created
        if (chartDom && !this.chartInstance) {
            // Pass the HTMLDivElement to echarts.init
            this.chartInstance = echarts.init(chartDom);
            this.setChartOption();
            this.chartInstance.setOption(this.options as any);
        }
    }

    private updateChart(): void {
        if (!this.chartInstance) return; // Guard against calling on null instance
        this.setChartOption(); // Re-calculate options based on new data
        this.chartInstance.setOption(this.options as any, true); // Use true to not merge options
    }

    private setChartOption(): void {
        this.options = {
            title: {
                text: this.title,
                left: 'center',
                textStyle: {
                    color: '#333',
                    fontSize: 16
                }
            },
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(50,50,50,0.9)', // Dark background with transparency
                borderColor: '#555',
                borderWidth: 1,
                padding: [10, 15],
                textStyle: {
                    color: '#fff' // White text
                },
                extraCssText: 'box-shadow: 0 3px 10px rgba(0,0,0,0.3); border-radius: 6px;', // Add shadow and border-radius
                // --- End Tooltip Style Improvements ---
                formatter: (params: any) => {
                    // Only show tooltip for scatter points (not map regions)
                    if (params.seriesType === 'scatter') {
                        const data = params.data;
                        if (!data) return null;
                        
                        // Start with name in bold
                        let tooltipContent = `<div style="font-weight: bold; font-size: 14px; margin-bottom: 5px;">${data.name || 'Unknown'}</div>`;
                        
                        // Add coordinates with lighter color
                        if (data.value && Array.isArray(data.value) && data.value.length >= 2) {
                            tooltipContent += `<div style="font-size: 12px; color: #ddd; margin-bottom: 8px;">`;
                            tooltipContent += `Longitude: ${data.value[0].toFixed(2)}, Latitude: ${data.value[1].toFixed(2)}`;
                            tooltipContent += `</div>`;
                        }
                        
                        // Add details if available
                        if (data.details) {
                            if (this.isObject(data.details)) {
                                tooltipContent += '<div style="font-size: 11px; color: #ddd; white-space: pre-wrap; max-width: 250px; word-wrap: break-word;">';
                                for (const key in data.details) {
                                    // Ensure it's an own property and not null/undefined
                                    if (Object.prototype.hasOwnProperty.call(data.details, key) && data.details[key] != null) {
                                        tooltipContent += `<div><span style="color: #bbb;">${key}:</span> ${data.details[key]}</div>`;
                                    }
                                }
                                tooltipContent += '</div>';
                            } else {
                                tooltipContent += `<div style="font-size: 11px; color: #ddd; white-space: pre-wrap; max-width: 250px; word-wrap: break-word;">${data.details}</div>`; // Style details
                            }
                        }
                        return tooltipContent;
                    }
                    // No tooltip for map regions or other series types
                    return null;
                } // End of formatter function
            }, // End of tooltip object

            geo: {
                map: 'china', // 3. 使用 'china' 地图
                roam: false, // Roaming disabled as requested
                zoom: 1.2,   // Adjusted zoom level for China map
                center: [104, 35], // Centered on China
                emphasis: {
                    // Force emphasis style to match normal state to prevent highlight
                    itemStyle: {
                        areaColor: '#f3f3f3', // Slightly different background for map area
                        borderColor: '#ccc'      // Same as normal itemStyle
                    },
                    label: {
                        show: false             // Same as normal label
                    }
                },
                select: {
                    disabled: true // Disable selection
                },
                itemStyle: { // Normal style
                    areaColor: '#f3f3f3', // Normal map area color
                    borderColor: '#ccc'
                },
                label: { // Normal label
                    show: false
                }
            }, // End of geo object

            series: [
                // Only the Scatter series for points
                {
                    name: 'Points',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    data: this.mapPoints, // Use data passed via @Input
                    symbolSize: 8,
                    itemStyle: { // Normal style for points
                        color: '#155EEF'
                    },
                    emphasis: { // Emphasis style for points (keep consistent)
                        itemStyle: {
                            color: '#155EEF' // Same color
                        },
                        label: {
                            show: false
                        }
                    }
                } // End of scatter series
            ] // End of series array
        }; // End of this.options assignment
    } // End of setChartOption method

    // --- Helper method for template type checking ---
    isObject(value: any): boolean {
        return typeof value === 'object' && value !== null && !Array.isArray(value);
    }
    // --- End helper method ---

    // --- Tooltip联动方法 ---
    showMapTooltip(dataIndex: number): void {
        if (this.chartInstance) {
            this.chartInstance.dispatchAction({
                type: 'showTip',
                seriesIndex: 0, // Assuming scatter series is the first (and only) series now
                dataIndex: dataIndex
            });
        }
    }

    hideMapTooltip(): void {
        if (this.chartInstance) {
            this.chartInstance.dispatchAction({
                type: 'hideTip'
            });
        }
    }
    // --- End Tooltip联动方法 ---

} // End of component class