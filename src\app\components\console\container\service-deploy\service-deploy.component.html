<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword" autocomplete="off" [(ngModel)]="keyword" nz-input placeholder="请输入服务名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch><i nz-icon nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <button nz-button nzType="primary" (click)="toServiceConfig()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新增服务部署
                    </button>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">服务部署</span>
            </div>
            
            <nz-select style="width: 200px; margin-bottom: 20px" [(ngModel)]="serviceDeploy"
                       (ngModelChange)="getServiceDeploy(serviceDeploy)">
                <nz-option *ngFor="let option of namespaceList" [nzValue]="option.name"
                           [nzLabel]="option.name">
                </nz-option>
            </nz-select>
            
            <nz-table #serviceDeployListData
                      style="overflow-x: auto"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzShowPagination]="false"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="serviceDeployList">
                <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)">
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width">
                            {{ col.title }}
                        </th>
                    </ng-container>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of serviceDeployListData.data; trackBy: trackById">
                    <td width="20%">{{ item.deploymentName || '无' }}</td>
                    <td>{{ item.clusterIp || '无' }}</td>
                    <td [ngClass]="{'warning-text': item.readyNum !== item.replicasNum}">{{ item.readyNum }} / {{ item.replicasNum }}</td>
                    <td>{{ item.serviceType == 'ClusterIP' ? '仅在集群内访问' : 'VPC内网访问' }}</td>
                    <td width="20%">{{ analysisLabels(item.labels) }}</td>
                    <td>{{ getServiceTime(item.createTime) }}</td>
                    <td>
                        <div class="on-table-actions" *ngIf="permission('view')"
                             [hidden]="busyStatus[item.deploymentName]">
                            <div class="on-table-action-item"
                                 (click)="getServicePod(item)">
                                <i nzTooltipTitle="查看容器"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   nz-icon
                                   nzType="code-sandbox"
                                   nzTheme="outline"></i>
                            </div>
                            <div class="on-table-action-item" *ngIf="permission('view')"
                                 (click)="showPort(item)">
                                <i nzTooltipTitle="端口"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   nz-icon
                                   nzType="apartment"
                                   nzTheme="outline"></i>
                            </div>
                            <div class="on-table-action-item" *ngIf="permission('delete')"
                                 nz-popconfirm
                                 nzTooltipContent="top"
                                 [nzCondition]="!serviceCanDelete()"
                                 nzTitle="确定要删除该服务吗？"
                                 (nzOnConfirm)="deleteService(item)"
                                 [ngClass]="{'disabled': !serviceCanDelete()}">
                                <i nzTooltipTitle="删除"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   nz-icon nzType="delete"
                                   nzTheme="outline"></i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                             [hidden]="!busyStatus[item.deploymentName]">
                            <div class="action-loading-placeholder">
                                <i class="icon" nz-icon [nzType]="'loading'"></i>
                                {{ getBusyText(item.deploymentName) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="serviceDeployListData.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 查看端口弹窗 -->
<nz-modal [(nzVisible)]="ifShowPort"
          [nzTitle]="showPortTitle + ' 端口'"
          (nzOnCancel)="ifShowPort = false"
          [nzFooter]="null">
    <ng-container *nzModalContent>
    <nz-table #portTable
              [nzBordered]=true
              [nzPageSize]=99999
              [nzShowPagination]=false
              [nzData]="portTableLIst">
        <thead>
        <tr>
            <th>端口名称</th>
            <th>协议</th>
            <th>端口</th>
            <th>目标端口</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of portTable.data">
            <td>{{ item.name }}</td>
            <td>{{ item.protocol }}</td>
            <td>{{ item.port }}</td>
            <td>{{ item.targetPort }}</td>
        </tr>
        </tbody>
    </nz-table>
    </ng-container>
</nz-modal>

<!-- 查看Pod弹窗 -->
<nz-modal [(nzVisible)]="showPod"
          [nzTitle]="'容器列表'"
          (nzOnCancel)="closePod()"
          [nzWidth]="1000"
          [nzFooter]="null">
    <ng-container *nzModalContent>
    <div class="pod-header">
        <button nz-button nzType="primary" (click)="refreshPod()">
            <i nz-icon nzType="reload" nzTheme="outline"></i>
            刷新
        </button>
    </div>
    <nz-table #podTable
              [nzBordered]=true
              [nzPageSize]=99999
              [nzShowPagination]=false
              [nzData]="podListData">
        <thead>
        <tr>
            <th>Pod名称</th>
            <th>状态</th>
            <th>IP</th>
            <th>节点</th>
            <th>创建时间</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of podTable.data">
            <td>{{ item.name }}</td>
            <td>
                <span class="dot {{ getStatusClass(item.status) }}">{{ item.status }}</span>
            </td>
            <td>{{ item.ip }}</td>
            <td>{{ item.nodeName }}</td>
            <td>{{ getServiceTime(item.createTime) }}</td>
        </tr>
        </tbody>
    </nz-table>
    </ng-container>
</nz-modal>
