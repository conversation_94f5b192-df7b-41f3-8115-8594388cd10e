<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><span>云内互联</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">云内互联</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
               <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                    [routerLink]="'../vpc-connect-config'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建云内互联
                     </a>
                </div>
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <a nz-button nzType="primary"
                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                    [routerLink]="isArchiveUser === 'true'? null : '../vpc-connect-config-quota'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建云内互联
                     </a>
                </div>
            </div>
            <nz-table #vpnList style="overflow-x: auto"
            [nzItemRender]="renderItemTemplate"
            [nzLoading]="isLoading"
            [nzLoadingDelay]="300"
            [nzFrontPagination]="false"
            [nzTotal]="pager.total"
            [nzPageIndex]="pager.page"
            [nzPageSize]="pager.pageSize"
            (nzPageIndexChange)="pageChanged($event)"
            (nzQueryParams)="onParamsChange($event)"
            [nzData]="vpnListData">
            <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th
                          *ngIf="col.width"
                          nz-resizable
                          nzBounds="window"
                          nzPreview
                          [nzWidth]="col.width"
                          [nzMaxWidth]="300"
                          [nzMinWidth]="60"
                          [nzShowSort]="col.showSort"
                          [nzSortFn]="col.sortFlag"
                          [nzSortOrder]="col.allowSort"
                          [nzColumnKey]="col.ColumnKey"
                          (nzResizeEnd)="onResize($event, col.title)"
                        >
                          {{ col.title }}
                          <nz-resize-handle nzDirection="right">
                            <div class="resize-trigger"></div>
                          </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width" style="min-width: 120px;">
                          {{ col.title }}
                        </th>
                      </ng-container>
                    <!-- <th width="15%">站点名称</th>
                    <th width="15%">本地端点</th>
                    <th width="15%">本地子网</th>
                    <th width="15%">对等端点</th>
                    <th width="15%">对等子网</th>
                    <th width="">已启用站点</th>
                    <th width="15%">操作</th> -->
                </tr>
            </thead>
            <tbody>
                <tr
                    *ngFor="let item of vpnList.data; trackBy: trackById">
                    <td>{{ item.name }}</td>
                    <td>{{ item.localRegionTitle }}</td>
                    <td>{{ item.localIP }}</td>
                    <td>{{ item.localSubNetwork }}</td>
                    <td>{{ item.otherRegionTitle }}</td>
                    <td>{{ item.otherIP }}</td>
                    <td>{{ item.otherSubNetwork }}</td>
                    <!--<td>{{ item.cansecretVisible ?item.secretKey : "***"}}
                        <i class="suffix" [title]="item.cansecretVisible ? '隐藏密码' : '查看密码'" nz-icon
                        [nzType]="item.cansecretVisible ? 'eye-invisible' : 'eye'"
                        (click)="item.cansecretVisible = !item.cansecretVisible" style="margin-bottom: 5px;"></i></td>-->
                    <td>
                        <div class="on-table-actions"
                            [hidden]="busyStatus[item.id]">
                            <!--<div class="on-table-action-item"
                             (click)="showModal();">
                                <i nzTitle="隧道配置"
                                nzPlacement="bottom"
                                nz-tooltip nz-icon nzType="cluster"
                                nzTheme="outline" class="icon">
                                </i>
                            </div>-->
                            <!-- <div class="on-table-action-item"
                                (click)="showEditModal(item)">
                                <i nzTitle="编辑"
                                    nzPlacement="bottom"
                                    nz-tooltip
                                    class="icon fa fa-edit"></i>
                            </div> -->
                            <div class="on-table-action-item"
                                nz-popconfirm
                                nzPlacement="top"
                                [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                [nzTitle]="isArchiveUser === 'true'? null : '确定要删除该VPC吗？'"
                                (nzOnConfirm)="isArchiveUser === 'true'? null : deletevpn(item);">
                                <i nzTitle="删除"
                                    nzPlacement="bottom"
                                    nz-tooltip
                                    class="icon fa fa-trash-o">
                                </i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                            [hidden]="!busyStatus[item.id]">
                            <div
                                class="action-loading-placeholder">
                                <i class="icon" nz-icon
                                    [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                        <!-- 隧道配置对话框 -->
                        <nz-modal [(nzVisible)]="isVisible" nzTitle="隧道配置" [nzOkText]="null" nzCancelText="返回" (nzOnCancel)="handleCancel()">
                            <ng-container *nzModalContent>
                            <form class="config-content md network-form">
                                <div class="field-group">
                                    <div class="field-item">
                                        <label>
                                            <span class="label-text">IKE版本：</span>
                                            <span class="label-text">IKE-Flex</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="field-group">
                                    <div class="field-item">
                                        <label>
                                            <span class="label-text">摘要算法：</span>
                                            <span class="label-text">SHA_256</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="field-group">
                                    <div class="field-item">
                                        <label>
                                            <span class="label-text">加密算法：</span>
                                            <span class="label-text">AES256</span>
                                            </label>
                                    </div>
                                </div>
                                <div class="field-group">
                                    <div class="field-item">
                                        <label>
                                            <span class="label-text">身份验证：</span>
                                            <span class="label-text">PSK</span>
                                        </label>
                                    </div>
                                </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text">Hellman组：</span>
                                                <span class="label-text">DH15</span>
                                            </label>
                                        </div>
                                    </div>
                            </form>
                            </ng-container>
                        </nz-modal>
                    </td>
                </tr>
                <tr [hidden]="vpnList.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
            </tbody>
        </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
