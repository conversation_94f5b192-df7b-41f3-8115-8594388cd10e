<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">容器服务</a></li>-->
<!--        <li><a routerLink="../">集群管理</a></li>-->
<!--        <li><span>{{ clusterInfo.name }}</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="details-container">
            <!--顶部菜单-->
<!--            <div class="details-header">-->
<!--                <div class="title-container">-->
<!--                    <p>{{ basicInfor[1].value }}</p>-->
<!--                    <span>{{ basicInfor[6].value }}</span>-->
<!--                    <span class="title-des-separate">{{ basicInfor[7].value }}</span>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <span class="operate-btn" (click)="deleteItem('k8s', id)">-->
<!--                        <i class="fa fa-trash-o"></i><span>删除</span>-->
<!--                    </span>-->
<!--                </div>-->
<!--            </div>-->
            <!--内容菜单-->
            <div class="details-menu">
                <nz-tabset [nzTabPosition]="'top'" [nzType]="'card'"
                            (nzSelectChange)="selectMenu($event)"
                            [nzSelectedIndex]="activeContentIndex">
                        <nz-tab *ngFor="let tab of contentMenuOptions"[nzTitle]="tab.title">
                        </nz-tab>
                </nz-tabset>
            </div>
            <!--基本信息-->
            <div class="content-body-item" *ngIf="activeContentIndex === 0">
                <section class="info-container">
                    <p>基本信息</p>
                    <div class="info-details-container">
                        <div class="info-details-item">
                            <p>集群名称</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                <span style="color:#1890ff;">
                                {{ clusterInfo.name }}</span><br>{{clusterInfo.uid}}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>状态</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{ clusterInfo.phase }}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>创建时间</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{ clusterInfo.creationTimestamp }}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>版本</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{ clusterInfo.platformVersion }}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>类型</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{clusterInfo.k8sClusterType }} - {{clusterInfo.k8sClusterSubType}}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>VPC</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{clusterInfo.spVpcName }}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>节点数量</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{clusterInfo.nodeList ? clusterInfo.nodeList.length : '-' }}
                            </p>
                        </div>
                        <div class="info-details-item">
                            <p>子网</p>
                            <!-- <p class="info-text">{{ item.value }}</p> -->
                            <p class="info-text">
                                {{clusterInfo.subnetName}}
                            </p>
                        </div>
                    </div>
                </section>

                <!--主节点配置-->
<!--                <section class="info-container">-->
<!--                    <p>主节点配置</p>-->
<!--                    <div class="info-details-container">-->
<!--                        <span class="info-details-item" *ngFor="let item of basicInforHostNode; index as i">-->
<!--                            <p>{{ item.title }}</p>-->
<!--                            <p class="info-text">{{ item.value }}</p>-->
<!--                        </span>-->
<!--                    </div>-->
<!--                </section>-->
                <!--镜像仓库配置-->
<!--                <section class="info-container">-->
<!--                    <p>镜像仓库配置</p>-->
<!--                    <div class="info-details-container" style="padding-top: 15px">-->
<!--                        <span class="wider-title">绑定的镜像仓库：</span>-->
<!--                        <span class="info-container-label-text" -->
<!--                            (click)="bindMirrorObj.name ?-->
<!--                                (!mirrorRepoLoading && createOrUpdateBindMirrorRepo('change')) :-->
<!--                                (!mirrorRepoLoading && createOrUpdateBindMirrorRepo('create'))">-->
<!--                            {{bindMirrorObj.name || '暂未绑定'}}</span>-->
<!--                        <span class="fa fa-pencil" (click)="createOrUpdateBindMirrorRepo('change')"-->
<!--                            *ngIf="bindMirrorObj.name && !mirrorRepoLoading" -->
<!--                            nz-tooltip nzTitle="更改镜像仓库">-->
<!--                        </span>-->
<!--                        <span class="fa fa-trash-o" (click)="unbindMirrorRepo()"-->
<!--                            *ngIf="bindMirrorObj.name && !mirrorRepoLoading" -->
<!--                            nz-tooltip nzTitle="解绑镜像仓库">-->
<!--                        </span>-->
<!--                        <span class="fa fa-plus" (click)="createOrUpdateBindMirrorRepo('create')"-->
<!--                            *ngIf="!bindMirrorObj.name && !mirrorRepoLoading" -->
<!--                            nz-tooltip nzTitle="绑定镜像仓库">-->
<!--                        </span>-->
<!--                        <span class="oprt-loading" *ngIf="mirrorRepoLoading" nz-icon [nzType]="'loading'" -->
<!--                            nz-tooltip nzTitle="操作进行中...">-->
<!--                        </span>-->
<!--                    </div>-->
<!--                </section>-->
            </div>
            <!--节点池-->
            <div class="content-body-item" *ngIf="activeContentIndex === 1">
                <!--内容头部-->
                <div class="header">
                    <p>节点池
                        <!--                        <small class="warning-text" *ngIf="showResLimit">-->
                        <!--                            <i class="fa fa-exclamation-circle"></i>-->
                        <!--                            每个用户最多可免费创建2个计算节点，如有其它需求请提交工单联系！-->
                        <!--                        </small>-->
                        <!--                        <span class="header-bottom-tips">计算节点初始用户名：root，初始密码：root</span>-->
                    </p>
                    <div class="pull-right" *ngIf="permission('create')">
<!--                        <button nz-button nzType="primary" [ngClass]="{'disabled': resLimit}" (click)="!resLimit && showAddNodeWindow()">-->
                        <button nz-button nzType="primary" (click)="!resLimit && showAddNodeWindow()">
                            <i nz-icon nzType="plus"
                               nzTheme="outline"></i>
                            新增节点池
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #nodeList
                          [nzBordered]=true
                          [nzItemRender]="nodePageTemplate"
                          [nzFrontPagination]="false"
                          [nzTotal]="nodePage.total"
                          [nzPageIndex]="nodePage.current"
                          [nzPageSize]="nodePage.size"
                          (nzPageIndexChange)="nodePageChange($event)"
                          [nzData]="nodePoolListData">
                    <thead>
                    <tr>
                        <th width="400">节点池</th>
                        <th>规格</th>
                        <th>节点数量</th>
                        <th>根卷(GB)</th>
                        <th>数据卷(GB)</th>
                        <th>子网</th>
                        <th>弹性伸缩</th>
                        <th>创建日期</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of nodeList.data">
                        <td align="left" style="padding-left:35px;"><span style="color:#1890ff;">{{ item.name || '-' }}</span><br>{{item.uid }}</td>
                        <td><span>{{item.flavor}}</span></td>
                        <td>{{ item.initialNodeCount }}</td>
                        <td>{{ item.rootVolumeSize }}</td>
                        <td>{{ item.dataVolumeSize }}</td>
                        <td>
<!--                            {{item.subnetName}}<br/>-->
                            {{item.subnetName}}
                        </td>

                        <td align="left" style="padding-left:35px;">
                            <span style="color:{{!item.autoscalingEnable? '#bdb5b5':'#44cc11'}};">{{item.autoscalingEnable ? '开启' : '关闭'}}</span><br>
                            最小/最大数：{{item.autoscalingMinNodeCount}}/{{item.autoscalingMaxNodeCount}}
                        </td>
                        <td>
                            {{item.creationTimestamp}}
                        </td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions" style="float:left" *ngIf="permission('create')"
                                [hidden]="busyStatus[item.name]">
                                <div class="on-table-action-item"
                                    (click)="scaleNode(item);">
                                    <i nzTitle="节点扩缩容"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon-vpn iconfont icon"></i>
                                </div>
                            </div>
<!--                                                                <div class="on-table-action-item"-->
<!--                                                                    nz-popconfirm-->
<!--                                                                    nzTooltipContent="top"-->
<!--                                                                    [nzCondition]="!nodeCanDelete(item)"-->
<!--                                                                    nzTitle="删除节点会导致部署在此节点上的服务关闭，确定执行删除吗？"-->
<!--                                                                    (nzOnConfirm)="deleteNodePool('node', item.hostName + '&' + item.vpcIp, item);"-->
<!--                                                                    [ngClass]="{'disabled': !nodeCanDelete(item)}">-->
<!--                                                                    <i nzTitle="删除"-->
<!--                                                                        nzTooltipContent="bottom"-->
<!--                                                                        nz-tooltip-->
<!--                                                                        class="icon fa fa-trash-o"></i>-->
<!--                                                                </div>-->
                            <div class="on-table-actions" style="float:left;" *ngIf="permission('delete')"
                                 [hidden]="busyStatus[item.name]">
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipContent="top"
                                     [nzCondition]="!nodeCanDelete(item)"
                                     [nzPopconfirmTitle]="'确定要删除该云服务器吗？'"
                                     (nzOnConfirm)="deleteNodePool(item);"
                                     >
                                    <i nzTooltipTitle="删除"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-trash-o"></i>
                                </div>
                            </div>

                            <!--                                </div>-->
                            <!--                                <div class="on-table-actions"-->
                            <!--                                    [hidden]="!busyStatus[item.name]">-->
                            <!--                                    <div-->
                            <!--                                        class="action-loading-placeholder">-->
                            <!--                                        <i class="icon" nz-icon-->
                            <!--                                            [nzType]="'loading'"></i>-->
                            <!--                                        {{ getBusyText(item.name) }}-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #nodePageTemplate let-type let-page="page">
                    <a *ngIf="type === 'pre'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <div class="content-body-item" *ngIf="activeContentIndex === 2">
                <!--节点-->
                <div class="header">
                    <p>节点
                        <!--                        <small class="warning-text" *ngIf="showResLimit">-->
                        <!--                            <i class="fa fa-exclamation-circle"></i>-->
                        <!--                            每个用户最多可免费创建2个计算节点，如有其它需求请提交工单联系！-->
                        <!--                        </small>-->
                        <!--                        <span class="header-bottom-tips">计算节点初始用户名：root，初始密码：root</span>-->
                    </p>
                    <div class="pull-right">
<!--                        <button nz-button nzType="primary"-->
<!--                                [ngClass]="{'disabled': resLimit}"-->
<!--                                (click)="!resLimit && showAddNodeWindow()">-->
<!--                            <i nz-icon nzType="plus"-->
<!--                               nzTheme="outline"></i>-->
<!--                            新增节点-->
<!--                        </button>-->
                    </div>
                </div>
                <!--内容-->
                <nz-table #nodeList
                          [nzBordered]=true
                          [nzItemRender]="nodePageTemplate"
                          [nzFrontPagination]="false"
                          [nzTotal]="nodePage.total"
                          [nzPageIndex]="nodePage.current"
                          [nzPageSize]="nodePage.size"
                          (nzPageIndexChange)="nodePageChange($event)"
                          [nzData]="nodeListData">
                    <thead>
                    <tr>
                        <th width="400">节点</th>
                        <th>规格</th>
                        <th>根卷(GB)</th>
                        <th>数据卷(GB)</th>
                        <th>创建日期</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of nodeList.data">
                        <td align="left" style="padding-left:35px;"><span style="color:#1890ff;">{{ item.name || '-' }}</span><br>{{item.uid }}</td>
                        <td>{{item.flavor}}</td>
                        <td>{{item.rootVolumeSize}}</td>
                        <td>{{item.dataVolumeSize}}</td>
                        <td>
                            {{item.creationTimestamp}}
                        </td>
                        <!--更多操作-->
                        <td>
                            <!--                                <div class="on-table-actions"-->
                            <!--                                    [hidden]="busyStatus[item.name]">-->
                            <!--                                    <div class="on-table-action-item"-->
                            <!--                                        (click)="visit(item);"-->
                            <!--                                        [ngClass]="{'disabled': !nodeCanVisit(item)}">-->
                            <!--                                        <i nzTitle="访问"-->
                            <!--                                            nzTooltipContent="bottom"-->
                            <!--                                            nz-tooltip-->
                            <!--                                            class="icon fa fa-eye"></i>-->
                            <!--                                    </div>-->
                            <!--                                    <div class="on-table-action-item"-->
                            <!--                                        nz-popconfirm-->
                            <!--                                        nzTooltipContent="top"-->
                            <!--                                        [nzCondition]="!nodeCanDelete(item)"-->
                            <!--                                        nzTitle="删除节点会导致部署在此节点上的服务关闭，确定执行删除吗？"-->
                            <!--                                        (nzOnConfirm)="deleteItem('node', item.hostName + '&' + item.vpcIp, item);"-->
                            <!--                                        [ngClass]="{'disabled': !nodeCanDelete(item)}">-->
                            <!--                                        <i nzTitle="删除"-->
                            <!--                                            nzTooltipContent="bottom"-->
                            <!--                                            nz-tooltip-->
                            <!--                                            class="icon fa fa-trash-o"></i>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                                <div class="on-table-actions"-->
                            <!--                                    [hidden]="!busyStatus[item.name]">-->
                            <!--                                    <div-->
                            <!--                                        class="action-loading-placeholder">-->
                            <!--                                        <i class="icon" nz-icon-->
                            <!--                                            [nzType]="'loading'"></i>-->
                            <!--                                        {{ getBusyText(item.name) }}-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #nodePageTemplate let-type let-page="page">
                    <a *ngIf="type === 'pre'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <!--命名空间-->
            <div class="content-body-item" *ngIf="activeContentIndex === 3">
                <div class="header">
                    <p>命名空间</p>
                    <div class="pull-right" *ngIf="permission('create')">
                        <button nz-button nzType="primary" (click)="showAddNamespaceWindow()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            新增命名空间
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #namespaceList
                        [nzBordered]=true
                        [nzItemRender]="namespaceTemplate"
                        [nzFrontPagination]="false"
                        [nzTotal]="namespacePage.total"
                        [nzPageIndex]="namespacePage.current"
                        [nzPageSize]="namespacePage.size"
                        (nzPageIndexChange)="namespacePageChange($event)"
                        [nzData]="namespaceListData">
                    <thead>
                        <tr>
                            <th>命名空间名称</th>
                            <th>命名空间描述</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of namespaceList.data">
                            <td class="fixed-td">{{ item.name }}</td>
                            <td class="fixed-td">{{ item.des || '-' }}</td>
                            <td>{{ getdate(item.createTm) }}</td>
                            <td>
                                <span class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status, 'namespace') }}</span>                                
                            </td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" *ngIf="permission('update')"
                                    [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item"
                                        (click)="showUpdateNamespaceWindow(item)"
                                        [ngClass]="{'disabled': !namespaceCanOperate(item)}">
                                        <i nzTitle="修改"
                                            nzTooltipContent="bottom"
                                            nz-tooltip
                                            class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" *ngIf="permission('delete')"
                                        nz-popconfirm
                                        nzTooltipContent="top"
                                        [nzCondition]="!namespaceCanOperate(item)"
                                        nzTitle="删除命名空间会同时删除该命名空间下的所有服务！确定要删除该命名空间吗？"
                                        (nzOnConfirm)="deleteNodePool('namespace', item.id, item)"
                                        [ngClass]="{'disabled': !namespaceCanOperate(item)}">
                                        <i nzTitle="删除"
                                            nzTooltipContent="bottom"
                                            nz-tooltip
                                            class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions"
                                    [hidden]="!busyStatus[item.id]">
                                    <div
                                        class="action-loading-placeholder">
                                        <i class="icon" nz-icon
                                            [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
                <ng-template #namespaceTemplate let-type let-page="page">
                    <a *ngIf="type === 'pre'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <!--服务-->
            <div class="content-body-item" *ngIf="activeContentIndex === 4">
                <div class="header">
                    <p>服务部署</p>
                    <div class="pull-right" *ngIf="permission('create')">
                        <button nz-button nzType="primary" (click)="toServiceConfig()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            新增服务部署
                        </button>
                    </div>
                </div>
                <nz-select style="width: 200px;margin-bottom: 20px" [(ngModel)]="namespaceSelected"
                    (ngModelChange)="getServiceDeploy(namespaceSelected)">
                    <nz-option *ngFor="let option of namespaceListData" [nzValue]="option.name"
                        [nzLabel]="option.name">
                    </nz-option>
                </nz-select>
                <!--内容-->
                <nz-table #serviceDeployListData
                            [nzBordered]=true
                            [nzPageSize]=99999
                            [nzShowPagination]=false
                            [nzData]="serviceDeployList">
                    <thead>
                        <tr>
                            <th>服务名称</th>
                            <th>集群内IP</th>
                            <th>运行/预期数量</th>
                            <th>访问方式</th>
                            <th>标签</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of serviceDeployListData.data">
                            <td width="20%">{{ item.deploymentName || '无' }}</td>
                            <td>{{ item.clusterIp || '无' }}</td>
                            <td [ngClass]="{'warning-text': item.readyNum !== item.replicasNum}">{{ item.readyNum }} / {{ item.replicasNum }}</td>
                            <td>{{ item.serviceType == 'ClusterIP' ? '仅在集群内访问' : 'VPC内网访问' }}</td>
                            <td width="20%">{{ analysisLabels(item.labels) }}</td>
                            <td>{{ getServiceTime(item.createTime) }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" *ngIf="permission('view')"
                                    [hidden]="busyStatus[item.deploymentName]">
                                    <div class="on-table-action-item"
                                        (click)="getServicePod(item)">
                                        <i nzTitle="查看容器"
                                            nzTooltipContent="bottom"
                                            nz-tooltip
                                            nz-icon 
                                            nzType="code-sandbox" 
                                            nzTheme="outline"></i>
                                    </div>
                                    <div class="on-table-action-item" *ngIf="permission('view')"
                                        (click)="showPort(item)">
                                        <i nzTitle="端口"
                                            nzTooltipContent="bottom"
                                            nz-tooltip
                                            nz-icon 
                                            nzType="apartment" 
                                            nzTheme="outline"></i>
                                    </div>
                                    <div class="on-table-action-item" *ngIf="permission('delete')"
                                        nz-popconfirm
                                        nzTooltipContent="top"
                                        [nzCondition]="!serviceCanDelete()"
                                        nzTitle="确定要删除该服务吗？"
                                        (nzOnConfirm)="deleteService(item)"
                                        [ngClass]="{'disabled': !serviceCanDelete()}">
                                        <i nzTitle="删除"
                                            nzTooltipContent="bottom"
                                            nz-tooltip
                                            nz-icon nzType="delete"
                                            nzTheme="outline"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions"
                                    [hidden]="!busyStatus[item.deploymentName]">
                                    <div
                                        class="action-loading-placeholder">
                                        <i class="icon" nz-icon
                                            [nzType]="'loading'"></i>
                                        {{ getBusyText(item.deploymentName) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!--告警规则-->
            <div class="content-body-item" *ngIf="activeContentIndex === 5">
                <div class="header">
                    <p>告警规则 <span class="warning-text">*告警规则的相关操作会影响告警实例，请谨慎操作！</span></p>
                    <div class="pull-right" *ngIf="permission('create')">
                        <button nz-button nzType="primary" (click)="showAddWarningRuleWindow()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            新增告警规则
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #warningRule
                        [nzBordered]=true
                        [nzItemRender]="warningRuleTemplate"
                        [nzFrontPagination]="false"
                        [nzTotal]="warningRulePage.total"
                        [nzPageIndex]="warningRulePage.current"
                        [nzPageSize]="warningRulePage.size"
                        (nzPageIndexChange)="warningRulePageChange($event)"
                        [nzData]="warningRuleListData">
                    <thead>
                    <tr>
                        <th>告警类型</th>
                        <th>告警条件</th>
                        <th>状态</th>
                        <th>告警延时</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of warningRule.data">
                        <td>{{ matchWarningRuleType(item.ruleType) }}</td>
                        <td width="280">{{ item.valueComputed }}</td>
                        <td>
                            <span class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status, 'service') }}</span>
                        </td>
                        <td>{{ matchWarningRuleDelayTime(item.delayTime) }}</td>
                        <td>{{ getdate(item.createTm) }}</td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions" *ngIf="permission('update')"
                                [hidden]="busyStatus['warningRuleOperate']">
                                <div class="on-table-action-item"
                                    (click)="showUpdateWarningRuleWindow(item)">
                                    <i nzTitle="修改"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-wrench"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('update')"
                                    [hidden]="item.status === 0"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要禁用该告警规则吗？"
                                    (nzOnConfirm)="updateWarningRuleStatusFun(item, 0)">
                                    <i nzTitle="禁用"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-stop-circle-o"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('update')"
                                    [hidden]="item.status === 1"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要启用该告警规则吗？"
                                    (nzOnConfirm)="updateWarningRuleStatusFun(item, 1)">
                                    <i nzTitle="启用"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-play-circle-o"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该告警规则吗？"
                                    (nzOnConfirm)="deleteNodePool('warningRule', item.ruleId, item)">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus['warningRuleOperate']">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText('warningRuleOperate') }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #warningRuleTemplate let-type let-page="page">
                    <a *ngIf="type === 'pre'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <!--告警用户-->
            <div class="content-body-item" *ngIf="activeContentIndex === 6">
                <div class="header">
                    <p>告警用户 <span class="warning-text">*告警用户的相关操作会影响告警实例，请谨慎操作！</span></p>
                    <div class="pull-right" *ngIf="permission('create')">
                        <button nz-button nzType="primary" (click)="showAddWarningUserWindow()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            新增告警用户
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #warningUser
                        [nzBordered]=true
                        [nzItemRender]="warningUserTemplate"
                        [nzFrontPagination]="false"
                        [nzTotal]="warningUserPage.total"
                        [nzPageIndex]="warningUserPage.current"
                        [nzPageSize]="warningUserPage.size"
                        (nzPageIndexChange)="warningUserPageChange($event)"
                        [nzData]="warningUserListData">
                    <thead>
                    <tr>
                        <th>用户名</th>
                        <th>邮箱地址</th>
                        <th>创建时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of warningUser.data">
                        <td>{{ item.reciverName }}</td>
                        <td class="fixed-td">{{ item.emailAddress }}</td>
                        <td>{{ getdate(item.createTm) }}</td>
                        <td>
                            <span class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status, 'namespace') }}</span>                                
                        </td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions" *ngIf="permission('update')"
                                [hidden]="busyStatus[item.reciverId]">
                                <div class="on-table-action-item"
                                    (click)="showChangeWarningUserStatus(item)">
                                    <i nzTitle="修改"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-wrench"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该告警用户吗？"
                                    (nzOnConfirm)="deleteNodePool('warningUser', item.reciverId, item)">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.reciverId]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item.reciverId) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #warningUserTemplate let-type let-page="page">
                    <a *ngIf="type === 'pre'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <!--告警实例-->
            <div class="content-body-item" *ngIf="activeContentIndex === 7">
                <div class="header">
                    <p>告警实例</p>
                    <nz-radio-group [(ngModel)]="warningInstanceType" (ngModelChange)="getWarningInstance()">
                        <label nz-radio nzValue="active">已激活告警实例</label>
                        <label nz-radio nzValue="silence">静默告警实例</label>
                    </nz-radio-group>
                </div>
                <nz-select style="width: 200px;margin-bottom: 20px" [(ngModel)]="warningInstanceGroupSelected"
                    (ngModelChange)="getWarningInstanceByGroup()">
                    <nz-option *ngFor="let option of warningInstanceGroup" [nzValue]="option"
                        [nzLabel]="option">
                    </nz-option>
                </nz-select>
                <!--内容-->
                <nz-table #warningInstance
                            [nzBordered]=true
                            [nzPageSize]=99999
                            [nzShowPagination]=false
                            [nzData]="warningInstanceListData">
                    <thead>
                    <tr>
                        <th *ngIf="warningInstanceType === 'active'">告警内容</th>
                        <th>告警类型</th>
                        <th>异常主机名</th>
                        <th *ngIf="warningInstanceType === 'active'">告警时间</th>
                        <th *ngIf="warningInstanceType !== 'active'">起始时间</th>
                        <th *ngIf="warningInstanceType !== 'active'">结束时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of warningInstance.data">
                        <td *ngIf="warningInstanceType === 'active'">
                            {{ item.annotations.description }}
                        </td>
                        <td>{{ item.alertType }}</td>
                        <td>{{ item.hostname }}</td>
                        <td>{{ getWarningInstanceTime(item.startsAt) }}</td>
                        <td *ngIf="warningInstanceType !== 'active'">
                            {{ getWarningInstanceTime(item.endsAt) }}
                        </td>
                        <td>
                            <span class="dot {{ getStatusClass(warningInstanceType) }}">{{ getStatusText(warningInstanceType, 'warningInstance') }}</span>                                
                        </td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions" *ngIf="permission('restore')">
                                <div class="on-table-action-item"
                                    [hidden]="warningInstanceType === 'active'"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要恢复该告警实例吗？"
                                    (nzOnConfirm)="cancelSilence(item.id)">
                                    <i nzTitle="恢复告警"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-bell-o"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('update')"
                                    [hidden]="warningInstanceType === 'silence'"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要静默该告警实例吗？"
                                    (nzOnConfirm)="silence(item.label)">
                                    <i nzTitle="静默告警"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-bell-slash-o"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
            </div>
        </div>
    </div>
</div>

<!--修改基本信息弹出层-->
<nz-modal [(nzVisible)]="showChangeBasicWindow" nzTitle="修改集群描述" 
    [nzOkLoading]="isLoading"
    [nzCancelLoading]="isLoading"
    (nzOnCancel)="showChangeBasicWindow = false"
    (nzOnOk)="submitChangeBasic()">
    <div class="select-container">
        <span class="select-tips">集群描述：</span>
        <textarea style="width: 300px; height: 120px"
            nz-input rows="2"
            maxlength="200"
            [(ngModel)]="des"
            placeholder="请输入实例描述(长度200字符以内)">
        </textarea>
    </div>
</nz-modal>
<!--镜像仓库操作弹出层-->
<nz-modal [(nzVisible)]="showCreateOrUpdateBindMirrorRepo" nzTitle="{{showCreateOrUpdateBindMirrorRepoTitle}}" 
    [nzOkLoading]="mirrorRepoLoading"
    [nzCancelLoading]="mirrorRepoLoading"
    (nzOnCancel)="showCreateOrUpdateBindMirrorRepo = false"
    (nzOnOk)="submitCreateOrUpdateBindMirrorRepo()">
    <div class="select-container">
        <span class="select-tips">镜像仓库：</span>
        <nz-select style="width: 300px;" [nzDisabled]="mirrorRepoLoading"
                    [(ngModel)]="bindMirrorRepoIp">
            <nz-option *ngFor="let option of bindMirrorRepoList"
                [nzValue]="option.elasticIp + '-' + option.id"
                [nzLabel]="option.clusterName">
            </nz-option>
        </nz-select>
    </div>
    <div class="select-container">
        <span class="select-tips">用户名：</span>
        <nz-input-group style="width: 300px;"  [nzSuffix]="suffixTemplate">
        <input type="text" nz-input [disabled]="mirrorRepoLoading" 
            [(ngModel)]="mirrorRepoUserName" placeholder="请输入镜像仓库用户名" />
        </nz-input-group>
        <ng-template #suffixTemplate
        ><i
            nz-icon
            nz-tooltip
            class="ant-input-clear-icon"
            nzTheme="fill"
            nzType="close-circle"
            *ngIf="mirrorRepoUserName && !mirrorRepoLoading"
            (click)="mirrorRepoUserName = null"
        ></i
        ></ng-template>
    </div>
    <div class="select-container">
        <span class="select-tips">密码：</span>
        <input nz-input [disabled]="mirrorRepoLoading"
               type="password"
               style="width: 300px;"
               placeholder="请输入镜像仓库密码"
               [(ngModel)]="mirrorRepoPassword"/>
    </div>
</nz-modal>
<!--创建节点弹出层-->
<nz-modal [(nzVisible)]="showCreateNodeWindow" nzTitle="节点扩缩容"
    (nzOnCancel)="showCreateNodeWindow = false" [nzWidth]="500"
    [nzOkLoading]="isLoading"
    [nzCancelLoading]="isLoading"
    (nzOnOk)="submitScaleNode()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">当前节点数：</span>
        <span>{{scaleNodeObj.initialNodeCount}}</span>
    </div>
    <div class="select-container">
        <span class="select-tips">调整为：</span>
        <span><nz-input-number [nzPrecision]=0 [(ngModel)]="initialNodeCount" [nzMin]="0" [nzMax]="200" [nzStep]="1">
                        </nz-input-number></span>
    </div>
    </ng-container>
</nz-modal>
<!--创建/更新告警规则弹出层-->
<nz-modal [(nzVisible)]="showCreateWarningRuleWindow" nzTitle="{{ createUpdateWarningRuleWindowTitle }}"
    (nzOnCancel)="showCreateWarningRuleWindow = false"
    [nzOkLoading]="isLoading" [nzWidth]="500"
    [nzCancelLoading]="isLoading"
    (nzOnOk)="submitAddWarningRule()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">当前节点数：</span>
        <span>{{scaleNodeObj.initialNodeCount}}</span>
    </div>
    <div class="select-container" *ngIf="!warningRuleTypeNodeAlive">
        <span class="select-tips">告警条件：</span>
        <nz-select style="width: 80px;" [(ngModel)]="selectLimitType">
            <nz-option nzValue="<" nzLabel="<"></nz-option>
            <nz-option nzValue=">" nzLabel=">"></nz-option>
        </nz-select>
        <nz-input-number style="width: 80px; margin-left: 10px" *ngIf="!warningRuleTypeIo" [(ngModel)]="limitValue"
            [nzMin]="1" [nzPrecision]=2 [nzMax]="100" [nzStep]="1" [nzFormatter]="formatterPercent" [nzParser]="parserPercent">
        </nz-input-number>
        <!--流量流出、进入-->
        <nz-input-number style="width: 80px; margin-left: 10px" *ngIf="warningRuleTypeIo" [(ngModel)]="limitValueIo"
            [nzMin]="1" [nzPrecision]=0 [nzStep]="1">
        </nz-input-number>
        <nz-select style="width: 80px; margin-left: 10px" *ngIf="warningRuleTypeIo" [(ngModel)]="limitValueIoUnit">
            <nz-option nzValue="KB" nzLabel="KB"></nz-option>
            <nz-option nzValue="MB" nzLabel="MB"></nz-option>
        </nz-select>
    </div>
    <div class="select-container">
        <span class="select-tips">延时告警时间：</span>
        <nz-input-number style="width: 80px;" [nzPrecision]=0 [(ngModel)]="delayTimeValue" [nzMin]="1" [nzMax]="60" [nzStep]="1">
        </nz-input-number>
        <nz-select style="width: 80px; margin-left: 10px" [(ngModel)]="selectDelayTimeType">
            <nz-option nzValue="m" nzLabel="分钟"></nz-option>
            <nz-option nzValue="h" nzLabel="小时"></nz-option>
        </nz-select>
    </div>
    </ng-container>
</nz-modal>
<!--静默告警实例弹出层-->
<nz-modal [(nzVisible)]="silenceWarningInstanceWindow" nzTitle="静默告警实例"
    [nzWidth]=600
    (nzOnCancel)="silenceWarningInstanceWindow = false" (nzOnOk)="submitSilenceWarningInstance()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">静默时间段：</span>
        <nz-date-picker
            [nzDisabled]=true
            nzShowTime
            nzFormat="yyyy-MM-dd HH:mm:ss"
            [(ngModel)]="silenceTimeStart">
        </nz-date-picker>
        <span style="display: inline-block; margin: 0 10px;">~</span>
        <nz-date-picker
            [nzDisabledDate]="disabledDate"
            [nzDisabledTime]="disabledRangeTime"
            nzShowTime
            nzFormat="yyyy-MM-dd HH:mm:ss"
            [(ngModel)]="silenceTimeEnd">
        </nz-date-picker>
    </div>
    </ng-container>
</nz-modal>
<!--创建告警用户弹出层-->
<nz-modal [(nzVisible)]="showCreateWarningUserWindow" nzTitle="创建告警用户"
    (nzOnCancel)="showCreateWarningUserWindow = false"
    [nzOkLoading]="isLoading" [nzWidth]="600"
    [nzCancelLoading]="isLoading"
    (nzOnOk)="submitAddWarningUser()">
    <ng-container *nzModalContent>
    <form nz-form [formGroup]="validateForm">
        <nz-form-item>
            <nz-form-label [nzSpan]="6" nzRequired nzFor="username">用户名</nz-form-label>
            <nz-form-control [nzSpan]="14">
                <input nz-input formControlName="username" id="username" />
                <nz-form-explain
                    *ngIf="(validateForm.get('username')?.dirty && validateForm.get('username')?.errors)">
                    用户名需在1-16位之间！
                </nz-form-explain>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="6" nzRequired nzFor="email">E-mail</nz-form-label>
            <nz-form-control [nzSpan]="14">
                <input nz-input formControlName="email" maxlength="40" id="email" />
                <nz-form-explain
                    *ngIf="(validateForm.get('email')?.dirty && validateForm.get('email')?.errors)">
                    邮箱格式不规范！
                </nz-form-explain>
            </nz-form-control>
        </nz-form-item>
    </form>
    </ng-container>
</nz-modal>
<!--修改告警用户状态弹出层-->
<nz-modal [(nzVisible)]="changeWarningUserStatusWindow" nzTitle="修改告警用户状态"
    (nzOnCancel)="changeWarningUserStatusWindow = false"
    [nzOkLoading]="isLoading" [nzWidth]="600"
    [nzCancelLoading]="isLoading"
    (nzOnOk)="submitChangeWarningUserStatus()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="update-tips">用户名：</span>
        <span class="update-tips-value">{{ changeWarningUserItem.reciverName }}</span>
    </div>
    <div class="select-container">
        <span class="update-tips">邮箱：</span>
        <span class="update-tips-value">{{ changeWarningUserItem.emailAddress }}</span>
    </div>
    <div class="select-container">
        <span class="update-tips">状态：</span>
        <span class="update-tips-value">
            <nz-radio-group [(ngModel)]="warningUserStatusValue">
                <label nz-radio nzValue="1">激活</label>
                <label nz-radio nzValue="0">取消激活</label>
            </nz-radio-group>
        </span>
    </div>
    </ng-container>
</nz-modal>
<!--创建命名空间弹出层-->
<nz-modal [(nzVisible)]="showCreateNamespaceWindow"
          nzTitle="{{ createOrUpdateNamespaceTitle }}"
          (nzOnCancel)="showCreateNamespaceWindow = false" 
          [nzOkLoading]="isLoading" [nzWidth]="600"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submitAddOrUpdateNamespace()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">命名空间名称：</span>
        <input nz-input
               style="width: 300px;"
               maxlength="63"
               [disabled]="!ifCreateNamespace"
               (blur)="checkNamespaceName()"
               [(ngModel)]="namespaceName"/>
        <p [ngClass]="{'warning-tips': !namespaceNameReg}">*长度为2-63个字符之间(含),可包含小写字母、数字及分隔符("-"),不能以分隔符开头或结尾</p>
    </div>
    <div class="select-container">
        <span class="select-tips">命名空间描述：</span>
        <textarea style="width: 300px; height: 120px"
                  nz-input rows="2"
                  (blur)="checkNamespaceDes()"
                  [(ngModel)]="namespaceDes"
                  placeholder="命名空间描述(长度200字符以内)">
        </textarea>
        <p [ngClass]="{'warning-tips': !namespaceDesReg}">*长度需在200字符以内</p>
    </div>
    </ng-container>
</nz-modal>
<!--服务部署容器弹出层-->
<nz-modal [(nzVisible)]="showPod" nzTitle="{{showPodObj.deploymentName}} 容器列表"
    [nzWidth]=1000
    [nzCancelText]="'关闭'"
    [nzOkText]="'手动刷新'"
    (nzOnCancel)="closePod()"
    (nzOnOk)="refreshPod()">
    <ng-container *nzModalContent>
    <nz-table #nestedTable 
            [nzBordered]=true
            [nzData]="podListData" 
            [nzPageSize]=99999
            [nzShowPagination]=false>
        <thead>
          <tr>
            <th nzShowExpand></th>
            <th>名称</th>
            <th>主机IP</th>
            <th>实例IP</th>
            <th>状态</th>
            <th>创建时间</th>
            <th>异常日志</th>
          </tr>
        </thead>
        <tbody>
          <ng-template ngFor let-data [ngForOf]="nestedTable.data">
            <tr>
              <td nzShowExpand [(nzExpand)]="data.expand"></td>
              <td width="20%">{{ data.podName }}</td>
              <td>{{ data.hostIp || '无' }}</td>
              <td>{{ data.podIp || '无' }}</td>
              <!-- <td>{{ data.status }}</td> -->
              <td>
                <span class="dot {{ getStatusClass(data.status) }}">{{ getStatusText(data.status, 'service') }}</span>
            </td>
              <td>{{ data.createTime ? getServiceTime(data.createTime) : '无'}}</td>
              <td>
                <i class="table-btn-box able-icon" 
                    nz-icon nzType="warning" nzTheme="outline"
                    nzTitle="{{ data.podMessage || '无' }}"
                    nzTooltipContent="bottomCenter"
                    nz-tooltip
                ></i>
              </td>
            </tr>
            <tr [nzExpand]="data.expand">
              <td></td>
              <td colspan="7">
                <nz-table #innerTable [nzData]="data.container || []" nzSize="middle" [nzShowPagination]="false">
                  <thead>
                    <tr>
                      <th>容器名称</th>
                      <th>镜像版本</th>
                      <th>日志/异常</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let data2 of innerTable.data">
                      <td width="25%" style="word-break: break-all;">{{ data2.containerName }}</td>
                      <td width="45%"><span style="word-break: break-all;">{{ data2.imageVersion }}</span></td>
                      <td>
                        <i nz-icon *ngIf="!data2.errorMessage"
                            class="table-btn-box able-icon"
                            (click)="showContainerLog(data.podName, data2.containerName)"
                            nzType="file-text" nzTheme="outline"
                            nzTitle="查看日志"
                            nzTooltipContent="bottomCenter"
                            nz-tooltip
                        ></i>
                        <i nz-icon *ngIf="data2.errorMessage"
                            class="table-btn-box able-icon"
                            nzType="warning" nzTheme="outline"
                            nzTitle="{{ data2.errorMessage || '无' }}"
                            nzTooltipContent="bottomCenter"
                            nz-tooltip>
                        </i>
                      </td>
                    </tr>
                  </tbody>
                </nz-table>
              </td>
            </tr>
          </ng-template>
        </tbody>
      </nz-table>
    </ng-container>
</nz-modal>
<!--服务部署端口弹出层-->
<nz-modal [(nzVisible)]="ifShowPort" nzTitle="{{showPortTitle}} 端口列表"
    [nzWidth]=1000
    [nzCancelText]=null
    [nzOkText]="'关闭'"
    (nzOnCancel)="ifShowPort=false"
    (nzOnOk)="ifShowPort=false">
    <ng-container *nzModalContent>
    <nz-table #portTable 
            [nzBordered]=true
            [nzData]="portTableLIst" 
            [nzPageSize]=99999
            [nzShowPagination]=false>
        <thead>
          <tr>
            <th>容器端口</th>
            <th>集群内端口</th>
            <th>主机端口</th>
            <th>类型</th>
          </tr>
        </thead>
        <tbody>
            <tr *ngFor="let item of portTable.data">
              <td class="fixed-td">{{ item.containerPort || '无' }}</td>
              <td class="fixed-td">{{ item.clusterPort || '无' }}</td>
              <td class="fixed-td">{{ item.nodePort || '无' }}</td>
              <td class="fixed-td">{{ item.type || '无' }}</td>
            </tr>
        </tbody>
      </nz-table>
    </ng-container>
</nz-modal>
<!-- pod/容器日志 -->
<nz-drawer
      [nzClosable]="false"
      [nzVisible]="showContainerLogDrawer"
      nzTooltipContent="right"
      [nzWidth]=800
      nzTitle="{{containerLogDrawerTitle}} 日志"
      (nzOnClose)="showContainerLogDrawer=false"
    >
    <p class="warning-text">*当前最多展示100条数据，需登录虚机控制台查看更多</p>
    <ol class="log-list">
        <li *ngFor="let data of containerLog">
            <p><span class="log-container-name">[{{containerLogDrawerTitle}}] </span>{{data}}</p>
        </li>
    </ol>
</nz-drawer>
<!--加载动画-->
<div class="ant-modal-mask ng-star-inserted" *ngIf="showSpain" ng-reflect-klass="ant-modal-mask">
    <div class="spin-container">
        <ng-template #indicatorTemplate><i nz-icon nzType="sync" [nzSpin]="showSpain"></i> </ng-template>
        <nz-spin nzSimple
                 [nzIndicator]="indicatorTemplate"
                 nzTip="加载中，请稍后...">
        </nz-spin>
    </div>
</div>

<app-k8s-node-pool-edit
        [isVisible]="editNodePoolWindow"
        type="add"
        [bean]="bean"
        (refreshParent)="getNodePoolList()"
        (close)="editNodePoolWindow = false">
</app-k8s-node-pool-edit>