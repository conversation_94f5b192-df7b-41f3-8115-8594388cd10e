<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><span>Redis数据库</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入Redis数据库名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <a nz-button nzType="primary"
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="resLimit ? null : '../redis-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建Redis数据库
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">Redis数据库
                    <!-- <small class="danger" *ngIf="showResLimit">
                        <i class="fa fa-exclamation-circle"></i>
                        试用期间每个用户最多可免费创建1个Redis数据库，如有其它需求请提交工单联系！
                    </small> -->
                </span>
            </div>
            <nz-table
                #redis style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="redisList">
                <thead>
                    <!-- <tr>
                        <th width="20%">名称</th>
                        <th width="10%">运行状态</th>
                        <th width="10%">架构类型</th>
                        <th width="15%">内网IP</th>
                        <th width="10%">磁盘规格(GB)</th>
                        <th width="10%">内存规格(GB)</th>
                        <th width="15%">操作</th>
                    </tr> -->
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of redis.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>
                            <span class="dot {{ getRedisStatusClass(item) }}">{{ getStatusText(item.redisStatus)}}</span>
                        </td>
                        <td>{{ item.redisType === 'cluster' ? '集群' : '标准'}}</td>
                       <!-- <td>{{ getIpAddress(item) }}
                            <div *ngFor="let data of ipAddressList; let i = index">
                                <span>{{data}}</span>
                            </div>
                        </td>-->
                        <td>
                            <div *ngFor="let data of item.ipAddressList; let i = index">
                                <span>{{data}}</span>
                            </div>
                        </td>
                        <!-- <td>{{ item.diskGb }}</td> -->
                        <td>{{ item.sharding + 'G' }}</td>
                        <!-- <td>后付费</td> -->
                        <td style="white-space: nowrap">
                            <div class="on-table-actions" *ngIf="permission('view')"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    *ngIf="item.redisStatus !== 'stop'"
                                    (click)="viewDetail(item);">
                                    <!-- [ngClass]="{'disabled': !canViewDetail(item)}" -->
                                    <i nzTitle="详情"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon"
                                        nz-icon nzType="search" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要关闭该Redis实例吗？"
                                    (nzOnConfirm)="powerOffRedis(item);"
                                    [nzCondition]="!canPowerOffRedis(item)"
                                    [hidden]="!(canPowerOffRedis(item) || item.deployStatus === 'INIT')"
                                    [ngClass]="{'disabled': !canPowerOffRedis(item)}">
                                    <i nzTooltipTitle="关机"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-guanji">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    (click)="powerOnRedis(item);"
                                    [hidden]="!canPowerOnRedis(item)"
                                    [ngClass]="{'disabled': !canPowerOnRedis(item)}">
                                    <i nzTooltipTitle="开机"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-qidong"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要重启该Redis实例吗？"
                                    [nzCondition]="!canRebootRedis(item)"
                                    (nzOnConfirm)="rebootRedis(item);"
                                    [ngClass]="{'disabled': !canRebootRedis(item)}">
                                    <i nzTooltipTitle="重启"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-haikezhangguizhushou_zhongqi">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('update')"
                                (click)="isArchiveUser === 'true'?null :showChangeService(item)"
                                    [hidden]="!canChange(item)"
                                    [ngClass]="{'disabled': !canChange(item) || isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="变更"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont iconfontBianji3 icon-bianji3"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzPlacement="top"
                                    [title]="isArchiveUser === 'true' ? null : '确定要删除该Redis实例吗？'"
                                    [nzCondition]="!canDeleteRedis(item)"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteRedis(item);"
                                    [ngClass]="{'disabled': !canDeleteRedis(item) || isArchiveUser === 'true'}">
                                    <i nzTitle="删除"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="redis.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
<nz-modal [(nzVisible)]="changeModalVisible" nzTitle="redis数据库变更" (nzOnCancel)="handleCancelChange()"
(nzOnOk)="changeService()" [nzOkLoading]="isChanging" [nzCancelLoading]="isChanging" [nzWidth]="620">
<ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="serviceItem" (submit)="changeService()">
    <section class="field-section">
        <div class="field-group">
            <div class="field-item topSty">
                <label>
                    <span class="label-text">redis数据库名称</span>
                    <input class="inputWidth_Name" type="text" formControlName="serviceName" disabled value="{{ serviceItem.value.instanceBefore.name }}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label class="label01">
                    <span class="label-text">CPU</span>
                    <input required type="text" formControlName="cpuBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.cpuNum + '核'}}"/>
                </label>

                <label class="label02">
                    <span class="label-text">CPU变更为</span>
                    <input required type="text" formControlName="cpuAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.cpuUnit + '核'}}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label class="label01">
                    <span class="label-text">内存</span>
                    <input required type="text" formControlName="memoryBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.memoryGB + 'G'}}"/>
                </label>

                <label class="label02">
                    <span class="label-text">内存变更为</span>
                    <input required type="text" formControlName="memoryAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.memoryUnit + 'G'}}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label class="label01">
                    <span class="label-text">磁盘</span>
                    <input required type="text" formControlName="diskBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.diskGB + 'G'}}"/>
                </label>

                <label class="label02">
                    <span class="label-text">磁盘变更为</span>
                    <input required type="text" formControlName="diskAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.diskUnit + 'G'}}"/>
                </label>
            </div>
        </div>
    </section>
</form>
</ng-container>
</nz-modal>
