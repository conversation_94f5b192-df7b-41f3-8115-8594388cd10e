$routingFiles = Get-ChildItem -Path "d:\workspace\aic\webui\src\app" -Filter "*-routing.module.ts" -Recurse

$fixedFilesCount = 0

foreach ($file in $routingFiles) {
    $content = Get-Content -Path $file.FullName -Raw
    $modified = $false
    
    # 检查文件是否包含 redirectTo 但没有 pathMatch
    if ($content -match 'redirectTo:' -and ($content -match '{\s*path:.*redirectTo:.*}' -or $content -match '{\s*path:.*redirectTo:.*,\s*}')) {
        $lines = Get-Content -Path $file.FullName
        $newLines = @()
        $inRouteBlock = $false
        $hasRedirectTo = $false
        $hasPathMatch = $false
        
        for ($i = 0; $i -lt $lines.Count; $i++) {
            $line = $lines[$i]
            $newLines += $line
            
            # 检测路由块的开始
            if ($line -match '\{\s*$' -and ($i -eq 0 -or $lines[$i-1] -match 'path:')) {
                $inRouteBlock = $true
                $hasRedirectTo = $false
                $hasPathMatch = $false
            }
            
            # 在路由块内检测 redirectTo
            if ($inRouteBlock -and $line -match 'redirectTo:') {
                $hasRedirectTo = $true
            }
            
            # 在路由块内检测 pathMatch
            if ($inRouteBlock -and $line -match 'pathMatch:') {
                $hasPathMatch = $true
            }
            
            # 如果找到 redirectTo 行但没有 pathMatch，并且下一行是路由块的结束，则添加 pathMatch
            if ($inRouteBlock -and $hasRedirectTo -and -not $hasPathMatch -and $i + 1 -lt $lines.Count -and $lines[$i + 1] -match '^\s*\},?\s*$') {
                # 获取当前行的缩进
                $indent = ""
                if ($line -match '^(\s+)') {
                    $indent = $matches[1]
                }
                
                # 检查当前行是否以逗号结束
                if ($line -match ',\s*$') {
                    $newLines += "$indent    pathMatch: 'full'"
                } else {
                    # 如果当前行不以逗号结束，添加逗号
                    $newLines[$newLines.Count - 1] = $line + ","
                    $newLines += "$indent    pathMatch: 'full'"
                }
                $modified = $true
            }
            
            # 检测路由块的结束
            if ($inRouteBlock -and $line -match '\},?\s*$') {
                $inRouteBlock = $false
            }
        }
        
        # 如果文件被修改，则保存
        if ($modified) {
            Set-Content -Path $file.FullName -Value $newLines
            $fixedFilesCount++
            Write-Host "已修复文件: $($file.FullName)"
        }
    }
}

Write-Host "修复完成！共修复了 $fixedFilesCount 个文件。"
