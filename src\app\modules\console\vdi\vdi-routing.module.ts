import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { VdiComponent } from '../../../components/console/vdi/vdi/vdi.component';
import { VdiConfigComponent } from '../../../components/console/vdi/vdi/config/vdi-config.component';
import { VdiPoolComponent } from '../../../components/console/vdi/vdi-pool/vdi-pool.component';

const routes: Routes = [
    {
        path: '',
        component: VdiPoolComponent,
        pathMatch: 'full',
    },
    {
        path: 'index',
        component: VdiComponent,
    },
    {
        path: 'config',
        component: VdiConfigComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class VdiRoutingModule {}
