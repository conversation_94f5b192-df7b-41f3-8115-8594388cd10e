<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><span>弹性公网IP</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入弹性公网IP" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div *ngIf="permission('refresh')">
                    <a nz-button class="default"
                       [ngClass]="{'disabled': isArchiveUser === 'true'}"
                       (click)="refresh()">
                        刷&nbsp;新
                    </a>
                </div>
                <div *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary"
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="'../public-network-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建弹性公网
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">弹性公网IP
                    <!-- 20200520 删除 -->
                    <!-- <small class="danger" *ngIf="showResLimit">
                        <i class="fa fa-exclamation-circle"></i>
                        限免期间每个用户最多可创建1个弹性公网IP，如有其它需求请提交工单联系！
                    </small> -->
                </span>
            </div>
            <nz-table #publicNetworks style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="publicNetworkList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzColumnKey="col.ColumnKey"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="500"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 150px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                        <!-- <th width="10%">弹性公网IP</th>
                        <th width="10%">状态</th>
                        <th width="10%">带宽</th>
                        <th width="20%">绑定实例</th>
                        <th width="10%">绑定实例类型</th>
                        <th width="15%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of publicNetworks.data; trackBy: trackById">
                        <td align="left" style="padding-left:35px;"><span style="color:#1890ff;">{{ item.ipAddress || '-' }}</span><br>{{item.resourceId }}</td>
                        <td>
                            <span class="dot"
                                *ngIf="!item.orderId"
                                [ngClass]="{'dot-green': item.ipStatus === 'assigned', 'dot-gray': item.ipStatus === 'unassigned'}">
                                {{ item.ipStatus === 'assigned' ? '已绑定' : '未绑定' }}
                            </span>
                            <span class="dot {{ getStatusClass(item) }}"
                                *ngIf="item.orderId">
                                {{ getStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.bandwidth }}Mbps</td>
                        <!-- 20200520 删除 -->
                        <!-- <td>按使用流量计费</td>
                        <td>后付费</td> -->
                        <td style="white-space: nowrap">
                            {{item.privateIpAddress}}
<!--                            <div *ngFor="let ipBinding of item.ipBinding; let i = index">-->
<!--                                 {{ipBinding.vmName}}-->
<!--                            </div>-->
                        </td>
<!--                        <td>-->
<!--                            <span>{{ item.bindType ? item.bindType : '-' }}</span>-->
<!--                            <i class="icon tip" *ngIf="item.bindType === '负载均衡'"-->
<!--                                nz-tooltip-->
<!--                                nzTooltipTitle="绑定类型为负载均衡的公网IP如需解绑，直接删除该负载均衡实例即可"-->
<!--                                nz-icon-->
<!--                                nzType="question-circle"-->
<!--                                nzTheme="outline"></i>-->
<!--                        </td>-->
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <!-- <div class="on-table-action-item"
                                [ngClass]="{'disabled': item.snat || isArchiveUser === 'true'}"
                                nz-popconfirm
                                nzPlacement="top"
                                [nzTitle]= "isArchiveUser === 'true' ? null :'确定设置为访问互联网出口？'"
                                [nzCondition]="item.snat"
                                (nzOnConfirm)="isArchiveUser === 'true' ? null :setSnat(item);"
                                >
                               <i nzTitle="访问互联网出口"
                                  nzPlacement="bottom"
                                  nz-tooltip
                                  class="icon iconfont icon-haikezhangguizhushou_zhongqi">
                               </i>
                           </div> -->
                           <div class="on-table-action-item" [hidden]="true" *ngIf="permission('view')"
                                    [ngClass]="{'disabled': !canCheckChart(item)}"
                                    (click)="checkChart(item)">
                                    <i nz-tooltip nz-icon nzType="fund" nzTooltipTitle="查看" class="icon"></i>
                                </div>
                               <div class="on-table-action-item" *ngIf="permission('binding')"
                                    [ngClass]="{'disabled': !canBindIp(item) || isArchiveUser === 'true'}"
                                    [hidden]="!canBindIp(item)"
                                    (click)="isArchiveUser === 'true'?null :bindIp(item)">
                                    <i nz-tooltip nz-icon nzType="link" nzTheme="outline" nzTooltipTitle="绑定" class="icon"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('binding')"
                                    [ngClass]="{'disabled': !canUnbindIp(item) || isArchiveUser === 'true'}"
                                    [hidden]="canBindIp(item)"
                                    nz-popconfirm
                                    [nzPopconfirmTitle]= "isArchiveUser === 'true' ? null : '确定要解绑该公网IP吗？'"
                                    [nzCondition]="!canUnbindIp(item)"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : unbindIp(item);">
                                    <i nz-tooltip nz-icon nzType="disconnect" nzTheme="outline" nzTooltipTitle="解绑" class="icon"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    [ngClass]="{'disabled': !canReleaseIp(item) || isArchiveUser === 'true'}"
                                    nz-popconfirm
                                    [nzPopconfirmTitle]= "isArchiveUser === 'true' ? null : '确定要释放该公网IP吗？'"
                                    [nzCondition]="!canReleaseIp(item)"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : releaseIp(item);" >
                                    <i nz-tooltip nz-icon nzType="delete" nzTheme="outline" nzTooltipTitle="释放" class="icon">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('update')"
                                    [ngClass]="{'disabled': !canChangeIp(item) || isArchiveUser === 'true'}"
                                    [hidden]="!canChangeIp(item)"
                                    (click)="isArchiveUser === 'true'?null :showChangeIp(item)">
                                    <i nz-tooltip nz-icon nzType="fund" nzTooltipTitle="变更" class="icon iconfont iconfontBianji3 icon-bianji3"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="(publicNetworks.data && publicNetworks.data.length) || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-bind-ip [isVisible]="bindModalVisible"
    [network]="currentNetwork" (submit)="reload()"
    (close)="bindModalVisible = false"></app-bind-ip>

<!-- 图表 -->
<nz-modal [nzWidth]="800" [(nzVisible)]="chartModalVisible"
    nzTitle="用量详情" [nzFooter]="null"
    (nzOnCancel)="chartModalVisible = false"
    (nzOnOk)="chartModalVisible = false">
    <ng-container *nzModalContent>
    <div class="network-usage">
        <div class="action-bar clearfix">
            <nz-select [(ngModel)]="checkTime"
                (ngModelChange)="checkTimeChange($event)"
                style="width: 120px;"
                nzPlaceHolder="请选择查询间隔时间">
                <nz-option *ngFor="let item of timeList"
                    [nzValue]="item.val"
                    [nzLabel]="item.name"></nz-option>
            </nz-select>
            <div class="pull-right">
                <button nz-button nzType="primary"
                    (click)="refreshChart();">
                    <i nz-icon nzType="reload"
                        nzTheme="outline"></i>
                    刷新
                </button>
            </div>
        </div>
        <nz-spin [nzSpinning]="isGettingChart" [nzDelay]="300">
            <div echarts [options]="elasticChartOption"
                [ngClass]="{'no-data': ipDataEmpty}"
                [merge]="chartOptionConfig" class="demo-chart">
            </div>
        </nz-spin>
    </div>
    </ng-container>
</nz-modal>
<!--变更弹窗-->
<nz-modal [(nzVisible)]="changeModalVisible" nzTitle="弹性公网带宽变更" (nzOnCancel)="handleCancelChange()"
(nzOnOk)="changeIp()" [nzOkLoading]="isOkLoading" [nzCancelLoading]="isLoading" [nzWidth]="620">
    <ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="ipItem" (submit)="changeIp()">
    <section class="field-section">
        <div class="field-title">
            变更协议： {{ipItem.value.quotaName}}
        </div>
        <div class="field-group">
            <div class="field-item">
                <label >
                    <span class="label-text">弹性公网IP</span>
                    <input required maxlength="50" type="text" formControlName="serviceName" disabled/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label  class="label01">
                    <span class="label-text">带宽</span>
                    <input required type="text" formControlName="bandwidthBefore" class="inputWidth" disabled/>
                </label>

                <label  class="label02">
                    <span class="label-text">带宽变更为</span>
                    <input required type="text" formControlName="bandwidthAfter" class="inputWidth" disabled/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item unitDiv">
                <label  class="label01">
                    <span class="label-text">单位</span>
                    <input required type="text" formControlName="unitBefore" class="inputWidth" disabled/>
                </label>

                <label  class="label02">
                    <span class="label-text">单位变更为</span>
                    <input required type="text" formControlName="unitAfter" class="inputWidth" disabled/>
                </label>
            </div>
        </div>
    </section>
 </form>
    </ng-container>
</nz-modal>
