import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ModelManagementComponent } from 'src/app/components/console/ai-model/management/model-management.component';
import { ModelMarketComponent } from 'src/app/components/console/ai-model/market/model-market.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'model-market',
        pathMatch: 'full',
    },
    {
        path: 'model-management',
        component: ModelManagementComponent,
    },
    {
        path: 'model-market',
        component: ModelMarketComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AiModelRoutingModule {}
