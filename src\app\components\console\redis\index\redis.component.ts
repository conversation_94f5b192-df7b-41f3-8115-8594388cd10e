import { Component, OnInit, OnDestroy } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal'
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { MessageService } from 'src/app/service/console/utils/message.service';
import { RedisService } from 'src/app/service/console/redis/redis.service';
import { environment } from 'src/environments/environment';
import { Router, ActivatedRoute } from '@angular/router';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { FormBuilder, FormGroup, AbstractControl, Validators} from "@angular/forms";

const PRODUCT = 'redis';
const LIMIT = 1;

const BUSY_TEXT_MAP = {
    'reboot': '重启中',
    'delete': '删除中',
    'start': '启动中',
    'powerOn': '开机中',
    'powerOff': '关机中',
};

const STATUS_CLASS_MAP = {
    'init': 'dot-blue',
    'running': 'dot-green',
    'stop': 'dot-red',
    'power_on': 'dot-green',
    'power_off': 'dot-gray',
};
const STATUS_MAP = {
    'init': '初始化中',
    'running': '运行中',
    'stop': '停止',
};

@Component({
    selector: 'app-redis',
    templateUrl: './redis.component.html',
    styleUrls: ['./redis.component.less']
})
export class RedisComponent implements OnInit, OnDestroy {
    constructor(
        private msg: MessageService,
        private modal: NzModalService,
        private router: Router,
        private route: ActivatedRoute,
        private redisService: RedisService,
        private fb: FormBuilder
    ) {
    }

    cols = [
        {
            title: '名称',
            ColumnKey: "name",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '330px'
        },
        {
            title: '运行状态',
            ColumnKey: "redisStatus",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '架构类型',
            ColumnKey: "redisType",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '内网IP',
            ColumnKey: "ipAddress",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        // {
        //     title: '磁盘规格',
        //     ColumnKey: "diskGb",
        //     allowSort: '',
        //     sortFlag: true,
        //     showSort: true,
        //     width: '150px'
        // },
        {
            title: '内存规格',
            ColumnKey: "sharding",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '操作',
            allowSort: '',
            sortFlag: true,
            showSort: false,
            ColumnKey: ""
        }
    ];

    isAdmin = window.localStorage.getItem('isAdmin') === 'true'
    rules = JSON.parse(window.localStorage.getItem("rules")).filter(item => item.service === 'redis').map(item => item.permissionType);
    permission(param: string) {
        return this.isAdmin || this.rules.includes(param)
    }
    isArchiveUser = window.localStorage.getItem('isArchiveUser')
    isLoading: boolean = false;
    keyword: string = '';
    redisList = [];
    busyStatus = {};
    ipAddressList = [];
    filters = {
        pageNum: 0 ,
        pageSize: environment.pageSize,
        orderBy1: false,
        orderName1: '',
        orderBy2: false,
        orderName2: '',
    };
    pager = {
        page: 1,
        pageSize: environment.pageSize,
        total: 0,
    };
    sortName = '';
    sortValue = false;
    openFlag:boolean = true;
    fristQuery:boolean = false;
    oldSortName;
    sort;
    index;

    // unfinishStatus = ['启动中', '部署中'];
    unfinishStatus = ['init'];
    pollingTimer = null;

    resLimit: boolean = false;
    showResLimit: boolean = environment.showResLimit[PRODUCT];

    changeModalVisible = false;
    serviceItem: FormGroup;
    mirrorItem: FormGroup;
    isChanging: boolean = false;

    ngOnInit() {
        this.getRedisList();
        this.initFbGroup();
    }

    initFbGroup(){
        this.serviceItem = this.fb.group({
            serviceName: '',
            cpuBefore: '',
            cpuAfter: '',
            memoryBefore: '',
            memoryAfter: '',
            diskBefore: '',
            diskAfter: '',
            instanceBefore: {},
            instanceAfter: {},
        });

        this.mirrorItem = this.fb.group({
            mirrorName: ['', {
                validators: [
                    Validators.required
                ]
            }],
        });
    }
    
    getRedisList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);
        if (keyword) {
            params.bean = {
                name: keyword
            }
        }
        this.isLoading = true;
        this.redisService.getRedisList(params)
            .then(rs => {
                if (rs.success) {
                    let list = rs.data.dataList || [];
                    list.forEach(item => {
                        item.ipList = item.bootStrapIps ? item.bootStrapIps.split(',') : [];
                        this.getIpAddress(item);
                    });
                    this.redisList = list;
                    this.pager = {
                        page: rs.data.pageNum+1,
                        pageSize: rs.data.pageSize,
                        total: rs.data.recordCount,
                    };

                    if (environment.resLimit[PRODUCT]) {
                        if (rs.data.total >= LIMIT && environment.freeAccount.indexOf(localStorage.getItem('username')) === -1) {
                            // 如果个数超限 && 不在无视名单里， 则限制创建
                            this.resLimit = true;
                        } else {
                            this.resLimit = false;
                        }
                    }

                    this.setupPolling(this.redisList);
                } else {
                    this.msg.error(`获取Redis实例列表失败${rs.message ? ': ' + rs.message : ''}`);
                }

                this.isLoading = false;
            })
            .catch(err => {
                this.isLoading = false;
            });
    }

    // 设置部署进度轮询
    setupPolling(list = []) {
        let unfinish = list.filter(item => this.unfinishStatus.indexOf(item.redisStatus) > -1);
        if (unfinish.length) {
            if (!this.pollingTimer) {
                this.pollingTimer = setInterval(() => {
                }, 5000);
            }
        } else {
            // if (this.pollingTimer) {
            clearInterval(this.pollingTimer);
            this.pollingTimer = null;
            // }
        }
    }

    trackById(item) {
        return item.id;
    }

    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getRedisList();
    }

    search() {
        this.filters.pageNum = 1;
        this.getRedisList();
    }

    getBusyText(item): string {
        return BUSY_TEXT_MAP[this.busyStatus[item.id]] || '';
    }

    // 格式化状态
    getStatusText(status): string {
        return STATUS_MAP[String(status)] || '-';
    }

    getRedisStatusClass(item) {
        return STATUS_CLASS_MAP[item.redisStatus];
    }

    getIpAddress(item) {
        if (item.ipAddress) {
            item.ipAddressList = item.ipAddress.split(','||'，');
        }
    }

    // 变更
    canChange(item) {
        // let data = item.spVapp.updateOrderQuotaDetail || {};
        // let detailStatus = String(data.quotaDetailStatus).toLowerCase()
        // if(detailStatus === 'start') {
        //     return true;
        // }
        if(!item.spVapp.updateOrderQuotaDetail){
            return false;
        }
        return true;
    }

    showChangeService(item) {
        if (!this.canChange(item)) {
            return;
        }
        this.changeModalVisible = true;
        this.redisService.getUpdateConfigCloudServer(item.spVapp.updateOrderQuotaDetail.id)
        .then(rs => {
            if (rs.success) {
                this.serviceItem.value.instanceBefore = item.spVapp.vmList[0];
                this.serviceItem.value.instanceAfter = rs.data;
            } else {
                this.msg.error(`获取初期化数据失败${rs.message ? ': ' + rs.message : ''}`);
            }
        })
        .catch(err => {
            this.msg.error('获取初期化数据失败');
        });
    }

    handleCancelChange() {
        this.changeModalVisible = false;
    }

    changeService() {
        let data: any = {};
        let after = this.serviceItem.value.instanceAfter;
        let before = this.serviceItem.value.instanceBefore;
        // 变更后的CPU或内存任意一个小于变更前的时
        if(before.cpuNum > after.cpuUnit || before.memoryGB > after.memoryUnit || before.diskGB > after.diskUnit){
            let powerStatus = before.spVapp.vmList.forEach((item, index)=> {
                if(item.powerStatus != '"power_off"'){
                    return;
                }
            })
            this.msg.error('降低配置前，必须关机');
        }
        data.quotaDetailId = after.id;
        data.updateVappId = before.id;
        this.isChanging = true;
        this.redisService.updateRdeis(data)
        .then(rs => {
            if (rs.success) {
                this.msg.success('操作成功！');
                this.getRedisList();
                this.isChanging = false;
                this.changeModalVisible = false;
            } else {
                this.msg.error(`操作失败${rs.message ? ': ' + rs.message : ''}`);
                this.isChanging = false;
                this.changeModalVisible = false;
            }
        })
        .catch(err => {
            this.isChanging = false;
            this.changeModalVisible = false;
        })
    }

    viewDetail(item) {
        this.router.navigate(['../redis-detail', item.id], {
            relativeTo: this.route,
        });
    }

    // 删除
    canDeleteRedis(item) {
        // item.redisStatus是init和running时不能删除
        return item.redisStatus != 'init' && item.redisStatus !== 'running';
    }

    deleteRedis(item) {
        if (!this.canDeleteRedis(item)) {
            return;
        }
        this.busyStatus[item.id] = 'delete';
        this.redisService.deleteRedis(item.id)
            .then(rs => {
                if (rs.success) {
                    this.msg.success('Redis删除成功');
                    this.getRedisList();
                } else {
                    this.msg.error(`Redis删除失败${rs.message ? ': ' + rs.message : ''}`);
                }
                this.busyStatus[item.id] = '';
            })
            .catch(err => {
                this.busyStatus[item.id] = '';
            })
    }

    // 开机
    canPowerOnRedis(item) {
        // item.redisStatus是running和init时不能开机
        if(item.redisStatus) {
            return item.redisStatus != 'running' && item.redisStatus !== 'init';
        }
        return false;
    }

    powerOnRedis(item): void {
        if (!this.canPowerOnRedis(item)) {
            return;
        }
        this.busyStatus[item.id] = 'powerOn';
        this.redisService.powerOnCloudServer(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('开机处理提交成功');
                this.getRedisList();
            } else {
                this.msg.error(`开机处理提交失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('开机处理提交失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 关机
    canPowerOffRedis(item): boolean {
        // item.redisStatus是init和stop时不能关机
        if(item.redisStatus) {
            return item.redisStatus != 'init' && item.redisStatus !== 'stop';
        }
        return false;
    }

    powerOffRedis(item): void {
        if (!this.canPowerOffRedis(item)) {
            return;
        }
        this.busyStatus[item.id] = 'powerOff';
        this.redisService.powerOffCloudServer(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('关机处理提交成功');
                this.getRedisList();
            } else {
                this.msg.error(`关机处理提交失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('关机处理提交失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 重启
    canRebootRedis(item) {
        // item.redisStatus是init和stop时不能重启
        if(item.redisStatus) {
            return item.redisStatus != 'init' && item.redisStatus !== 'stop';
        }
        return false;
    }

    rebootRedis(item): void {
        if (!this.canRebootRedis(item)) {
            return;
        }
        this.busyStatus[item.id] = 'reboot';
        this.redisService.rebootCloudServer(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('重启处理提交成功');
                this.getRedisList();
            } else {
                this.msg.error(`重启处理提交失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('重启处理提交失败');
            this.busyStatus[item.id] = '';
        });
    }

    ngOnDestroy() {
        if (this.pollingTimer) {
            clearInterval(this.pollingTimer);
            this.pollingTimer = null;
        }
    }

    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
        if (this.index !== undefined && this.index !== '') {
            this.cols[this.index].allowSort = this.sort;
        }
        this.openFlag = false;
    }

    onParamsChange(params: NzTableQueryParams) {
        if (this.fristQuery) {
            if (this.openFlag) {
                var checkData = false;
                var getIndex
                var index = -1;
                params.sort.forEach(sortDate => {
                    index ++;
                    if(sortDate.value) {
                        this.sortName = sortDate.key;
                        if (sortDate.value === 'ascend') {
                            this.sort = sortDate.value;
                            this.sortValue = false;
                        } else {
                            this.sortValue = true;
                        }
                        checkData = true;
                        getIndex = index;
                    }
                })
                this.index = getIndex;
                if (checkData) {
                    var names = this.sortName.split(',');
                    if (names.length == 2) {
                        this.filters.orderBy2 = this.sortValue; 
                        this.filters.orderName2 = names[1];
                    } else {
                        this.filters.orderBy2 = this.sortValue; 
                        this.filters.orderName2 = '';
                    }
                    this.filters.orderBy1 = this.sortValue; 
                    this.filters.orderName1 = names[0];
                    this.getRedisList();
                } else {
                    this.filters.orderBy1 = false; 
                    this.filters.orderName1 = '';
                    this.filters.orderBy2 = false; 
                    this.filters.orderName2 = '';
                    this.getRedisList();
                }
            } else {
                this.openFlag = true;
            }
        } else {
            this.fristQuery = true;
        }
    }
}
