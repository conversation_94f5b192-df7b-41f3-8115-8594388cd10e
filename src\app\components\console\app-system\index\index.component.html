<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">企业项目</a></li>-->
<!--        <li>项目管理</li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入资源类型名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right">
                    <a nz-button nzType="primary" class="primary"
                       (click)="editWindow(null)">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建项目
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    项目管理
                </span>
            </div>
            <nz-table #tableList
                      [nzItemRender]="renderItemTemplate"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <th>项目名称</th>
                        <th>项目所有者</th>
                        <th>备注</th>
                        <th width="100px">状态</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.appSystem.name}}</td>
                        <td>{{data.appSystem.ownerName}}</td>
                        <td>{{data.appSystem.comment}}</td>
                        <td>
                            <nz-switch [(ngModel)]="data.appSystem.appSystemStatusBoolean" nzCheckedChildren="启用" [disabled]="data.appSystem.ownerId !== userId"
                                       nzUnCheckedChildren="禁用" (ngModelChange)="switchAppSystemStatus(data)">
                            </nz-switch>
                        </td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item" *ngIf="data.appSystem.ownerId === userId" (click)="editWindow(data);">
                                    <i nzTitle="修改"
                                       m nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="data.appSystem.ownerId === userId" (click)="editUser(data);">
                                    <i nzTooltipTitle="配置用户权限"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="user" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="data.appSystem.ownerId === userId" (click)="transferUser(data);">
                                    <i nzTooltipTitle="项目转让"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="user-switch" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="data.appSystem.ownerId === userId" (click)="appSystemQuota(data);">
                                    <i nzTooltipTitle="项目额度"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="pie-chart" nzTheme="outline"></i>
                                </div>
<!--                                <div class="on-table-action-item"-->
<!--                                    nz-popconfirm-->
<!--                                    nzTooltipContent="top"-->
<!--                                     [hidden]="busyStatus[data.id]"-->
<!--                                    nzTitle="确定要删除项目吗？"-->
<!--                                    (nzOnConfirm)="delete(data);">-->
<!--                                    <i nzTitle="删除"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        class="icon fa fa-trash-o"></i>-->
<!--                                </div>-->
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[data.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
<!--                                    {{ getBusyText(data) }}-->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="dialogWindow"
          nzTitle="创建项目"
          nzOkText="创建"
          (nzOnCancel)="handleCancel()"
          [nzWidth]="400"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submit()">
    <ng-container *nzModalContent>
    <form [formGroup]="projectForm" class="config-content sm">
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">项目名称</span>
                    <input type="text" formControlName="name" placeholder="请输入项目名称">
                    <div class="small tip label-padding">
                        项目名称不能为空
                    </div>
                </label>
                <div *ngIf="isDirty(projectForm.get('name'))" class="form-hint error">
                    <div *ngIf="projectForm.get('name').hasError('required')">
                        项目名称不能为空
                    </div>
                </div>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item">
                <label>
                    <span class="label-text">备注</span>
                    <textarea formControlName="comment" placeholder="请输入备注" rows="3"></textarea>
                </label>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="dialogUserWindow" nzTitle="项目成员"
          (nzOnCancel)="dialogUserWindow = false" [nzWidth]="650"
          [nzOkLoading]="isLoading" (nzOnOk)="submitUser()"
          [nzCancelLoading]="isLoading">
    <ng-container *nzModalContent>
        <div style="padding:25px 0 15px">
        <nz-row>
            <nz-col nzSpan="3">
                <span style="font-weight: bold;">项目名称：</span>
            </nz-col>
            <nz-col nzSpan="9">
                {{appSystem.name }}
            </nz-col>
            <nz-col nzSpan="3">
                <span style="font-weight: bold;">所有者：</span>
            </nz-col>
            <nz-col nzSpan="9">
                {{appSystem.ownerName }}
            </nz-col>
        </nz-row>
    </div>
    <div class="select-container transfer">
        <nz-transfer [nzListStyle]="{width: '300px',height: '400px'}"
            [nzDataSource]="transferUserList"
            nzShowSearch
            [nzDisabled]="isLoading"
            [nzRender]="renderItem"
            [nzTitles]="['未选', '已选']"
            (nzChange)="transferUserChange($event)">
            <ng-template #renderItem let-item>
                <span [title]="item.title" nzTooltipContent="bottom" nz-tooltip>{{ item.title }}</span>
            </ng-template>
        </nz-transfer>
    </div>
    </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="dialogTransferUserWindow" nzTitle="项目转让"
          (nzOnCancel)="dialogTransferUserWindow = false" [nzWidth]="500"
          [nzOkLoading]="isLoading" (nzOnOk)="transferOwnerConfirm()"
          [nzCancelLoading]="isLoading">
    <ng-container *nzModalContent>
        <div style="margin-top:25px">
            <nz-row>
                <nz-col nzSpan="5">
                    项目名称：
                </nz-col>
                <nz-col nzSpan="19">
                    {{appSystem.name }}
                    
                </nz-col>
            </nz-row>
        </div>
        <div style="margin:20px 0">
            <nz-row>
                <nz-col nzSpan="5">
                    所有者：
                </nz-col>
                <nz-col nzSpan="19">
                    {{appSystem.ownerName }}
                </nz-col>
            </nz-row>
        </div>
        <div style="margin-bottom: 15px">
            <nz-row>
                <nz-col nzSpan="5">
                    受让人：
                </nz-col>
                <nz-col nzSpan="19">
                    <nz-select style="width: 100%;" nzSize="small"
                               [(ngModel)]="transferUserId"
                               nzPlaceHolder="请选择要转让的用户"
                               nzShowSearch
                               nzAllowClear>
                        <nz-option *ngFor="let user of relationUserList"
                                   [nzValue]="user.value"
                                   [nzLabel]="user.label"
                                   [nzDisabled]="user.disabled">
                        </nz-option>
                    </nz-select>
                </nz-col>
            </nz-row>
        </div>

    </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="dialogQuotaWindow" nzTitle="项目额度"
          (nzOnCancel)="dialogQuotaWindow = false" [nzWidth]="800"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading">\
    <ng-container *nzModalContent>
    <div style="padding:25px 0 15px">
        <nz-row>
            <nz-col nzSpan="3">
                项目名称：
            </nz-col>
            <nz-col nzSpan="8">
                {{appSystem.name }}
            </nz-col>
            <nz-col nzSpan="3">
                所有者：
            </nz-col>
            <nz-col nzSpan="8">
                {{appSystem.ownerName }}
            </nz-col>
        </nz-row>
    </div>
<!--    <nz-card [nzLoading]="isLoading" nzHoverable>-->
        <nz-table #quotaData nzSize="small" [nzData]="quotaList" nzPageSize="999" nzShowPagination="false">
            <thead>
                <tr>
                    <th>服务类型</th>
                    <th>资源类型</th>
                    <th>已使用</th>
                    <th>已分配</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let data of quotaData.data">
                    <td>{{ data.serviceText }}</td>
                    <td>{{ data.resourceTypeText }}</td>
                    <td>{{ data.usedQuota }}</td>
                    <td>{{ data.quota }}</td>
                </tr>
            </tbody>
        </nz-table>
<!--    </nz-card>-->
    </ng-container>
</nz-modal>