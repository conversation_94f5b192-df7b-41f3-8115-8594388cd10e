const fs = require('fs');
const path = require('path');

// 递归获取所有路由模块文件
function getAllRoutingModules(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllRoutingModules(filePath, fileList);
    } else if (file.endsWith('-routing.module.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 修复路由模块文件中缺少 pathMatch 的重定向路由
function fixRoutingModule(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 使用正则表达式查找包含 redirectTo 但不包含 pathMatch 的路由配置
  const regex = /{\s*path:.*redirectTo:.*(?!\s*pathMatch).*}/gs;
  if (regex.test(content)) {
    // 使用正则表达式替换，在 redirectTo 后添加 pathMatch: 'full'
    content = content.replace(
      /({[\s\n]*path:[\s\n]*['"][^'"]*['"][\s\n]*,[\s\n]*redirectTo:[\s\n]*['"][^'"]*['"][\s\n]*)(,?[\s\n]*})/g, 
      '$1,\n        pathMatch: \'full\'$2'
    );
    
    modified = true;
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`已修复文件: ${filePath}`);
  }
  
  return modified;
}

// 主函数
function main() {
  const appDir = path.join('d:', 'workspace', 'aic', 'webui', 'src', 'app');
  const routingModules = getAllRoutingModules(appDir);
  
  let fixedCount = 0;
  
  routingModules.forEach(filePath => {
    if (fixRoutingModule(filePath)) {
      fixedCount++;
    }
  });
  
  console.log(`修复完成！共修复了 ${fixedCount} 个文件。`);
}

main();
