import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { MessageService } from 'src/app/service/console/utils/message.service';
import { environment } from 'src/environments/environment';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { FormBuilder, FormGroup, AbstractControl, Validators} from "@angular/forms";

const VDI_STATUS_MAP = {
    'ACTIVE': '运行中',
    'BUILD': '创建中',
    'REBUILD': '重装中',
    'SUSPENDED': '休眠中',
    'PAUSED': '已暂停',
    'STOPPED': '已停止',
    'SHUTOFF': '关机',
    'ERROR': '错误',
    'UNKNOWN': '未知'
};

const POWER_STATUS_MAP = {
    'ACTIVE': '开机',
    'STOPPED': '关机',
    'SHUTOFF': '关机',
    'SUSPENDED': '休眠',
    'PAUSED': '暂停',
    'ERROR': '错误',
    'UNKNOWN': '未知'
};

const BUSY_TEXT_MAP = {
    'powerOn': '开机中',
    'powerOff': '关机中',
    'reboot': '重启中',
    'delete': '删除中',
    'lock': '锁定中',
    'unlock': '解锁中',
    'resetStatus': '重置状态中'
};

@Component({
    selector: 'app-vdi',
    templateUrl: './vdi.component.html',
    styleUrls: ['./vdi.component.less']
})
export class VdiComponent implements OnInit, OnDestroy {
    // 虚拟机池信息
    poolId: string = '';
    poolName: string = '';

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private msg: MessageService,
        private fb: FormBuilder
    ) {}
    
    cols = [
        {
            title: '名称',
            ColumnKey: "name",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '8%',
            nzRight: false,
        },
        {
            title: '镜像',
            ColumnKey: "imageName",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '7%',
            nzRight: false,
        },
        {
            title: '用户名',
            ColumnKey: "account",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '5%',
            nzRight: false,
        },
        {
            title: 'IP地址',
            ColumnKey: "ipAddress",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '6%',
            nzRight: false,
        },
        {
            title: '配置',
            ColumnKey: "",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '6%',
            nzRight: false,
        },
        {
            title: '数据盘',
            ColumnKey: "disk",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '5%',
            nzRight: false,
        },
        {
            title: '桌面状态',
            ColumnKey: "vdiStatus",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '6%',
            nzRight: false,
        },
        {
            title: '任务',
            ColumnKey: "vdiTask",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '4%',
            nzRight: false,
        },
        {
            title: '电源',
            ColumnKey: "powerStatus",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '4%',
            nzRight: false,
        },
        {
            title: '重启还原',
            ColumnKey: "resetOnReboot",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '5%',
            nzRight: false,
        },
        {
            title: 'GPU',
            ColumnKey: "gpuSupport",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '4%',
            nzRight: false,
        },
        {
            title: '共享',
            ColumnKey: "share",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '4%',
            nzRight: false,
        },
        {
            title: 'VIP',
            ColumnKey: "vip",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '4%',
            nzRight: false,
        },
        {
            title: '锁定',
            ColumnKey: "locked",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '4%',
            nzRight: false,
        },
        {
            title: '主机',
            ColumnKey: "host",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '5%',
            nzRight: false,
        },
        {
            title: '组',
            ColumnKey: "group",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '4%',
            nzRight: false,
        },
        {
            title: '操作',
            ColumnKey: "",
            sortFlag: false,
            allowSort: '',
            width: '6%',
            showSort: false,
            nzRight: true,
        }
    ];

    isAdmin = window.localStorage.getItem('isAdmin') === 'true'
    isArchiveUser = window.localStorage.getItem('isArchiveUser')
    rules = JSON.parse(window.localStorage.getItem("rules") || '[]').filter(item => item.service === 'vdi').map(item => item.permissionType);
    
    isLoading: boolean = false;
    keyword: string = '';
    vdiList = [];
    busyStatus = {};

    pollingTimer;
    
    // 分页
    filters = {
        pageNum: 0 ,
        pageSize: environment.pageSize,
        orderBy1: false,
        orderName1: '',
        orderBy2: false,
        orderName2: '',
    };
    pager = {
        page: 1,
        pageSize: environment.pageSize,
        total: 0,
    };
    sortName = '';
    sortValue = false;
    openFlag:boolean = true;
    fristQuery:boolean = false;
    sort;
    index;

    ngOnInit() {
        // 获取查询参数
        this.route.queryParams.subscribe(params => {
            this.poolId = params['poolId'] || '';
            this.poolName = params['poolName'] || '';
        });

        this.getVdiList();
        setInterval(()=> {
            this.getVdiList();
        }, 60000);
    }

    ngOnDestroy() {
        if (this.pollingTimer) {
            clearInterval(this.pollingTimer);
            this.pollingTimer = null;
        }
    }

    getVdiList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);
        if (keyword) {
            params.bean = {
                name: keyword
            }
        }

        this.isLoading = true;
        // TODO: 替换为实际的VDI服务调用
        // this.vdiService.getVdiList(params)
        // .then(rs => {
        //     if (rs.success) {
        //         this.vdiList = rs.data.dataList || [];
        //         this.pager = {
        //             page: rs.data.pageNum + 1,
        //             pageSize: environment.pageSize,
        //             total: rs.data.recordCount,
        //         };
        //     } else {
        //         this.msg.error(`获取桌面虚拟机列表失败${ rs.message ? ': ' + rs.message : '' }`);
        //     }
        //     this.isLoading = false;
        // })
        // .catch(err => {
        //     this.isLoading = false;
        // });
        
        // 模拟数据
        setTimeout(() => {
            this.vdiList = [
                {
                    id: 1,
                    name: 'VDI-001',
                    imageName: 'Windows 10',
                    account: 'user01',
                    ipAddress: '*************',
                    cpu: '4核',
                    memory: '8G',
                    disk: '100G',
                    vdiStatus: 'ACTIVE',
                    vdiTask: '无',
                    powerStatus: 'ACTIVE',
                    resetOnReboot: '是',
                    gpuSupport: '是',
                    share: '否',
                    vip: '否',
                    locked: '否',
                    host: 'host-01',
                    group: 'group-01'
                },
                {
                    id: 2,
                    name: 'VDC_ENU',
                    imageName: 'Unbuntu',
                    account: 'admin',
                    ipAddress: '***********',
                    cpu: '8核',
                    memory: '16G',
                    disk: '40G',
                    vdiStatus: 'ACTIVE',
                    vdiTask: '无',
                    powerStatus: 'ACTIVE',
                    resetOnReboot: '是',
                    gpuSupport: '是',
                    share: '否',
                    vip: '否',
                    locked: '否',
                    host: 'dmz-host',
                    group: '-'
                },
            ];
            this.pager = {
                page: 1,
                pageSize: environment.pageSize,
                total: 1,
            };
            this.isLoading = false;
        }, 1000);
    }

    trackById(item) {
        return item.id;
    }

    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getVdiList();
    }

    search() {
        this.filters.pageNum = 0;
        this.getVdiList();
    }

    refresh(){
        this.getVdiList();
    }

    // 返回虚拟机池列表
    backToPoolList(): void {
        this.router.navigate(['/console/system/vdi-pool']);
    }

    // 权限检查
    permission(type: string): boolean {
        return this.rules.includes(type) || this.isAdmin;
    }

    // 获取状态文本
    getVdiStatusText(item): string {
        return VDI_STATUS_MAP[item.vdiStatus] || '-';
    }

    getPowerStatusText(item): string {
        return POWER_STATUS_MAP[item.powerStatus] || '-';
    }

    getBusyText(item): string {
        return BUSY_TEXT_MAP[this.busyStatus[item.id]] || '';
    }

    // 操作方法
    canPowerOn(item): boolean {
        return item.powerStatus === 'STOPPED' || item.powerStatus === 'SHUTOFF' || item.powerStatus === 'SUSPENDED' || item.powerStatus === 'PAUSED';
    }

    canPowerOff(item): boolean {
        return item.powerStatus === 'ACTIVE';
    }

    canReboot(item): boolean {
        return item.powerStatus === 'ACTIVE';
    }

    canDelete(item): boolean {
        return item.powerStatus === 'STOPPED' || item.powerStatus === 'SHUTOFF';
    }

    canRemoteDesktop(item): boolean {
        return item.powerStatus === 'ACTIVE';
    }

    // 操作按钮事件
    powerOn(item): void {
        if (!this.canPowerOn(item)) return;
        this.busyStatus[item.id] = 'powerOn';
        // TODO: 实现开机逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('开机操作已提交');
            this.getVdiList();
        }, 2000);
    }

    powerOff(item): void {
        if (!this.canPowerOff(item)) return;
        this.busyStatus[item.id] = 'powerOff';
        // TODO: 实现关机逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('关机操作已提交');
            this.getVdiList();
        }, 2000);
    }

    reboot(item): void {
        if (!this.canReboot(item)) return;
        this.busyStatus[item.id] = 'reboot';
        // TODO: 实现重启逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('重启操作已提交');
            this.getVdiList();
        }, 2000);
    }

    remoteDesktop(item): void {
        if (!this.canRemoteDesktop(item)) return;
        // TODO: 实现远程桌面逻辑
        this.msg.info('正在连接远程桌面...');
    }

    enableVip(item): void {
        // TODO: 实现开启VIP逻辑
        this.msg.info('开启VIP功能');
    }

    setShare(item): void {
        // TODO: 实现设置共享逻辑
        this.msg.info('设置共享功能');
    }

    bindUser(item): void {
        // TODO: 实现绑定用户逻辑
        this.msg.info('绑定用户功能');
    }

    setResetOnReboot(item): void {
        // TODO: 实现设置重启还原逻辑
        this.msg.info('设置重启还原功能');
    }

    snapshot(item): void {
        // TODO: 实现快照逻辑
        this.msg.info('创建快照功能');
    }

    delete(item): void {
        if (!this.canDelete(item)) return;
        this.busyStatus[item.id] = 'delete';
        // TODO: 实现删除逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('删除操作已提交');
            this.getVdiList();
        }, 2000);
    }

    lock(item): void {
        this.busyStatus[item.id] = 'lock';
        // TODO: 实现锁定逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('锁定操作已提交');
            this.getVdiList();
        }, 2000);
    }

    unlock(item): void {
        this.busyStatus[item.id] = 'unlock';
        // TODO: 实现解锁逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('解锁操作已提交');
            this.getVdiList();
        }, 2000);
    }

    resetStatus(item): void {
        this.busyStatus[item.id] = 'resetStatus';
        // TODO: 实现重置状态逻辑
        setTimeout(() => {
            this.busyStatus[item.id] = '';
            this.msg.success('重置状态操作已提交');
            this.getVdiList();
        }, 2000);
    }

    // 表格排序
    onParamsChange(params: NzTableQueryParams): void {
        // TODO: 实现排序逻辑
    }

    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map((e, index) => (e.title === col ? { ...e, width: `${width}px` } : e));
    }
}
