<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">云服务器</a></li>-->
<!--        <li><span>备份</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">备份</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="keyword" nz-input
                            placeholder="请输入备份名称" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <div class="pull-right">
                    <button nz-button nzType="primary"
                        (click)="backupModalVisible = true">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建备份
                    </button>
                </div>
            </div>
            <nz-table #backups
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="backupList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width">
                              {{ col.title }}
                            </th>
                        </ng-container>
                        <!-- <th width="35%" nzColumnKey="backupName" [nzSortFn]="true">备份名称</th>
                        <th width="25%" nzColumnKey="backupVmName" [nzSortFn]="true">云服务器</th>
                        <th nzColumnKey="backupSequence" [nzSortFn]="true">序号</th>
                        <th nzColumnKey="createTm" [nzSortFn]="true">创建时间</th>
                        <th width="15%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of backups.data; trackBy: trackById">
                        <td>{{item.backupName}}</td>
                        <td>{{item.backupVmName}}</td>
                        <td>{{item.backupSequence}}</td>
                        <td>{{item.createTm}}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="showRestoreConfirm(item)">
                                    <i nzTooltipTitle="还原"
                                    nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-history"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要删除该备份吗？"
                                    (nzOnConfirm)="deleteBackup(item);">
                                    <i nzTooltipTitle="删除"
                                    nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="backups.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-backup-strategy
    [isVisible]="backupModalVisible"
    [strategyId]="currentStrategyId"
    [cloudServer]="currentCloudServer"
    (close)="backupModalVisible = false"
    (submit)="getBackupList()">
</app-backup-strategy>

<app-restore-confirm [isVisible]="restoreModalVisible"
    type="backup" [cloudServer]="restoreCloudServer"
    [restoreItem]="currentRestoreItem"
    (close)="restoreModalVisible = false"
    (submit)="restoreBackup(currentRestoreItem)">
</app-restore-confirm>