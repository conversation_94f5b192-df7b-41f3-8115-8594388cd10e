const { EventEmitter } = require('events');

// 设置 EventEmitter 默认最大监听器数量
EventEmitter.defaultMaxListeners = 30;

// 设置进程最大监听器数量
if (typeof process !== 'undefined' && process.setMaxListeners) {
  process.setMaxListeners(30);
}

// 导入 Angular CLI 的开发服务器
const ngDevServer = require('@angular/cli/lib/commands/serve/cli');

// 启动开发服务器前的钩子
process.on('beforeExit', () => {
  console.log('服务器即将退出，清理资源...');
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
});

// 启动 Angular 开发服务器
ngDevServer({
  $0: 'ng',
  _: ['serve'],
  port: 4200,
  'proxy-config': 'proxy.conf.js'
})
  .then(() => {
    console.log('Angular 开发服务器已启动');
  })
  .catch((err) => {
    console.error('启动 Angular 开发服务器时出错:', err);
    process.exit(1);
  });
