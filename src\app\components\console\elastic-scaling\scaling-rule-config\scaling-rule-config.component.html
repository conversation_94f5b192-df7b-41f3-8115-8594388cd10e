<nz-modal [(nzVisible)]="isVisible"
    [nzTitle]="ruleId ? '编辑伸缩策略' : '添加伸缩策略'"
    [nzOkText]="ruleId ? '保存' : '添加'"
    [nzMaskClosable]="false"
    [nzBodyStyle]="{padding: 0}"
    (nzAfterOpen)="ruleModalOpened()"
    (nzOnCancel)="handleCancel()"
    (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <form [formGroup]="scalingRule" class="config-content sm">
        <section class="field-section">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">策略名称</span>
                        <input type="text" formControlName="ruleName" placeholder="请输入策略名称">
                    </label>
                    <div *ngIf="isDirty(scalingRule.get('ruleName'))" class="form-hint error">
                        <div *ngIf="scalingRule.get('ruleName').hasError('required')">
                            策略名称不能为空
                        </div>
                        <div *ngIf="scalingRule.get('ruleName').hasError('maxlength')">
                            策略名称长度不能超过{{ scalingRule.get('ruleName').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="field-section">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">监控指标</span>
                        <nz-select formControlName="monitorMetric" nzPlaceHolder="请选择监控指标">
                            <nz-option *ngFor="let item of ruleInitData.MonitorMetric | keyvalue" [nzValue]="item.key"
                                [nzLabel]="item.value"></nz-option>
                        </nz-select>
                    </label>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">低阈值(%)</span>
                        <input type="text" formControlName="minThreshold" placeholder="请输入监控指标的低阈值">
                    </label>
                    <div *ngIf="isDirty(scalingRule.get('minThreshold'))" class="form-hint error">
                        <div *ngIf="scalingRule.get('minThreshold').hasError('required')">
                            低阈值不能为空
                        </div>
                        <div *ngIf="scalingRule.get('minThreshold').hasError('pattern')">
                            请输入正确的数字
                        </div>
                        <div *ngIf="scalingRule.get('minThreshold').hasError('min')">
                            低阈值不能低于{{ scalingRule.get('minThreshold').errors.min.min }}%
                        </div>
                        <div *ngIf="scalingRule.get('minThreshold').hasError('max')">
                            低阈值至多为{{ scalingRule.get('minThreshold').errors.max.max }}%
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">高阈值(%)</span>
                        <input type="text" formControlName="maxThreshold" placeholder="请输入监控指标的高阈值">
                    </label>
                    <div *ngIf="isDirty(scalingRule.get('maxThreshold'))" class="form-hint error">
                        <div *ngIf="scalingRule.get('maxThreshold').hasError('required')">
                            高阈值不能为空
                        </div>
                        <div *ngIf="scalingRule.get('maxThreshold').hasError('pattern')">
                            请输入正确的数字
                        </div>
                        <div *ngIf="(scalingRule.errors && scalingRule.errors.maxError)">
                            高阈值不能低于低阈值
                        </div>
                        <div *ngIf="scalingRule.get('maxThreshold').hasError('max')">
                            高阈值至多为{{ scalingRule.get('maxThreshold').errors.max.max }}%
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="field-section">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">监控周期(分钟)</span>
                        <nz-select formControlName="referencePeriod" nzPlaceHolder="请选择监控周期">
                            <nz-option *ngFor="let item of ruleInitData.ReferencePeriod" [nzValue]="item" [nzLabel]="item"></nz-option>
                        </nz-select>
                    </label>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">连续出现次数
                            <i nz-icon
                                nz-tooltip
                                nzTitle="连续出现次数指监控指标超过您的阈值设定多少次后，才会触发伸缩活动。"
                                nzType="question-circle"
                                theme="outline"></i>
                        </span>
                        <input type="text" formControlName="triggerAfter" placeholder="请输入连续出现次数">
                    </label>
                    <div *ngIf="isDirty(scalingRule.get('triggerAfter'))" class="form-hint error">
                        <div *ngIf="scalingRule.get('triggerAfter').hasError('required')">
                            连续出现次数不能为空
                        </div>
                        <div *ngIf="scalingRule.get('triggerAfter').hasError('pattern')">
                            请输入正确的数字
                        </div>
                        <div *ngIf="scalingRule.get('triggerAfter').hasError('min')">
                            连续出现次数不能低于{{ scalingRule.get('triggerAfter').errors.min.min }}次
                        </div>
                        <div *ngIf="scalingRule.get('triggerAfter').hasError('max')">
                            连续出现次数至多为{{ scalingRule.get('triggerAfter').errors.max.max }}次
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">冷却时间(秒)
                            <i nz-icon
                                nz-tooltip
                                nzTitle="冷却时间指执行完一次伸缩后，在您设置的冷却时间内都不会再次执行伸缩策略导致的伸缩活动。如果不填写则使用伸缩组默认冷却时间。"
                                nzType="question-circle"
                                theme="outline"></i>
                        </span>
                        <input type="text" formControlName="cooldownTime" placeholder="请输入冷却时间">
                    </label>
                    <div *ngIf="isDirty(scalingRule.get('cooldownTime'))" class="form-hint error">
                        <div *ngIf="scalingRule.get('cooldownTime').hasError('pattern')">
                            请输入正确的冷却时间
                        </div>
                        <div *ngIf="scalingRule.get('cooldownTime').hasError('min')">
                            冷却时间不能低于{{ scalingRule.get('cooldownTime').errors.min.min }}秒
                        </div>
                        <div *ngIf="scalingRule.get('cooldownTime').hasError('max')">
                            冷却时间不能高于{{ scalingRule.get('cooldownTime').errors.max.max }}秒
                        </div>
                    </div>
                </div>
                <p class="small tip" *ngIf="!ruleId">
                    <i class="fa fa-exclamation-circle"></i>
                    新建策略为未启用状态，请手动启用
                </p>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>