// BPMN.js 中文翻译模块
import bpmn from 'bpmn-js-i18n-zh/lib/bpmn-js';
import properties from 'bpmn-js-i18n-zh/lib/properties-panel';
import camunda from 'bpmn-js-i18n-zh/lib/camunda-properties-panel';
import zeebe from 'bpmn-js-i18n-zh/lib/zeebe-properties-panel';

// 合并所有翻译资源
const zhCN = {
  ...bpmn,
  ...properties,
  ...camunda,
  ...zeebe,
  // 可以在这里添加自定义翻译内容
  'Create StartEvent': '创建开始事件',
  'Create EndEvent': '创建结束事件',
  'Create Task': '创建任务',
  'Create UserTask': '创建用户任务',
  'Create ServiceTask': '创建服务任务',
  'Create Gateway': '创建网关',
  'Create ExclusiveGateway': '创建排他网关',
  'Create ParallelGateway': '创建并行网关',
  'Create InclusiveGateway': '创建包容网关',
  'Create SubProcess': '创建子流程',
  'Create Pool/Participant': '创建池/参与者',
  'Create Lane': '创建泳道',
  'Create DataObject': '创建数据对象',
  'Create DataStore': '创建数据存储',
  'Create Group': '创建组',
  'Create TextAnnotation': '创建文本注释',
  'Append Task': '追加任务',
  'Append UserTask': '追加用户任务',
  'Append ServiceTask': '追加服务任务',
  'Append Gateway': '追加网关',
  'Append EndEvent': '追加结束事件',
  'Change type': '更改类型',
  'Connect using Sequence/MessageFlow or Association': '使用顺序流/消息流或关联连接',
  'Remove': '删除',
  'Activate the hand tool': '激活手型工具',
  'Activate the lasso tool': '激活套索工具',
  'Activate the create/remove space tool': '激活创建/删除空间工具',
  'Activate the global connect tool': '激活全局连接工具',
  'Edit label': '编辑标签',
  'Add Lane above': '在上方添加泳道',
  'Add Lane below': '在下方添加泳道',
  'Divide into two Lanes': '分为两个泳道',
  'Divide into three Lanes': '分为三个泳道',
  'Replace with expanded SubProcess': '替换为展开的子流程',
  'Replace with collapsed SubProcess': '替换为折叠的子流程',
  'Replace with EventSubProcess': '替换为事件子流程'
};

/**
 * 自定义翻译函数
 * @param template 翻译模板
 * @param replacements 替换参数
 * @returns 翻译后的文本
 */
export function customTranslate(template: string, replacements?: Record<string, any>): string {
  replacements = replacements || {};

  // 翻译
  template = zhCN[template] || template;

  // 替换参数
  return template.replace(/{([^}]+)}/g, function (_, key) {
    return replacements[key] || '{' + key + '}';
  });
}

// 导出翻译模块
export default {
  translate: ['value', customTranslate]
};
