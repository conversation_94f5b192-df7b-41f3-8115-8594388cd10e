<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../monitor-manage">云监控</a></li>-->
<!--        <li><span>告警管理</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">告警管理</h3>
        </div>
        <div class="details-container">
            <!--内容菜单-->
            <div class="details-menu">
                <nz-tabset [nzTabPosition]="'top'" [nzType]="'card'" (nzSelectChange)="selectMenu($event)"
                    [nzSelectedIndex]="activeContentIndex">
                    <nz-tab *ngFor="let tab of contentMenuOptions" [nzTitle]="tab.title">
                    </nz-tab>
                </nz-tabset>
            </div>
            <!-- 告警症状 -->
            <div class="content-body-item" *ngIf="activeContentIndex === 0">
                <!--内容头部-->
                <div class="header">
                    <p>告警症状</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="createSymptom()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            新增告警症状
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #symptom [nzBordered]=true [nzLoading]=tableLoading [nzPageSize]=99999
                    [nzShowPagination]=false [nzData]="symptomList">
                    <thead>
                        <tr>
                            <th width="20%">名称</th>
                            <th>告警等级</th>
                            <th>监控指标</th>
                            <th>更多操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of symptom.data">
                            <td>{{ item.symptomDefName || '-' }}</td>
                            <td>
                                <i class="fa fa-warning {{getStatusClass(item)}}"></i>
                                <span
                                    class="margin-span">{{ item.symptomDefinition ? getDegreeText(item) : '-' }}</span>
                            </td>
                            <td>
                                <span class="margin-span">{{item.metricName || '-'}}</span>
                                <span
                                    class="margin-span">{{ item.symptomDefinition ? getOperatorText(item) : '-' }}</span>
                                <span class="margin-span">{{ item.symptomDefinition ? getNumber(item) : '-' }}</span>
                            </td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createSymptom(item)">
                                        <i nzTitle="修改" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top"
                                        nzTitle="确定要删除该告警症状吗？" (nzOnConfirm)="deleteSymptom(item)">
                                        <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!-- 告警定义 -->
            <div class="content-body-item" *ngIf="activeContentIndex === 1">
                <!--内容头部-->
                <div class="header">
                    <p>告警定义</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="createAlertDefine()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            新增告警定义
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #alertDef [nzBordered]=true [nzPageSize]=99999 [nzLoading]=tableLoading
                    [nzShowPagination]=false [nzData]="alertDefineList">
                    <thead>
                        <tr>
                            <th width="20%">名称</th>
                            <th>监控等级</th>
                            <th width="20%">监控对象</th>
                            <th>等待周期</th>
                            <th>取消周期</th>
                            <th>创建时间</th>
                            <th>更多操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of alertDef.data">
                            <td>{{ item.alertDefName || '-' }}</td>
                            <td>
                                <i class="fa fa-warning {{getStatusClass(item)}}"></i>
                                <span class="margin-span">{{ item.states ? getDegreeText(item) : '-' }}</span>
                            </td>
                            <td *ngIf="item.resources">
                                <p *ngFor="let resouce of item.resources">
                                    {{resouce.resourceName || '-'}}
                                </p>
                            </td>
                            <td *ngIf="!item.resources">-</td>
                            <td>{{ item.waitCycles || '-' }}</td>
                            <td>{{ item.cancelCycles || '-' }}</td>
                            <td>{{ getdate(item.createTime) || '-' }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createAlertDefine(item, 'detail')">
                                        <i nzTitle="查看详情" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-search"></i>
                                    </div>
                                    <div class="on-table-action-item" (click)="createAlertDefine(item)">
                                        <i nzTitle="修改" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top"
                                        nzTitle="确定要删除该告警定义吗？" (nzOnConfirm)="deleteAlertDef(item)">
                                        <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!-- 告警规则 -->
            <div class="content-body-item" *ngIf="activeContentIndex === 2">
                <!--内容头部-->
                <div class="header">
                    <p>告警规则</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="createNotification()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            新增告警规则
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #symptom [nzBordered]=true [nzPageSize]=99999 [nzShowPagination]=false
                    [nzData]="notificationList">
                    <thead>
                        <tr>
                            <th width="20%">名称</th>
                            <th>告警定义</th>
                            <th>联系组</th>
                            <th>描述</th>
                            <th>更多操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of symptom.data">
                            <td>{{ item.name || '-' }}</td>
                            <td>{{ item.alertDefinitionNames.join('，') || '-' }}</td>
                            <td>{{ item.groupName || '-' }}</td>
                            <td>{{ item.remark || '-' }}</td>

                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createNotification(item)">
                                        <i nzTitle="修改" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top"
                                        nzTitle="确定要删除该告警规则吗？" (nzOnConfirm)="deleteNotification(item)">
                                        <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!-- 联系组 -->
            <div class="content-body-item" *ngIf="activeContentIndex === 3">
                <!--内容头部-->
                <div class="header">
                    <p>联系组</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="createMemberGroup()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            新增联系组
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #memberGroup [nzBordered]=true [nzPageSize]=99999 [nzShowPagination]=false
                    [nzData]="memberGroupList">
                    <thead>
                        <tr>
                            <th width="20%">名称</th>
                            <th width="20%">成员</th>
                            <th width="20%">备注</th>
                            <th>创建时间</th>
                            <th>更多操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of memberGroup.data">
                            <td>{{ item.name || '-' }}</td>
                            <td>{{ analysisMembersArrary(item.members) }}</td>
                            <td>{{ item.remark || '-' }}</td>
                            <td>{{ getdate(item.createTime) || '-' }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createMemberGroup(item)">
                                        <i nzTitle="修改" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top"
                                        nzTitle="确定要删除该联系组吗？" (nzOnConfirm)="deleteMemberGroup(item)">
                                        <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!-- 消息接收 -->
            <div class="content-body-item" *ngIf="activeContentIndex === 4">
                <!--内容头部-->
                <div class="header">
                    <p>消息接收</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="createMember()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            新增消息接收
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #member [nzBordered]=true [nzPageSize]=99999 [nzShowPagination]=false [nzData]="memberList">
                    <thead>
                        <tr>
                            <th width="20%">名称</th>
                            <th width="20%">邮箱</th>
                            <th>电话</th>
                            <th width="20%">备注</th>
                            <th>创建时间</th>
                            <th>更多操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of member.data">
                            <td>{{ item.memName || '-' }}</td>
                            <td>{{ item.email || '-' }}</td>
                            <td>{{ item.phone || '-' }}</td>
                            <td>{{ item.remark || '-' }}</td>
                            <td>{{ getdate(item.createTime) || '-' }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createMember(item)">
                                        <i nzTitle="修改" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top"
                                        nzTitle="确定要删除该联系人吗？" (nzOnConfirm)="deleteMember(item)">
                                        <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
        </div>
    </div>
</div>
<!-- 创建告警症状 -->
<nz-modal [(nzVisible)]="showCreateSymptomWindow" [nzWidth]=750 [nzTitle]="showCreateSymptomWindowTitle"
    (nzOnCancel)="showCreateSymptomWindow = false" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading"
    (nzOnOk)="submitCreateSymptom()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">名称：</span>
        <input nz-input style="width: 550px;" [(ngModel)]="symptomName" />
    </div>
    <div class="select-container">
        <span class="select-tips">监控指标：</span>
        <nz-collapse class="collapse">
            <nz-collapse-panel *ngFor="let panel of createMetricPanels; index as i" [nzHeader]="panel.name"
                [nzActive]="panel.active">
                <nz-select style="width: 150px" nzPlaceHolder="请选择" (ngModelChange)="createMetricTypeChange($event, i)"
                    [(ngModel)]="panel.selectMetricTypeValue">
                    <nz-option *ngFor="let select1 of panel.metricTypeOption" nzCustomContent
                        [nzLabel]="select1.categoryName" [nzValue]="select1.categoryKey">
                        <span [title]="select1.categoryName" nzTooltipContent="bottom"
                            nz-tooltip>{{select1.categoryName}}</span>
                    </nz-option>
                </nz-select>
                <nz-select style="width: 170px; margin: 0 5px" nzPlaceHolder="请选择"
                    (ngModelChange)="createMetricChange($event, i)" [(ngModel)]="panel.selectMetricValue">
                    <nz-option *ngFor="let select2 of panel.metricOption" nzCustomContent [nzLabel]="select2.name"
                        [nzValue]="select2.key">
                        <span [title]="select2.name" nzTooltipContent="bottom" nz-tooltip>{{select2.name}}</span>
                    </nz-option>
                </nz-select>
                <nz-select style="width: 65px; margin: 0 5px" nzPlaceHolder="请选择"
                    [(ngModel)]="panel.selectOperatorValue">
                    <nz-option *ngFor="let select2 of operatorOption" [nzLabel]="select2.name"
                        [nzValue]="select2.value">
                    </nz-option>
                </nz-select>
                <nz-input-number [nzPrecision]=0 [(ngModel)]="panel.settingValue" style="width: 65px;" [nzMin]="1"
                    [nzMax]="10" [nzStep]="1">
                </nz-input-number>
                <span style="font-size: 12px; margin-left: 5px">{{panel.unit}}</span>
            </nz-collapse-panel>
        </nz-collapse>
    </div>
    <div class="select-container">
        <span class="select-tips">监控等级：</span>
        <nz-select style="width: 550px;" nzShowSearch nzAllowClear [(ngModel)]="selectDegree">
            <nz-option nzCustomContent nzLabel="警告" nzValue="WARNING">
                <i class="fa fa-warning warning"></i> 警告
            </nz-option>
            <nz-option nzCustomContent nzLabel="紧急" nzValue="IMMEDIATE">
                <i class="fa fa-warning immediate"></i> 紧急
            </nz-option>
            <nz-option nzCustomContent nzLabel="严重" nzValue="CRITICAL">
                <i class="fa fa-warning critical"></i> 严重
            </nz-option>
        </nz-select>
    </div>
	</ng-container>
</nz-modal>
<!-- 创建告警定义 -->
<nz-modal [(nzVisible)]="showCreateAlertDefWindow" [nzWidth]=600 [nzTitle]="showCreateAlertDefWindowTitle"
    (nzOnCancel)="showCreateAlertDefWindow = false" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading"
    [nzCancelText]="showAlertDefDetailWindow ? null : '取消'" [nzOkText]="showAlertDefDetailWindow ? '关闭' : '确定'"
    (nzOnOk)="showAlertDefDetailWindow ? (showCreateAlertDefWindow = false) : submitCreateAlertDef()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">名称：</span>
        <input nz-input [disabled]="showAlertDefDetailWindow || isLoading" style="width: 400px;"
            [(ngModel)]="alertDefName" />
    </div>
    <div class="select-container">
        <span class="select-tips" style="vertical-align: top">告警症状：</span>
        <div style="display: inline-block">
            <div class="operator-box" *ngIf="createAlertDefPanels.length>1">
                <span>匹配以下症状集中的</span>
                <nz-select style="width: 100px;margin: 0 10px" [disabled]="showAlertDefDetailWindow || isLoading"
                    nzPlaceHolder="请选择" [(ngModel)]="symptomGatherOperator">
                    <nz-option *ngFor="let operator of createAlertDefOperatorOption" [nzLabel]="operator.name"
                        [nzValue]="operator.value">
                    </nz-option>
                </nz-select>
                <span>个</span>
            </div>
            <nz-collapse class="collapse" style="width: 400px">
                <nz-collapse-panel *ngFor="let panel of createAlertDefPanels; index as i1" [nzHeader]="'症状集' + (i1 + 1)"
                    [nzExtra]="extraTpl" [nzActive]="panel.active">
                    <div class="operator-box" *ngIf="panel.symptomGather.length>1">
                        <span>此症状集为真的条件是出现</span>
                        <nz-select style="width: 100px;margin: 0 10px" nzPlaceHolder="请选择"
                            [disabled]="showAlertDefDetailWindow || isLoading" [(ngModel)]="panel.symptomOperator">
                            <nz-option *ngFor="let operator of createAlertDefOperatorOption" [nzLabel]="operator.name"
                                [nzValue]="operator.value">
                            </nz-option>
                        </nz-select>
                        <span>以下症状</span>
                    </div>
                    <div *ngFor="let item of panel.symptomGather; index as i2">
                        <nz-select style="width: 300px;margin: 10px 0" nzPlaceHolder="请选择告警症状"
                            [disabled]="showAlertDefDetailWindow || isLoading" [(ngModel)]="item.symptomValue">
                            <nz-option *ngFor="let select1 of symptomList; index as i3" nzCustomContent
                                [nzLabel]="select1.symptomDefName" [nzValue]="select1.symptomDefId">
                                <span [title]="select1.symptomDefName" nzTooltipContent="bottom"
                                    nz-tooltip>{{select1.symptomDefName}}</span>
                            </nz-option>
                        </nz-select>
                        <i class="fa fa-times-circle" *ngIf="!showAlertDefDetailWindow" nzTitle="删除症状"
                            style="margin-left: 10px; cursor: pointer;" nzTooltipContent="bottomCenter" nz-tooltip
                            (click)="deleteCreateAlertDefSymptom(i1, i2)">
                        </i>
                    </div>
                    <p class="choosable-add-text" *ngIf="!showAlertDefDetailWindow"
                        (click)="addCreateAlertDefSymptom(i1)">
                        <i class="fa fa-plus-circle"></i>
                        <span>添加症状</span>
                    </p>
                    <ng-template #extraTpl>
                        <i class="fa fa-times-circle" *ngIf="!showAlertDefDetailWindow" nzTitle="删除症状集"
                            nzTooltipContent="bottomCenter" nz-tooltip (click)="deleteCreateAlertDefSymptomGather(i1)">
                        </i>
                    </ng-template>
                </nz-collapse-panel>
            </nz-collapse>
            <p class="choosable-add-text" *ngIf="!showAlertDefDetailWindow" (click)="addCreateAlertDefSymptomGather()">
                <i class="fa fa-plus-circle"></i>
                <span>添加症状集</span>
            </p>
        </div>

    </div>
    <div class="select-container">
        <span class="select-tips" style="vertical-align: top">监控对象：</span>
        <nz-transfer [nzDataSource]="bindCloudServerList" nzShowSearch
            [nzDisabled]="showAlertDefDetailWindow || isLoading" [nzRender]="render" [nzTitles]="['未选', '已选']"
            (nzChange)="changeDefineCloudServer($event)">
            <ng-template #render let-item>
                <span [title]="item.title" nzTooltipContent="bottom" nz-tooltip>{{ item.title }}
                </span>
            </ng-template>
        </nz-transfer>
    </div>
    <div class="select-container">
        <span class="select-tips">监控等级：</span>
        <nz-select style="width: 400px;" [disabled]="showAlertDefDetailWindow || isLoading" nzShowSearch nzAllowClear
            [(ngModel)]="createDefSelectDegree">
            <nz-option nzCustomContent nzLabel="警告" nzValue="WARNING">
                <i class="fa fa-warning warning"></i> 警告
            </nz-option>
            <nz-option nzCustomContent nzLabel="紧急" nzValue="IMMEDIATE">
                <i class="fa fa-warning immediate"></i> 紧急
            </nz-option>
            <nz-option nzCustomContent nzLabel="严重" nzValue="CRITICAL">
                <i class="fa fa-warning critical"></i> 严重
            </nz-option>
        </nz-select>
    </div>
    <div class="select-container" style="width: 250px;display: inline-block;">
        <span class="select-tips">等待周期：</span>
        <nz-input-number [nzPrecision]=0 [(ngModel)]="createDefWaitcyle"
            [disabled]="showAlertDefDetailWindow || isLoading" [nzMin]="1" [nzStep]="1"></nz-input-number>
    </div>
    <div class="select-container" style="width: 250px;display: inline-block;">
        <span class="select-tips">取消周期：</span>
        <nz-input-number [nzPrecision]=0 [(ngModel)]="createDefCancelcycle"
            [disabled]="showAlertDefDetailWindow || isLoading" [nzMin]="1" [nzStep]="1"></nz-input-number>
    </div>
    </ng-container>
</nz-modal>
<!-- 创建告警规则 -->
<nz-modal [(nzVisible)]="showCreateNotificationWindow" [nzTitle]="showCreateNotificationWindowTitle" [nzWidth]="600"
    (nzOnCancel)="showCreateNotificationWindow = false" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading"
    (nzOnOk)="submitCreateNotification()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">名称：</span>
        <input nz-input maxlength="64" style="width: 300px;" (blur)="checkNotificationName()" [(ngModel)]="notificationName" />
        <p class="warning-tips" *ngIf="!notificationNameCheck" style="width: 406px">
            *长度为3-64个字符之间(含)</p>
    </div>
    <div class="select-container">
        <span class="select-tips">告警定义：</span>
        <nz-select style="width: 300px" nzPlaceHolder="请选择" [(ngModel)]="selectAlertDefValue">
            <nz-option *ngFor="let item of alertDefineList" [nzLabel]="item.alertDefName" [nzValue]="item.alertDefId">
            </nz-option>
        </nz-select>
    </div>
    <div class="select-container">
        <span class="select-tips">联系组：</span>
        <nz-select style="width: 300px" nzPlaceHolder="请选择" [(ngModel)]="selectMemberGroupValue">
            <nz-option *ngFor="let item of memberGroupList" [nzLabel]="item.name" [nzValue]="item.id">
            </nz-option>
        </nz-select>
    </div>
    <div class="select-container">
        <span class="select-tips">备注：</span>
        <textarea style="width: 300px; height: 120px" nz-input rows="2" (input)="checkNotificationRemark()"
            [(ngModel)]="notificationRemark" placeholder="备注(长度200字符以内)">
        </textarea>
        <p class="warning-tips" *ngIf="!notificationRemarkCheck" style="width: 300px">*长度需在200字符以内</p>
    </div>
    </ng-container>
</nz-modal>
<!-- 创建联系组 -->
<nz-modal [(nzVisible)]="showCreateMemberGroupWindow" [nzTitle]="showCreateMemberGroupWindowTitle"
    (nzOnCancel)="showCreateMemberGroupWindow = false" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading"
    [nzWidth]=600 (nzOnOk)="submitCreateMemberGroup()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">联系组名称：</span>
        <input nz-input style="width: 406px;" (blur)="checkMemberGroupName()" [(ngModel)]="memberGroupName" />
        <p class="warning-tips" *ngIf="!memberGroupNameCheck" style="width: 406px">
            *长度为2-64个字符之间(含),可包含小写字母、数字及分隔符("-"),不能以分隔符开头或结尾</p>
    </div>
    <div class="select-container">
        <span class="select-tips">成员：</span>
        <nz-transfer [nzDataSource]="bindMemberList" nzShowSearch [nzRender]="render" [nzTitles]="['未选', '已选']"
            (nzChange)="changeSelectMembers($event)">
            <ng-template #render let-item>
                <span [title]="item.title" nzTooltipContent="bottom" nz-tooltip>{{ item.title }}
                </span>
            </ng-template>
        </nz-transfer>
    </div>
    <div class="select-container">
        <span class="select-tips">备注：</span>
        <textarea style="width: 406px; height: 120px" nz-input rows="2" (input)="checkMemberGroupRemark()"
            [(ngModel)]="memberGroupRemark" placeholder="备注(长度200字符以内)">
        </textarea>
        <p class="warning-tips" *ngIf="!memberGroupRemarkCheck" style="width: 406px">*长度需在200字符以内</p>
    </div>
    </ng-container>
</nz-modal>
<!-- 创建消息接收联系人 -->
<nz-modal [(nzVisible)]="showCreateMemberWindow" [nzTitle]="showCreateMemberWindowTitle"
    (nzOnCancel)="showCreateMemberWindow = false" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading"
    (nzOnOk)="submitCreateMember()">
    <ng-container *nzModalContent>
    <form nz-form [formGroup]="validateForm">
        <nz-form-item>
            <nz-form-label [nzSpan]="6" nzRequired nzFor="username">用户名</nz-form-label>
            <nz-form-control [nzSpan]="14" [nzValidateStatus]="validateForm.controls['username']">
                <input nz-input formControlName="username" id="username" />
                <nz-form-explain *ngIf="(validateForm.get('username')?.dirty && validateForm.get('username')?.errors)">
                    用户名需在1-16位之间！
                </nz-form-explain>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="6" nzRequired nzFor="email">E-mail</nz-form-label>
            <nz-form-control [nzSpan]="14" [nzValidateStatus]="validateForm.controls['email']">
                <input nz-input formControlName="email" id="email" />
                <nz-form-explain *ngIf="(validateForm.get('email')?.dirty && validateForm.get('email')?.errors)">
                    邮箱格式不规范！
                </nz-form-explain>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="phoneNumber" nzRequired>手机号</nz-form-label>
            <nz-form-control [nzSm]="14" [nzXs]="24" [nzValidateStatus]="validateForm.controls['phoneNumber']">
                <input formControlName="phoneNumber" id="'phoneNumber'" nz-input />
                <nz-form-explain
                    *ngIf="(validateForm.get('phoneNumber')?.dirty && validateForm.get('phoneNumber')?.errors)">
                    请输入手机号！
                </nz-form-explain>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="6" nzFor="memberRemark">备注</nz-form-label>
            <nz-form-control [nzSpan]="14">
                <textarea nz-input formControlName="memberRemark" id="memberRemark"></textarea>
                <nz-form-explain
                    *ngIf="(validateForm.get('memberRemark')?.dirty && validateForm.get('memberRemark')?.errors)">
                    长度需在200字符以内！
                </nz-form-explain>
            </nz-form-control>
        </nz-form-item>
    </form>
    </ng-container>
</nz-modal>