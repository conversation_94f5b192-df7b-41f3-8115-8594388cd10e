.config-content {
  //padding: 20px 0;
  .field-group {
    //margin-bottom: 20px;
    .field-title {
      display: flex;
      //justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      font-weight: bold;
      font-size: 14px;

      .add-button-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;

        .component-count-info {
          font-size: 12px;
          color: #8c8c8c;
          font-weight: normal;
        }
      }
    }

    .field-item {
      margin-bottom: 15px;

      //&.required .label-text::after {
      //  content: ' *';
      //  color: #ff4d4f;
      //}

      .field-wrapper {
        display: block;
        width: 100%;

        .label-text {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }

        input, textarea, nz-select, nz-date-picker, nz-input-number {
          width: 100%;
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }

      label {
        display: block;
        width: 100%;
        position: relative;

        .label-text {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          pointer-events: none; // 防止label文本阻挡点击
        }

        input, textarea, nz-select, nz-date-picker, nz-input-number {
          width: 100%;
          position: relative;
          z-index: 1; // 确保表单控件在最上层
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }

      .form-hint {
        margin-top: 4px;
        font-size: 12px;

        &.error {
          color: #ff4d4f;
        }
      }
    }
  }

  .field-group.service-plan-content{
    display: flex;
    justify-content: space-between;
    gap: 20px;
    .field-item{
      width: 100%;
    }
  }

  // 服务组件网格布局
  .service-plan-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 16px;
  }

  // 服务组件卡片样式
  .service-plan-card {
    width: calc(50% - 11px); // 每行3个，减去gap的影响
    //min-width: 280px; // 最小宽度保证内容不会太挤
    height: 280px; // 固定高度
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;
      background: #fafafa;
      border-radius: 8px 8px 0 0;
      flex-shrink: 0;

      .card-title {
        font-weight: 500;
        color: #262626;
        font-size: 14px;
      }

      .delete-btn {
        padding: 4px;
        height: auto;
        width: auto;
        min-width: auto;

        &:hover {
          background-color: #fff2f0;
        }
      }
    }

    .card-content {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      overflow-y: auto;

      .field-item {
        display: flex;
        flex-direction: column;
        gap: 6px;
        margin:0;

        &.required .field-label::after {
          content: ' *';
          color: #ff4d4f;
        }

        .field-label {
          font-size: 12px;
          font-weight: 500;
          color: #595959;
          line-height: 1.2;
        }

        .field-control {
          width: 100%;
          height: 32px;

          &.ant-select {
            height: 32px;

            .ant-select-selector {
              height: 32px;
              padding: 4px 11px;
            }
          }

          &.ant-input-number {
            height: 32px;
            width: 100%;
          }
        }

        .field-error {
          font-size: 11px;
          color: #ff4d4f;
          line-height: 1.2;
          margin-top: 2px;
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 1200px) {
    .service-plan-card {
      width: calc(50% - 8px); // 中等屏幕每行2个
    }
  }

  @media (max-width: 768px) {
    .service-plan-card {
      width: 100%; // 小屏幕每行1个
      min-width: auto;
    }
  }
}

// 修复弹框样式
:host ::ng-deep {
  .ant-modal-content {
    .ant-modal-body {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  // 修复表单控件样式
  .config-content {
    .field-group {
      .field-item {
        input {
          width: 100%;
          padding: 8px 11px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          box-shadow: none;
          transition: all 0.3s ease;
          font-size: 14px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          background-color: #fff;

          &:hover {
            border-color: #40a9ff;
          }

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, .2);
            outline: none;
          }
        }

        textarea {
          width: 100%;
          padding: 8px 11px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          box-shadow: none;
          transition: all 0.3s ease;
          resize: vertical;
          font-size: 14px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          background-color: #fff;

          &:hover {
            border-color: #40a9ff;
          }

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, .2);
            outline: none;
          }
        }
      }
    }
  }

  // 修复nz-select点击区域问题
  .ant-select {
    position: relative !important;
    z-index: 10 !important;
    pointer-events: auto !important;

    .ant-select-selector {
      position: relative !important;
      z-index: 10 !important;
      pointer-events: auto !important;
      cursor: pointer !important;
    }

    .ant-select-selection-search {
      pointer-events: auto !important;
    }

    .ant-select-selection-placeholder {
      pointer-events: auto !important;
      cursor: pointer !important;
    }

    .ant-select-selection-item {
      pointer-events: auto !important;
    }

    .ant-select-arrow {
      pointer-events: auto !important;
      cursor: pointer !important;
    }
  }

  // 确保弹窗内的选择框正常工作
  .ant-modal-body {
    .ant-select {
      z-index: 10 !important;
    }
  }

  // 优化表单控件在卡片中的显示
  .service-plan-card {
    .ant-select-selector {
      font-size: 12px;
    }

    .ant-input-number-input {
      font-size: 12px;
    }
  }
}


