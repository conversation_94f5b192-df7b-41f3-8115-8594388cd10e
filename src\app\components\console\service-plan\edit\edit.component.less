.service-plan-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;

    .right-button {
        text-align: right;
    }
}

.service-plan-item {
    padding: 0 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin-bottom: 16px;

    &:last-child {
        margin-bottom: 0;
    }
}

.empty-items {
    padding: 24px;
    text-align: center;
    background-color: #fafafa;
    border-radius: 4px;
    color: #999;
}

.service-plan-item-row {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin: 0 -8px;

    .field-item {
        min-width: auto;
        padding: 0 8px;

        &.name-item {
            flex: 0 0 25%;
            max-width: 25%;
        }

        &.type-item {
            flex: 0 0 25%;
            max-width: 25%;
        }

        &.subtype-item {
            flex: 0 0 20%;
            max-width: 20%;
        }

        &.amount-item {
            flex: 0 0 15%;
            max-width: 15%;
        }

        &.action-item {
            flex: 0 0 auto;
            width: 40px;
            min-width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;

            .on-table-action-item {
                cursor: pointer;
                display: inline-block;
                margin-top: 28px;

                .icon {
                    font-size: 16px;
                    //color: #f5222d;

                    &:hover {
                        //color: #ff4d4f;
                    }
                }
            }
        }
    }
}
.form-hint{
    padding-left:0 !important;
}
.field-group{
    padding-left:0 !important;
}

    // 修复区域下拉框样式
:host ::ng-deep {
    .ant-select-dropdown {
        z-index: 1050; // 确保下拉框在模态框之上
    }

    .ant-select-selection-overflow {
        flex-wrap: wrap;
        max-height: 100px;
        overflow-y: auto;
    }

    .ant-select-multiple .ant-select-selection-item {
        height: 24px;
        line-height: 22px;
        margin-top: 2px;
        margin-bottom: 2px;
    }
}