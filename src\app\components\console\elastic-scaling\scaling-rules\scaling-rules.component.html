<div class="scaling-rules table-content">
    <ol class="on-breadcrumb">
        <li><a routerLink="../">{{ currentGroup.groupName }}</a></li>
        <li><span>伸缩策略</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">伸缩策略</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <div class="pull-right">
                    <button nz-button nzType="primary"
                        (click)="showRuleModal()">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        添加伸缩策略
                    </button>
                </div>
            </div>
            <nz-table
                #rules
                [nzItemRender]="renderItemTemplate"
                [nzData]="scalingRules"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChange($event)">
                <thead>
                    <tr>
                        <th>名称/ID</th>
                        <th>触发条件</th>
                        <th>冷却时间(秒)</th>
                        <th>监控周期(分钟)</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of rules.data; trackBy: trackByFn">
                        <td>
                            {{ item.ruleName }}/{{ item.ruleId }}
                        </td>
                        <td class="pre-line">
                            {{ getTriggerText(item) }}
                        </td>
                        <td>
                            {{ item.cooldownTime }}
                        </td>
                        <td>
                            {{ item.referencePeriod }}
                        </td>
                        <td>
                            <span class="dot {{ getStatusClass(item) }}">{{ getStatusText(item) }}</span>
                        </td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.ruleId]">
                                <div class="on-table-action-item"
                                    (click)="showRuleModal(item.ruleId);">
                                    <i nzTitle="编辑策略"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该策略吗？"
                                    [ngClass]="{'disabled': !canDeleteRule(item)}"
                                    (nzOnConfirm)="deleteRule(item);"
                                    [nzCondition]="!canDeleteRule(item)">
                                    <i nzTitle="删除策略"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                                <div class="on-table-action-item"
                                    [hidden]="item.status === 'run'"
                                    (click)="enableRuleCheck(item)">
                                    <i nzTitle="启用策略"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-play-circle-o"></i>
                                </div>
                                <div class="on-table-action-item"
                                    [hidden]="item.status === 'stop'"
                                    (click)="disableRule(item);">
                                    <i nzTitle="停用策略"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-stop-circle-o"></i>
                                </div>
                        </div>
                        <div class="on-table-actions"
                            [hidden]="!busyStatus[item.ruleId]">
                            <div
                                class="action-loading-placeholder">
                                <i class="icon" nz-icon
                                    [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
        
    </div>
</div>

<app-scaling-rule-config
    [isVisible]="ruleModalVisible"
    [groupId]="currentGroup.id"
    [ruleId]="currentRuleId"
    (close)="hideRuleModal()"
    (submit)="getRules()">
</app-scaling-rule-config>