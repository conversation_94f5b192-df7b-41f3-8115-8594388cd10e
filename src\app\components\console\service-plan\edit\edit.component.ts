import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ServicePlanService } from 'src/app/service/console/service-plan/service-plan.service';
import { ServicePlanType, ServicePlanTypeMap, ServicePlanItemType, ServicePlanItemTypeMap, ServicePlanItemSubType, ServicePlanItemSubTypeMap } from '../service-plan-type.enum';

@Component({
    selector: 'app-edit',
    templateUrl: './edit.component.html',
    styleUrls: ['./edit.component.less']
})
export class EditComponent implements OnInit {
    @Input() isVisible: boolean = false;
    @Input() editData: any = null;
    @Output() close = new EventEmitter<boolean>();
    @Output() submit = new EventEmitter<boolean>();

    servicePlanForm: FormGroup;
    isSubmitting: boolean = false;
    isEdit: boolean = false;
    sliceTags: string[] = [];

    servicePlanTypeOptions = Object.keys(ServicePlanTypeMap).map(key => ({
        label: ServicePlanTypeMap[key],
        value: key
    }));

    servicePlanItemTypeOptions = Object.keys(ServicePlanItemTypeMap).map(key => ({
        label: ServicePlanItemTypeMap[key],
        value: key
    }));

    servicePlanItemSubTypeOptions = Object.keys(ServicePlanItemSubTypeMap).map(key => ({
        label: ServicePlanItemSubTypeMap[key],
        value: key
    }));

    constructor(
        private fb: FormBuilder,
        private servicePlanService: ServicePlanService,
        private msg: NzMessageService
    ) {}

    ngOnInit() {
        this.initForm();
        if (this.editData) {
            this.isEdit = true;
            this.patchFormValues();
        }
    }

    initForm() {
        this.servicePlanForm = this.fb.group({
            id: [null],
            name: ['', [Validators.required]],
            servicePlanType: ['', [Validators.required]],
            servicePlanCode: [''],
            regionsStr: [''],
            price: [0, [Validators.required, Validators.min(0)]],
            autoEffectiveDate: [null],
            autoExpiryDate: [null],
            servicePlanItems: this.fb.array([])
        });
    }

    patchFormValues() {
        if (!this.editData) return;

        // 处理日期时间字符串转为Date对象
        let autoEffectiveDate = null;
        let autoExpiryDate = null;

        if (this.editData.autoEffectiveDate) {
            autoEffectiveDate = new Date(this.editData.autoEffectiveDate);
        }

        if (this.editData.autoExpiryDate) {
            autoExpiryDate = new Date(this.editData.autoExpiryDate);
        }

        this.servicePlanForm.patchValue({
            id: this.editData.id,
            name: this.editData.name,
            servicePlanType: this.editData.servicePlanType,
            servicePlanCode: this.editData.servicePlanCode,
            regionsStr: this.editData.regionsStr,
            price: this.editData.price,
            autoEffectiveDate: autoEffectiveDate,
            autoExpiryDate: autoExpiryDate
        });

        // 清空现有的servicePlanItems
        while (this.servicePlanItems.length) {
            this.servicePlanItems.removeAt(0);
        }

        // 添加编辑数据中的servicePlanItems
        if (this.editData.servicePlanItems && this.editData.servicePlanItems.length) {
            this.editData.servicePlanItems.forEach(item => {
                this.servicePlanItems.push(this.createServicePlanItem(item));
            });
        }
    }

    get servicePlanItems(): FormArray {
        return this.servicePlanForm.get('servicePlanItems') as FormArray;
    }

    createServicePlanItem(item?: any): FormGroup {
        const formGroup = this.fb.group({
            id: [item ? item.id : null],
            name: [item ? item.name : '', [Validators.required]],
            servicePlanItemType: [item ? item.servicePlanItemType : '', [Validators.required]],
            servicePlanItemSubType: [item ? item.servicePlanItemSubType : null],
            amount: [item ? item.amount : 1, [Validators.required, Validators.min(1)]]
        });

        // 监听类型变更，当类型不是磁盘时，清空子类型值
        formGroup.get('servicePlanItemType').valueChanges.subscribe(value => {
            const subTypeControl = formGroup.get('servicePlanItemSubType');
            if (value !== 'DISK_GB' && subTypeControl.value) {
                subTypeControl.setValue(null);
            }
        });

        return formGroup;
    }

    addServicePlanItem() {
        this.servicePlanItems.push(this.createServicePlanItem());
    }

    removeServicePlanItem(index: number) {
        this.servicePlanItems.removeAt(index);
    }

    handleCancel() {
        this.close.emit(true);
    }

    validateAllFormFields(formGroup: FormGroup) {
        Object.keys(formGroup.controls).forEach(field => {
            const control = formGroup.get(field);
            if (control instanceof FormControl) {
                control.markAsDirty();
                control.updateValueAndValidity();
            } else if (control instanceof FormGroup) {
                this.validateAllFormFields(control);
            } else if (control instanceof FormArray) {
                for (let i = 0; i < control.length; i++) {
                    this.validateAllFormFields(control.at(i) as FormGroup);
                }
            }
        });
    }

    /**
     * 格式化日期时间为yyyy-MM-dd HH:mm:ss字符串
     * @param date 日期对象或日期字符串
     * @returns 格式化后的日期时间字符串
     */
    formatDateTime(date: Date | string): string {
        if (!date) return null;

        const d = typeof date === 'string' ? new Date(date) : date;

        // 检查日期是否有效
        if (isNaN(d.getTime())) return null;

        // 格式化年月日
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');

        // 格式化时分秒
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        // 返回格式化后的字符串
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    submitForm() {
        if (this.servicePlanForm.invalid) {
            this.validateAllFormFields(this.servicePlanForm);
            return;
        }

        this.isSubmitting = true;
        const formData = {...this.servicePlanForm.value};

        // 如果regionsStr为空字符串，则设置为null
        if (formData.regionsStr === '') {
            formData.regionsStr = null;
        }

        // 创建时不传servicePlanCode
        if (!this.isEdit) {
            delete formData.servicePlanCode;
        }

        // 处理服务组件子类型为空字符串的情况
        if (formData.servicePlanItems && formData.servicePlanItems.length > 0) {
            formData.servicePlanItems.forEach(item => {
                if (item.servicePlanItemSubType === '') {
                    item.servicePlanItemSubType = null;
                }
            });
        }

        // 处理日期时间格式，统一为yyyy-MM-dd HH:mm:ss字符串
        if (formData.autoEffectiveDate) {
            formData.autoEffectiveDate = this.formatDateTime(formData.autoEffectiveDate);
        }

        if (formData.autoExpiryDate) {
            formData.autoExpiryDate = this.formatDateTime(formData.autoExpiryDate);
        }

        console.log(formData);
        const apiCall = this.isEdit
            ? this.servicePlanService.update(formData)
            : this.servicePlanService.add(formData);

        apiCall.then(rs => {
            if (rs.success) {
                this.msg.success(`服务计划${this.isEdit ? '更新' : '创建'}成功`);
                this.submit.emit(true);
                this.isSubmitting = false;
            } else {
                this.msg.error(`服务计划${this.isEdit ? '更新' : '创建'}失败${rs.message ? ': ' + rs.message : ''}`);
                this.isSubmitting = false;
            }
        }).catch(err => {
            this.msg.error(`服务计划${this.isEdit ? '更新' : '创建'}失败`);
            this.isSubmitting = false;
        });
    }
}

import { FormControl } from '@angular/forms';
