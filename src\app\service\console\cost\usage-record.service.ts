import { Injectable } from '@angular/core';
import * as URL from 'src/app/service/common/URL';
import { RequestService } from '../../common/request/request.service';
import {CHECK_CONNECTION, SERVICE_USAGE_RECORD} from "src/app/service/common/URL";

@Injectable({
    providedIn: 'root'
})
export class UsageRecordService {
    constructor(
        private req: RequestService
    ) {}

    getDataList(params){
        return this.req.post(URL.SERVICE_USAGE_RECORD + "/query",params)
            .then(rs => {
                return rs;
            });
    }

    getChildList(id) {
        return this.req.get(URL.SERVICE_USAGE_RECORD + "/getChildList/" + id)
            .then(rs => {
                return rs;
            });
    }
}
