import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { MessageService } from 'src/app/service/console/utils/message.service';
import { RdsService } from '../../../../service/console/rds/rds.service';
import { environment } from 'src/environments/environment';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { FormGroup, FormBuilder } from "@angular/forms";

const PRODUCT = 'rds';
const LIMIT = 3;
const BUSY_TEXT_MAP = {
    'powerOn': '开机中',
    'powerOff': '关机中',
    'reboot': '重启中',
    'delete': '删除中',
};

const STATUS_CLASS_MAP = {
    'init': 'dot-blue',
    'running': 'dot-green',
    'stop': 'dot-red',
    'power_on': 'dot-green',
    'power_off': 'dot-gray',
};
const STATUS_MAP = {
    'init': '初始化中',
    'running': '运行中',
    'stop': '停止',
};

@Component({
    selector: 'app-rds',
    templateUrl: './rds.component.html',
    styleUrls: ['./rds.component.less']
})
export class RdsComponent implements OnInit {
    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private msg: MessageService,
        private rdsService: RdsService,
        private fb: FormBuilder
    ) {}

    isAdmin = window.localStorage.getItem('isAdmin') === 'true'
    rules = JSON.parse(window.localStorage.getItem("rules")).filter(item => item.service === 'rds').map(item => item.permissionType);
    permission(param: string) {
        return this.isAdmin || this.rules.includes(param)
    }
    isArchiveUser = window.localStorage.getItem('isArchiveUser')
    keyword: string = '';

    tableListData: any[] = [];
    tableListDataEmpty = false;
    statusArray: any[] = [];
    rebootStatusArray: any[] = [];
    getRdsListInterval: any;
    busyStatus = {};
    isLoading: boolean = false;

    resLimit: boolean = false;
    showResLimit: boolean = environment.showResLimit[PRODUCT];


    cols = [
        {
            title: '云数据库',
            ColumnKey: "name",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '250px'
        },
        {
            title: '运行状态',
            ColumnKey: "rebootStatus,cpuNum",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '250px'
        },
        {
            title: '内网IP地址',
            ColumnKey: "ipAddress",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '250px'
        },
        {
            title: 'CPU',
            ColumnKey: "cpuNum",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '内存',
            ColumnKey: "memoryGB",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '操作',
            allowSort: '',
            sortFlag: true,
            showSort: false,
            ColumnKey: ""
        }
    ];
    filters = {
        pageNum: 0 ,
        pageSize: environment.pageSize,
        orderBy1: false,
        orderName1: '',
        orderBy2: false,
        orderName2: '',
        searchType: 'rds'
    };
    pager = {
        page: 1,
        pageSize: environment.pageSize,
        total: 0,
    };
    sortName = '';
    sortValue = false;
    openFlag:boolean = true;
    fristQuery:boolean = false;
    oldSortName;
    sort;
    index;

    changeModalVisible = false;
    serviceItem: FormGroup;
    isChanging: boolean = false;

    ngOnInit() {
        this.initFbGroup();
        this.getRdsList();
        this.getRdsListInterval = window.setInterval(() => {
            this.statusArray = [];
            this.rebootStatusArray = [];
            this.getRdsList();
        }, 5000);
    }

    initFbGroup(){
        this.serviceItem = this.fb.group({
            serviceName: '',
            cpuBefore: '',
            cpuAfter: '',
            memoryBefore: '',
            memoryAfter: '',
            diskBefore: '',
            diskAfter: '',
            instanceBefore: {},
            instanceAfter: {},
        });
    }

    ngOnDestroy() {
        window.clearInterval(this.getRdsListInterval);
    }

    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getMonitorList();
    }

    getMonitorList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);
        if (keyword) {
            params.bean = {
                name: keyword
            }
        }
    }

    // 格式化状态
    getStatusText(status): string {
        return STATUS_MAP[String(status.rdsStatus)] || '-';
        // return STATUS_MAP[String(status)] || '-';
    }

    getRdsStatusClass(item) {
        return STATUS_CLASS_MAP[item.rdsStatus];
    }

    search() {
        this.filters.pageNum = 1;
        this.getRdsList();
    }

    // 获取rds列表
    getRdsList(filters?) {
        let stopInterval = true;
        const self = this;
        const tableListDataTemp = [];
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);
        if (keyword) {
            params.bean = {
                name: keyword
            }
        }
        this.isLoading = true;
        self.statusArray = [];
        self.rebootStatusArray = [];
        this.rdsService.getList(params).then(res => {
            stopInterval = false;
            if (res.code === '200') {
                if (res.data) {
                    const data = res.data.dataList;
                    const length = data.length;
                    if (length <= 0) {
                        self.tableListDataEmpty = true;
                    }
                    for (let i = 0; i < length; i++) {
                        tableListDataTemp.push(data[i]);
                        self.statusArray.push(data[i].status);
                        self.rebootStatusArray.push(data[i].rebootStatus);
                        tableListDataTemp[i].createTime = self.getdate(
                            data[i].createTime
                        );
                    }

                    self.tableListData = tableListDataTemp;
                    this.pager = {
                        page: res.data.pageNum+1,
                        pageSize: res.data.pageSize,
                        total: res.data.recordCount,
                    };
                    // TODO rds 创建按钮disabled状态，暂时屏蔽 --mayufei ********
                    // if (environment.resLimit[PRODUCT]) {
                    //     if (length >= LIMIT && environment.freeAccount.indexOf(localStorage.getItem('username')) === -1) {
                    //         this.resLimit = true;
                    //     } else {
                    //         this.resLimit = false;
                    //     }
                    // }

                    if ((self.statusArray.indexOf(0) === -1 && self.rebootStatusArray.indexOf(1) === -1)
                        || !(self.statusArray.length && self.rebootStatusArray.length)
                        || !self.tableListData.length
                        || stopInterval) {
                        window.clearInterval(self.getRdsListInterval);
                    }
                    // this.resLimit = self.tableListData.length >= 1;
                }
            } else {
                window.clearInterval(self.getRdsListInterval);
            }

            this.isLoading = false;
        })
        .catch(msg => {
            window.clearInterval(self.getRdsListInterval);
            this.isLoading = false;
        });
    }
    // 开机
    canPowerOnRds(item): boolean {
        if(item.rdsStatus){
            return item.rdsStatus != 'running' && item.rdsStatus !== 'init';
        }
        return false;
    }

    powerOnRds(item): void {
        if (!this.canPowerOnRds(item)) {
            return;
        }
        this.busyStatus[item.id] = 'powerOn';
        this.rdsService.powerOnRds(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('开机处理提交成功');
                this.getRdsList();
            } else {
                this.msg.error(`开机处理提交失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('开机处理提交失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 关机
    canPowerOffRds(item): boolean {
        if(item.rdsStatus) {
            return item.rdsStatus != 'init' && item.rdsStatus !== 'stop';
        }
        return false;
    }

    powerOffRds(item): void {
        if (!this.canPowerOffRds(item)) {
            return;
        }
        this.busyStatus[item.id] = 'powerOff';
        this.rdsService.powerOffRds(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('关机处理提交成功');
                this.getRdsList();
            } else {
                this.msg.error(`关机处理提交失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('关机处理提交失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 重启
    canRebootRds(item): boolean {
        if(item.rdsStatus) {
            return item.rdsStatus != 'init' && item.rdsStatus !== 'stop';
        }
        return false;
    }

    rebootRds(item): void {
        // console.log(item.powerStatus === 'reboot');
        if (!this.canRebootRds(item)) {
            return;
        }
        this.busyStatus[item.id] = 'reboot';
        this.rdsService.rebootRds(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('重启处理提交成功');
                this.getRdsList();
            } else {
                this.msg.error(`重启处理提交失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('重启处理提交失败');
            this.busyStatus[item.id] = '';
        });
    }
    trackById(item) {
        return item.id;
    }

    getBusyText(item): string {
        return BUSY_TEXT_MAP[this.busyStatus[item.id]] || '';
    }

    getRdsStatusText(rds) {
        let status = rds.cpuNum;
        let rebootStatus = rds.rebootStatus;
        if (rebootStatus === 1) {
            return '重启中';
        }
        if (rebootStatus === 2) {
            return '重启失败';
        }
        if (status === 0) {
            return '启动中';
        }
        if (status === 2) {
            return '启动失败';
        }
        if (status === 3) {
            return '已欠费';
        }
        if (status === 1 && rebootStatus === 0) {
            return '运行中';
        }
    }

    // getRdsStatusClass(rds) {
    //     let status = rds.cpuNum;
    //     let rebootStatus = rds.rebootStatus;
    //     if (rebootStatus === 1 || status === 0) {
    //         return 'dot-blue';
    //     }
    //     if (rebootStatus === 2 || status === 2 || status === 3) {
    //         return 'dot-red';
    //     }
    //     if (rebootStatus === 0 && status === 1) {
    //         return 'dot-green';
    //     }
    // }

    getdate(time) {
        const now = new Date(time),
            y = now.getFullYear(),
            m = now.getMonth() + 1,
            d = now.getDate();
        return (
            y +
            '-' +
            (m < 10 ? '0' + m : m) +
            '-' +
            (d < 10 ? '0' + d : d) +
            ' ' +
            now.toTimeString().substr(0, 8)
        );
    }

    canViewDetail(item) {
        return item.status !== 0 && item.status !== 3;
    }

    viewDetail(item) {
        if (!this.canViewDetail(item)) {
            return;
        }

        this.router.navigate(['..', 'rds-detail'], {
            queryParams: {
                id: item.id
            },
            relativeTo: this.route
        });
    }

    canDeleteRds(item) {
        return this.getRdsStatusClass(item) !== 'dot-blue' && this.getRdsStatusClass(item) !== 'dot-green';
    }

    deleteRds(item) {
        if (!this.canDeleteRds(item)) {
            return;
        }

        this.busyStatus[item.id] = 'delete';
        this.rdsService.deleteRds(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success(`RDS删除成功！`);
                this.getRdsList();
            } else {
                this.msg.error(`RDS删除失败${ rs.message ? ': ' + rs.message : '' }`);
            }

            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.busyStatus[item.id] = '';
        });
    }

    // 变更
    canChange(item) {
        if (!item.spVapp.updateOrderQuotaDetail) {
            return false;
        }
        return true;
    }

    showChangeService(item) {
        if (!this.canChange(item)) {
            return;
        }
        this.changeModalVisible = true;
        this.rdsService.getUpdateConfigCloudServer(item.spVapp.updateOrderQuotaDetail.id)
            .then(rs => {
                if (rs.success) {
                    this.serviceItem.value.instanceBefore = item.spVapp.vmList[0];
                    this.serviceItem.value.instanceAfter = rs.data;
                } else {
                    this.msg.error(`获取初期化数据失败${rs.message ? ': ' + rs.message : ''}`);
                }
            })
            .catch(err => {
                this.msg.error('获取初期化数据失败');
            });
    }

    changeService() {
        let data: any = {};
        let after = this.serviceItem.value.instanceAfter;
        let before = this.serviceItem.value.instanceBefore;
        data.quotaDetailId = after.id;
        data.updateVappId = before.id;
        this.isChanging = true;
        this.rdsService.updateRdsGB(data)
        .then(rs => {
            if (rs.success) {
                this.msg.success('操作成功！');
                this.getRdsList();
                this.isChanging = false;
                this.changeModalVisible = false;
            } else {
                this.msg.error(`操作失败${rs.message ? ': ' + rs.message : ''}`);
                this.isChanging = false;
                this.changeModalVisible = false;
            }
        })
        .catch(err => {
            this.isChanging = false;
            this.changeModalVisible = false;
        })
    }

    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
        if (this.index !== undefined && this.index !== '') {
            this.cols[this.index].allowSort = this.sort;
        }
        this.openFlag = false;
    }

    onParamsChange(params: NzTableQueryParams) {
        if (this.fristQuery) {
            if (this.openFlag) {
                var checkData = false;
                var getIndex
                var index = -1;
                params.sort.forEach(sortDate => {
                    index ++;
                    if(sortDate.value) {
                        this.sortName = sortDate.key;
                        if (sortDate.value === 'ascend') {
                            this.sort = sortDate.value;
                            this.sortValue = false;
                        } else {
                            this.sortValue = true;
                        }
                        checkData = true;
                        getIndex = index;
                    }
                })
                this.index = getIndex;
                if (checkData) {
                    var names = this.sortName.split(',');
                    if (names.length == 2) {
                        this.filters.orderBy2 = this.sortValue;
                        this.filters.orderName2 = names[1];
                    } else {
                        this.filters.orderBy2 = this.sortValue;
                        this.filters.orderName2 = '';
                    }
                    this.filters.orderBy1 = this.sortValue;
                    this.filters.orderName1 = names[0];
                    this.getRdsList();
                } else {
                    this.filters.orderBy1 = false;
                    this.filters.orderName1 = '';
                    this.filters.orderBy2 = false;
                    this.filters.orderName2 = '';
                    this.getRdsList();
                }
            } else {
                this.openFlag = true;
            }
        } else {
            this.fristQuery = true;
        }
    }
}
