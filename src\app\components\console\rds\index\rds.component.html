<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><span>RDS数据库</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入RDS数据库名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary"
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="resLimit ? null : '../rds-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建RDS数据库
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    RDS数据库
                    <!-- <small class="danger" *ngIf="showResLimit">
                        <i class="fa fa-exclamation-circle"></i>
                        试用期间每个用户最多可免费创建3个RDS数据库，如有其它需求请提交工单联系！
                    </small> -->
                </span>
            </div>
            <nz-table #tableList style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzShowPagination]="true"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableListData"
                >
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of tableList.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>
                            <span class="dot {{ getRdsStatusClass(item) }}">
                                {{ getStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.ipAddress }}</td>
                        <td>{{ item.spVapp.vmList?item.spVapp.vmList[0].cpuNum+' 核':''}}</td>
                        <td>{{ item.spVapp.vmList?item.spVapp.vmList[0].memoryGB+' GB':''}}</td>
                        <td style="white-space: nowrap">
                            <div class="on-table-actions" *ngIf="permission('view')"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="viewDetail(item);"
                                    [ngClass]="{'disabled': !canViewDetail(item)}">
                                    <i nzTitle="详情"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-search"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要关闭该Rds实例吗？"
                                    (nzOnConfirm)="powerOffRds(item);"
                                    [nzCondition]="!canPowerOffRds(item)"
                                    [hidden]="!(canPowerOffRds(item) || item.deployStatus === 'INIT')"
                                    [ngClass]="{'disabled': !canPowerOffRds(item)}">
                                    <i nzTooltipTitle="关机"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-guanji">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    (click)="powerOnRds(item);"
                                    [hidden]="!canPowerOnRds(item)"
                                    [ngClass]="{'disabled': !canPowerOnRds(item)}">
                                    <i nzTooltipTitle="开机"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-qidong"></i>
                                </div>
                            <div class="on-table-action-item" *ngIf="permission('power')"
                                nz-popconfirm
                                nzTooltipContent="top"
                                nzPopconfirmTitle="确定要重启吗？"
                                [nzCondition]="canRebootRds(item)"
                                (nzOnConfirm)="rebootRds(item);"
                                [ngClass]="{'disabled': canRebootRds(item)}">
                                <i nzTooltipTitle="重启"
                                    nzTooltipContent="bottom"
                                    nz-tooltip
                                    class="icon iconfont icon-haikezhangguizhushou_zhongqi">
                                </i>
                            </div>
                            <div class="on-table-action-item" *ngIf="permission('update')"
                            (click)="isArchiveUser === 'true'?null :showChangeService(item)"
                                [hidden]="!canChange(item)"
                                [ngClass]="{'disabled': !canChange(item) || isArchiveUser === 'true'}">
                                <i nzTooltipTitle="变更"
                                    nzTooltipContent="bottom"
                                    nz-tooltip
                                    class="icon iconfont iconfontBianji3 icon-bianji3"></i>
                            </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzPlacement="top"
                                    [title]="isArchiveUser === 'true'?null:'确定要删除该RDS数据库吗？'"
                                    [nzCondition]="!canDeleteRds(item)"
                                    (nzOnConfirm)="isArchiveUser === 'true'?null:deleteRds(item);"
                                    [ngClass]="{'disabled': !canDeleteRds(item) || isArchiveUser === 'true'}">
                                    <i nzTitle="删除"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
<nz-modal [(nzVisible)]="changeModalVisible" nzTitle="RDS数据库变更" (nzOnCancel)="changeModalVisible = false;"
(nzOnOk)="changeService()" [nzOkLoading]="isChanging" [nzCancelLoading]="isChanging" [nzWidth]="620">
<ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="serviceItem" (submit)="changeService()">
    <section class="field-section">
        <div class="field-group">
            <div class="field-item topSty">
                <label >
                    <span class="label-text">RDS数据库名称</span>
                    <input class="inputWidth_Name" type="text" formControlName="serviceName" disabled value="{{ serviceItem.value.instanceBefore.name }}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label  class="label01">
                    <span class="label-text">CPU</span>
                    <input required type="text" formControlName="cpuBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.cpuNum + '核'}}"/>
                </label>

                <label  class="label02">
                    <span class="label-text">CPU变更为</span>
                    <input required type="text" formControlName="cpuAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.cpuUnit + '核'}}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label  class="label01">
                    <span class="label-text">内存</span>
                    <input required type="text" formControlName="memoryBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.memoryGB + 'G'}}"/>
                </label>

                <label  class="label02">
                    <span class="label-text">内存变更为</span>
                    <input required type="text" formControlName="memoryAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.memoryUnit + 'G'}}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label  class="label01">
                    <span class="label-text">磁盘</span>
                    <input required type="text" formControlName="diskBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.diskGB + 'G'}}"/>
                </label>

                <label  class="label02">
                    <span class="label-text">磁盘变更为</span>
                    <input required type="text" formControlName="diskAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.diskUnit + 'G'}}"/>
                </label>
            </div>
        </div>
    </section>
</form>
</ng-container>
</nz-modal>
