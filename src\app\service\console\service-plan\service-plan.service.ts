import { Injectable } from '@angular/core';
import * as URL from 'src/app/service/common/URL';
import { RequestService } from '../../common/request/request.service';

@Injectable({
    providedIn: 'root'
})
export class ServicePlanService {
    constructor(
        private req: RequestService
    ) {}

    query(params: {}) {
        return this.req.post(URL.SERVICE_PLAN + '/query', params)
            .then(rs => {
                return rs;
            });
    }

    add(params: {}) {
        return this.req.post(URL.SERVICE_PLAN + '/add', params)
            .then(rs => {
                return rs;
            });
    }

    update(params: {}) {
        return this.req.put(URL.SERVICE_PLAN + '/update', params)
            .then(rs => {
                return rs;
            });
    }

    delete(id: number) {
        return this.req.delete(URL.SERVICE_PLAN + '/delete/' + id)
            .then(rs => {
                return rs;
            });
    }
}
