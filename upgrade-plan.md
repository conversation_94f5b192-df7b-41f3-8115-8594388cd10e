# Angular 14 升级到 Angular 15 计划

## 1. 项目当前状态
- 当前Angular版本: 14.3.0
- 当前ng-zorro-antd版本: 14.3.0
- TypeScript版本: 4.6.4
- 其他主要依赖:
  - echarts: 5.4.3
  - ngx-echarts: 8.0.1
  - ngx-pagination: 6.0.1
  - rxjs: 7.5.0

## 2. 升级步骤

### 2.1 备份项目
在开始升级前，确保有项目的完整备份。

### 2.2 升级Angular核心包
使用Angular CLI的update命令升级Angular核心包到15版本:
```bash
ng update @angular/core@15 @angular/cli@15 --force
```

### 2.3 升级ng-zorro-antd
升级ng-zorro-antd到与Angular 15兼容的版本:
```bash
ng update ng-zorro-antd@15 --force
```

### 2.4 升级其他依赖
检查并升级其他可能需要更新的依赖:
```bash
npm update
```

### 2.5 修复可能的兼容性问题
- 检查并修复可能的TypeScript错误
- 检查并修复可能的模板语法变更
- 检查并修复可能的API变更

### 2.6 测试应用
- 安装依赖: `npm install`
- 构建应用: `npm run build`
- 启动应用: `npm start`

### 2.7 修复运行时错误
解决启动和运行过程中可能出现的错误。

## 3. 可能遇到的问题及解决方案

### 3.1 TypeScript兼容性问题
Angular 15可能需要更新TypeScript版本，可能需要修改一些类型定义。

### 3.2 API变更
Angular 15可能有一些API变更，需要相应地更新代码。

### 3.3 ng-zorro-antd组件变更
检查ng-zorro-antd 15版本的变更日志，了解可能需要调整的组件用法。

### 3.4 构建配置变更
可能需要更新angular.json中的一些配置以适应新版本。

## 4. 回滚计划
如果升级过程中遇到无法解决的问题，准备回滚到原始版本的步骤。
