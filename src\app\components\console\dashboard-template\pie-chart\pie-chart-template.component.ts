import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { EChartsOption, PieSeriesOption } from 'echarts';

@Component({
  selector: 'app-pie-chart-template',
  templateUrl: './pie-chart-template.component.html', // 使用 templateUrl
  styleUrls: ['./pie-chart-template.component.less']
})
export class PieChartTemplateComponent implements OnInit, OnChanges {

  @Input() data: any[] = [];
  @Input() title: string = '';
  @Input() valueUnit: string = ''; // 添加单位输入属性
  @Input() chartHeight: number = 280; // 图表高度，默认280px

  chartOptions: EChartsOption = {};
  updateOptions: EChartsOption = {};
  colors = ['#5470c6', '#91cc75', '#fac858', '#73c0de', '#3ba272', '#67cba5', '#3b77ff', '#9a60b4', '#ea7ccc', '#bb6bcd', '#fc8452', '#ee6666']; // 颜色列表
  legendData: any[] = []; // 用于自定义 legend

  ngOnInit(): void {
    this.calculateLegendData();
    this.chartOptions = this.getChartOptions();
  }

  ngOnChanges(): void {
    this.calculateLegendData();
    this.updateOptions = {
      series: [{
        data: this.data
      }]
    };
  }

  calculateLegendData(): void {
    const total = this.data.reduce((sum, item) => sum + item.value, 0);
    this.legendData = this.data.map((item, index) => ({
      name: item.name,
      value: item.value,
      percent: total === 0 ? 0 : ((item.value / total) * 100).toFixed(2),
      color: this.colors[index % this.colors.length],
      unit: this.valueUnit // 将单位添加到 legend 数据中
    }));
  }

  getChartOptions(): EChartsOption {
    const option: EChartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          // Tooltip 保持不变，或者可以根据需要调整
          return `${params.seriesName} <br/>${params.marker}${params.name} : ${params.value}${this.valueUnit} (${params.percent}%)`;
        },
        axisPointer: {
          z: 1000
        }
      },
      legend: {
        show: false // 隐藏默认 legend
      },
      color: this.colors, // 应用颜色列表
      series: [
        {
          name: '占比',
          type: 'pie',
          radius: '85%', // 增大饼图尺寸
          center: ['50%', '50%'], // 调整中心位置，为右侧 legend 留出空间
          data: this.data,
          label: {
            show: false // 去除饼图上的文本说明
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    return option;
  }

}
