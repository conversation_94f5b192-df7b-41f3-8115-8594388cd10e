/**
 * 此脚本用于修复 Node.js EventEmitter 内存泄漏警告
 * 在启动 Angular 开发服务器之前运行此脚本
 */

// 导入所需模块
const { EventEmitter } = require('events');
const http = require('http');
const https = require('https');

console.log('正在应用 EventEmitter 内存泄漏修复...');

// 设置全局 EventEmitter 默认最大监听器数量
EventEmitter.defaultMaxListeners = 30;
console.log(`已将 EventEmitter.defaultMaxListeners 设置为 ${EventEmitter.defaultMaxListeners}`);

// 设置进程最大监听器数量
if (typeof process !== 'undefined' && process.setMaxListeners) {
  process.setMaxListeners(30);
  console.log(`已将 process.setMaxListeners 设置为 30`);
}

// 设置 HTTP 和 HTTPS 模块的 Server 类原型的最大监听器数量
if (http.Server && http.Server.prototype) {
  const originalCreateServer = http.createServer;
  http.createServer = function(...args) {
    const server = originalCreateServer.apply(this, args);
    server.setMaxListeners(30);
    return server;
  };
  console.log('已修补 http.createServer 方法');
}

if (https.Server && https.Server.prototype) {
  const originalCreateServer = https.createServer;
  https.createServer = function(...args) {
    const server = originalCreateServer.apply(this, args);
    server.setMaxListeners(30);
    return server;
  };
  console.log('已修补 https.createServer 方法');
}

// 监听未捕获的异常和拒绝
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
});

console.log('EventEmitter 内存泄漏修复已应用完成');
