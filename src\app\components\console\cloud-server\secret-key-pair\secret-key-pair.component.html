<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="/console/cloud-server">云服务器</a></li>-->
<!--        <li><a routerLink="/console/cloud-server/secret-key-pair">网络安全</a></li>-->
<!--        <li><span>密钥对</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入密钥对名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <button nz-button nzType="primary"
                            (click)="keyPairModalVisible = true">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建密钥对
                    </button>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">密钥对</span>
            </div>
            <nz-table #keyPairs style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="keyPairList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width">
                              {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of keyPairs.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>{{ item.fingerprint }}</td>
                        <td>
                            <div class="on-table-actions" *ngIf="permission('export')"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="downloadKeyPair(item)">
                                    <i nzTooltipTitle="下载"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-download"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要删除该密钥对吗？"
                                    (nzOnConfirm)="deleteKeyPair(item);">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="keyPairs.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-new-key [isVisible]="keyPairModalVisible"
    (close)="keyPairModalVisible = false"
    (submit)="search()">
</app-new-key>
