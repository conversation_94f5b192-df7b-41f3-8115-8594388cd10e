import { Injectable } from '@angular/core';
import * as URL from 'src/app/service/common/URL';
import { RequestService } from '../../common/request/request.service';
import {CHECK_CONNECTION, INSTANCE_INDEX, QUOTA, QUOTA_LOG} from "src/app/service/common/URL";

@Injectable({
    providedIn: 'root'
})
export class DashboardService {
    constructor(
        private req: RequestService
    ) {}
    list(params) {
        return this.req.post(URL.INSTANCE_INDEX + '/list/', params)
            .then(rs => {
                return rs;
            });
    }

    init() {
        return this.req.get(URL.INSTANCE_INDEX + '/init')
            .then(rs => {
                return rs;
            });
    }
}
