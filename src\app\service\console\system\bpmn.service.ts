import { Injectable } from '@angular/core';
import * as URL from 'src/app/service/common/URL';
import { RequestService } from '../../common/request/request.service';

@Injectable({
    providedIn: 'root'
})
export class BpmnService {
    constructor(
        private req: RequestService
    ) {}

    // 查询流程列表
    query(params: {}) {
        return this.req.post(URL.BPMN + '/query', params)
            .then(rs => {
                return rs;
            });
    }

    // 新增流程
    add(params: {}) {
        return this.req.post(URL.BPMN + '/add', params)
            .then(rs => {
                return rs;
            });
    }

    // 更新流程
    update(params: {}) {
        return this.req.put(URL.BPMN + '/update', params)
            .then(rs => {
                return rs;
            });
    }

    // 删除流程
    delete(id: number) {
        return this.req.delete(URL.BPMN + '/delete/' + id)
            .then(rs => {
                return rs;
            });
    }

    // 根据ID获取流程详情
    getById(id: number) {
        return this.req.get(URL.BPMN + '/' + id)
            .then(rs => {
                return rs;
            });
    }

    // 发布流程
    publish(id: number) {
        return this.req.post(URL.BPMN + '/publish/' + id, {})
            .then(rs => {
                return rs;
            });
    }

    // 暂停流程
    suspend(id: number) {
        return this.req.post(URL.BPMN + '/suspend/' + id, {})
            .then(rs => {
                return rs;
            });
    }

    // 恢复流程
    resume(id: number) {
        return this.req.post(URL.BPMN + '/resume/' + id, {})
            .then(rs => {
                return rs;
            });
    }

    // 归档流程
    archive(id: number) {
        return this.req.post(URL.BPMN + '/archive/' + id, {})
            .then(rs => {
                return rs;
            });
    }

    // 部署流程定义
    deploy(params: {}) {
        return this.req.post(URL.BPMN + '/deploy', params)
            .then(rs => {
                return rs;
            });
    }

    // 获取流程定义XML
    getProcessXml(processDefinitionId: string) {
        return this.req.get(URL.BPMN + '/xml/' + processDefinitionId)
            .then(rs => {
                return rs;
            });
    }

    // 获取流程实例列表
    getProcessInstances(params: {}) {
        return this.req.post(URL.BPMN + '/instances/query', params)
            .then(rs => {
                return rs;
            });
    }

    // 启动流程实例
    startProcessInstance(params: {}) {
        return this.req.post(URL.BPMN + '/instances/start', params)
            .then(rs => {
                return rs;
            });
    }

    // 获取任务列表
    getTasks(params: {}) {
        return this.req.post(URL.BPMN + '/tasks/query', params)
            .then(rs => {
                return rs;
            });
    }

    // 完成任务
    completeTask(taskId: string, params: {}) {
        return this.req.post(URL.BPMN + '/tasks/complete/' + taskId, params)
            .then(rs => {
                return rs;
            });
    }

    // 获取流程历史
    getProcessHistory(processInstanceId: string) {
        return this.req.get(URL.BPMN + '/history/' + processInstanceId)
            .then(rs => {
                return rs;
            });
    }

    // 保存流程设计
    saveProcess(params: any) {
        if (params.id) {
            // 更新现有流程
            return this.req.put(URL.BPMN + '/update', params)
                .then(rs => {
                    return rs;
                });
        } else {
            // 创建新流程
            return this.req.post(URL.BPMN + '/add', params)
                .then(rs => {
                    return rs;
                });
        }
    }

    // 获取流程测试数据（用于查看）
    getTestById(id: number) {
        return this.req.get('/cloud/api/aic/bpmn/getTest/' + id)
            .then(rs => {
                return rs;
            });
    }
}
