<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">运维管理</a></li>-->
<!--        <li><span>任务管理</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">任务管理</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form [formGroup]="orderForm" nzLayout="inline" (ngSubmit)="search()">

<!--                    <nz-input-group nzSearch-->
<!--                        [nzAddOnAfter]="suffixIconButton">-->
<!--                        <input type="text" name="keyword"-->
<!--                            autocomplete="off"-->
<!--                            [(ngModel)]="keyword" nz-input-->
<!--                            placeholder="请输入账户名称" />-->
<!--                    </nz-input-group>-->
<!--                    <ng-template #suffixIconButton>-->
<!--                        <button nz-button nzType="primary"-->
<!--                            nzSearch><i nz-icon-->
<!--                                nzType="search"></i></button>-->
<!--                    </ng-template>-->
                    <input type="text" autocomplete="off" formControlName="keyword"
                           nz-input placeholder="请输入任务名称" style="width: 220px; margin-right: 10px;" />
                    <nz-select formControlName="orderStatus" nzShowSearch nzPlaceHolder="请选择订单状态" style="margin: 10px 0 0 10px;width:220px;bottom:0px">
                        <nz-option *ngFor="let item of orderStatusList" [nzValue]="item.key"
                                   [nzLabel]="item.value">
                        </nz-option>
                    </nz-select>
                    <nz-select formControlName="type" nzShowSearch nzPlaceHolder="请选择订单类型" style="margin: 10px 0 0 10px;width:220px;bottom:0px">
                        <nz-option *ngFor="let item of orderTypeList" [nzValue]="item.key"
                                   [nzLabel]="item.value">
                        </nz-option>
                    </nz-select>
                    <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;">
                        <i nz-icon nzType="search"></i>
                        查询
                    </button>
                    <button nz-button nzType="primary" (click)="reset()" style=" margin-left: 10px;">
                        <i nz-icon nzType="reload"></i>
                        重置
                    </button>
                </form>

<!--                <div class="pull-right">-->
<!--                    <button nz-button nzType="primary"-->
<!--                            [ngClass]="{'disabled': isArchiveUser === 'true'}"-->
<!--                            (click)="isArchiveUser === 'true' ? null : editModalVisible = true">-->
<!--                        <i nz-icon nzType="plus"-->
<!--                           nzTheme="outline"></i>-->
<!--                        创建账户-->
<!--                    </button>-->
<!--                </div>-->
            </div>
            <nz-table #vpcs style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="userList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                        <!-- <th width="30%">网络名称</th>
                        <th width="20%">状态</th>
                        <th width="30%">网段</th>
                        <th width="20%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of vpcs.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>{{ orderTypeMap[item.type] }}</td>
                        <td>{{ orderStatusMap[item.orderStatus] }}</td>
                        <td>{{ item.applyUserName }}</td>
                        <td>{{ item.ownerName }}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                     nz-popconfirm *ngIf="item.orderStatus === 'deploy_failed' "
                                     nzTooltipContent="top"
                                     [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要重新部署该任务吗？'"
                                     (nzOnConfirm)="isArchiveUser === 'true' ? null : redeploy(item);"
                                     [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="重新部署"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon-shuaxin iconfont icon">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm *ngIf="item.orderStatus === 'deploy_failed' "
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要关闭该任务吗？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : close(item);"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="关闭任务"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon-close1 iconfont icon">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="vpcs.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-system-account-edit
    [isVisible]="editModalVisible"
    type="add"
    [bean]="bean"
    (submit)="reload()"
    (refreshParent)="getOrderList(null)"
    (close)="editModalVisible = false">
</app-system-account-edit>