import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DatabaseRoutingModule } from './database-routing.module';
import { RedisComponent } from 'src/app/components/console/redis/index/redis.component';
import { RdsComponent } from 'src/app/components/console/rds/index/rds.component';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';

@NgModule({
    declarations: [RedisComponent, RdsComponent],
    imports: [CommonModule, DatabaseRoutingModule, SharedModule]
})
export class DatabaseModule {
    constructor(moduleRef: NgModuleRef<DatabaseModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
