import { Component, OnInit, OnDestroy } from '@angular/core';
import { EChartsOption } from 'echarts';
import { NzMessageService } from 'ng-zorro-antd/message'
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { ElasticPublicNetworkService } from 'src/app/service/console/elastic-public-network-ip/elastic-public-network.service';
import { environment } from 'src/environments/environment';
import { CacheService } from 'src/app/service/common/cache/cache.service';
import { TaskPollingService } from 'src/app/service/console/utils/task-polling.service';
import { FormBuilder, FormGroup} from "@angular/forms";
import { NzResizeEvent } from 'ng-zorro-antd/resizable';

const PRODUCT = 'publicNetworkIp';
const LIMIT = 1;

const BUSY_TEXT_MAP = {
  'bind': '绑定中',
  'unbind': '解绑中',
  'delete': '释放中',
};

const STATUS_TEXT_MAP = {
  'start': '启动中',
  'running': '分配中',
  'finish': '已完成',
  'error': '错误',
  'undo': '未运行',
};

const STATUS_CLASS_MAP = {
  'start': 'dot-blue',
  'running': 'dot-blue',
  'finish': 'dot-green',
  'error': 'dot-red',
  'undo': 'dot-gray',
};

const COLOR = {
  blue: '#117dbb',
  green: '#4da60c',
  lightBlue: '#d9eaf4',
  lightGreen: '#dbebce',
};

const QUEUE_LABEL = 'ipQueue';

@Component({
  selector: 'app-elastic-public-network-ip',
  templateUrl: './elastic-public-network-ip.component.html',
  styleUrls: ['./elastic-public-network-ip.component.less']
})
export class ElasticPublicNetworkIpComponent implements OnInit, OnDestroy {
  constructor(
      private msg: NzMessageService,
      private cache: CacheService,
      private taskPollingService: TaskPollingService,
      private pubNetworkService: ElasticPublicNetworkService,
      private fb: FormBuilder,
  ) {}

  isAdmin = window.localStorage.getItem('isAdmin') === 'true'
  rules = JSON.parse(window.localStorage.getItem("rules")).filter(item => item.service === 'elasticip').map(item => item.permissionType);
  cols = [
    {
      title: '弹性公网IP',
      ColumnKey: "ipAddress",
      allowSort: '',
      sortFlag: true,
      showSort: true,
      width: '400px'
    },
    {
      title: '状态',
      ColumnKey: "bindStatus",
      allowSort: '',
      sortFlag: true,
      showSort: false,
      width: '15%'
    },
    {
      title: '带宽',
      ColumnKey: "bandwidth",
      allowSort: '',
      sortFlag: true,
      showSort: true,
      width: '20%'
    },
    {
      title: '内网IP',
      ColumnKey: "",
      allowSort: '',
      sortFlag: false,
      showSort: false,
      width: '20%'
    },
    // {
    //     title: '绑定实例类型',
    //     ColumnKey: "bindType",
    //     allowSort: '',
    //     sortFlag: true,
    //     showSort: true,
    //     width: '10%'
    // },
    {
      title: '操作',
      allowSort: '',
      sortFlag: true,
      showSort: false,
      ColumnKey: ""
    }
  ];

  isArchiveUser = window.localStorage.getItem('isArchiveUser')
  isLoading: boolean = false;
  keyword: string = '';
  publicNetworkList = [];
  busyStatus = {};

  filters = {
    pageNum: 0 ,
    pageSize: environment.pageSize,
    orderBy1: false,
    orderName1: '',
    orderBy2: false,
    orderName2: '',
  };
  pager = {
    page: 1,
    pageSize: environment.pageSize,
    total: 0,
  };
  sortName = '';
  sortValue = false;
  openFlag:boolean = true;
  fristQuery:boolean = false;
  oldSortName;
  sort;
  index;

  // 绑定
  bindModalVisible: boolean = false;
  currentNetwork = {};

  // 资源限制
  resLimit: boolean = false;
  showResLimit: boolean = environment.showResLimit[PRODUCT];

  // 查看
  chartModalVisible: boolean = false;

  // 变更弹窗用
  changeModalVisible: boolean = false;
  isOkLoading = false;
  // formSubmitAttempt: boolean = false;
  ipItem: FormGroup;
  checkId: number = null;
  checkTime: number = 200;
  isGettingChart: boolean = false;
  timeList = [
    { name: '1/4小时', val: 25 },
    { name: '1/2小时', val: 50 },
    { name: '1小时', val: 100 },
    { name: '2小时', val: 200 },
  ];
  elasticChartOption: EChartsOption = {
    legend: {
      data: ['in', 'out'],
      align: 'left',
      right: 0,
      top: 10
    },
    tooltip: {
      trigger: 'axis'
    },
    title: {
      text: 'In/Out',
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLine: {
        lineStyle: {
          color: '#555',
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e1e1e1',
        }
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#555',
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e1e1e1',
        }
      },
    },
    series: [{
      name: 'in',
      data: [],
      type: 'line',
      itemStyle: {
        color: COLOR.blue,
        borderColor: COLOR.blue,
        opacity: 1,
      },
      lineStyle: {
        width: 1,
        opacity: 0.65
      },
      areaStyle: {
        color: COLOR.lightBlue,
        opacity: 0.4
      }
    }, {
      name: 'out',
      data: [],
      type: 'line',
      itemStyle: {
        color: COLOR.green,
        borderColor: COLOR.green,
        opacity: 1,
      },
      lineStyle: {
        width: 1,
        opacity: 0.65
      },
      areaStyle: {
        color: COLOR.lightGreen,
        opacity: 0.4
      }
    }]
  };
  ipDataEmpty: boolean = false;
  ipQueue = [];
  pollingTimer: number[] = [];

  ngOnInit() {
    this.getPublicNetworkList();
    this.initFbGroup();
    setInterval(()=> {
      this.getPublicNetworkList();
    }, 30000);
  }

  refresh(){
    this.pubNetworkService.refresh();
  }

  initFbGroup(){
    this.ipItem = this.fb.group({
      quotaId: '',
      quotaName: '',
      serviceName: '',
      bandwidthBefore: '',
      bandwidthAfter: '',
      unitBefore: '',
      unitAfter: '',
    });
  }

  getPublicNetworkList(filters?, noLoading?: boolean) {
    filters = filters || this.filters;
    let keyword = this.keyword.trim();
    let params = Object.assign({}, filters);
    if (keyword) {
      params.bean = {
        name: keyword
      }
    }

    if (!noLoading) {
      this.isLoading = true;
    }
    this.pubNetworkService.getNetworkList(params)
        .then(rs => {
          if (rs.success) {
            let list = rs.data.dataList || [];
            list.forEach(this.formatBindStatus);
            // //TODO
            // list.forEach(this.addQuotaData);
            // if (rs.data.pageNum === 0) {
            //     let ipQueue = this.cache.get(QUEUE_LABEL);
            //     if (ipQueue) {
            //         // 设置任务进度
            //         this.ipQueue = ipQueue;

            //         let pollingOptions = {
            //             queue: this.ipQueue,
            //             timer: this.pollingTimer,
            //             cacheKey: QUEUE_LABEL,
            //             callback: () => {
            //                 this.getPublicNetworkList(this.filters, true);
            //             }
            //         }
            //         this.taskPollingService.setPolling(pollingOptions);
            //     }
            //     this.publicNetworkList = this.ipQueue.concat(list);
            // } else {
            //     this.publicNetworkList = list;
            // }
            this.publicNetworkList = list;

            this.pager = {
              page: rs.data.pageNum + 1,
              pageSize: rs.data.pageSize,
              total: rs.data.recordCount,
            };

            // if (environment.resLimit[PRODUCT]) {
            //     if (this.publicNetworkList.length >= LIMIT && environment.freeAccount.indexOf(localStorage.getItem('username')) === -1) {
            //         // 如果个数超限 && 不在无视名单里， 则限制创建
            //         this.resLimit = true;
            //     } else {
            //         this.resLimit = false;
            //     }
            // }
          } else {
            this.msg.error(`获取弹性公网列表失败${ rs.message ? ': ' + rs.message : '' }`);
          }

          this.isLoading = false;
        })
        .catch(err => {
          this.msg.error('获取弹性公网列表失败');
          this.isLoading = false;
        });
  }

  // //TODO
  // addQuotaData(item): void {
  //     item.quota = {quotaId:'10', quotaName:'XXXXXAAAA001',bandwidthBefore:'500', bandwidthAfter:'1000', serviceName:'弹性公网服务器名称001'};

  // }

  handleCancelChange() {
    this.changeModalVisible = false;
    // this.formSubmitAttempt = false;
    this.ipItem.reset();
  }

  trackById(item) {
    return item.id;
  }

  pageChanged(pageNum) {
    this.filters.pageNum = pageNum - 1;
    this.getPublicNetworkList();
  }

  search() {
    this.filters.pageNum = 0;
    this.getPublicNetworkList();
  }

  reload() {
    this.keyword = '';
    this.search();
  }

  getBusyText(item): string {
    return BUSY_TEXT_MAP[this.busyStatus[item.id]] || '';
  }

  getStatusText(item) {
    let status = item.status || 'finish'
    return STATUS_TEXT_MAP[status];
  }

  getStatusClass(item) {
    let status = item.status || 'finish'
    return STATUS_CLASS_MAP[status];
  }

  formatBindStatus(item): void {
    if (item.ipBinding && item.ipBinding[0].vmId) {
      item.bindType = '云服务器';
      item.bindStatus = true;
      item.bindName = item.ipBinding[0].vmName;
    } else if (item.ipBinding && item.ipBinding[0].virtualServerId) {
      item.bindType = '负载均衡';
      item.bindStatus = true;
      item.bindName = item.ipBinding[0].virtualServerName;
    } else {
      item.bindStatus = false;
    }
  }

  canBindIp(item) {
    if (item.orderId) {
      return false;
    }
    return !item.portId;
  }

  bindIp(item) {
    if (!this.canBindIp(item)) {
      return;
    }
    this.currentNetwork = item;
    this.bindModalVisible = true;
  }

  canChangeIp(item) {
    if (!item.quota) {
      return false;
    }
    return true;
  }

  showChangeIp(item) {
    if (!this.canChangeIp(item)) {
      return;
    }
    this.ipItem.patchValue(item.quota);
    this.changeModalVisible = true;
  }

  changeIp() {

  }

  canUnbindIp(item) {

    return item.portId //&& item.bindType === '云服务器';
  }

  unbindIp(item) {
    if (!this.canUnbindIp(item)) {
      return;
    }
    this.busyStatus[item.id] = 'unbind';
    this.pubNetworkService.unbindIp(item.id)
        .then(rs => {
          if (rs.success) {
            this.msg.success('弹性公网IP解绑成功');
            this.getPublicNetworkList();
          } else {
            this.msg.error(`弹性公网IP解绑失败${ rs.message ? ': ' + rs.message : '' }`);
          }

          this.busyStatus[item.id] = '';
        })
        .catch(err => {
          this.msg.error('弹性公网IP解绑失败');
          this.busyStatus[item.id] = '';
        });
  }

  canReleaseIp(item) {
    if (item.orderId) {
      return false;
    }
    return !item.portId;
  }

  releaseIp(item) {
    if (!this.canReleaseIp(item)) {
      return;
    }
    this.busyStatus[item.id] = 'delete';
    this.pubNetworkService.release(item.id)
        .then(rs => {
          if (rs.success) {
            this.pubNetworkService.refresh();
            this.msg.success('弹性公网IP释放成功');
            this.getPublicNetworkList();
          } else {
            this.msg.error(`弹性公网IP释放失败${ rs.message ? ': ' + rs.message : '' }`);
          }

          this.busyStatus[item.id] = '';
        })
        .catch(err => {
          this.msg.error('弹性公网IP释放失败');
          this.busyStatus[item.id] = '';
        });
  }

  canCheckChart(item) {
    if (item.orderId) {
      return false;
    }
    return item.portId;
  }

  checkChart(item) {
    if (!this.canCheckChart(item)) {
      return;
    }
    this.checkId = item.id;
    this.checkTime = 200;
    this.getNetworkChart(this.checkId, this.checkTime);
    this.chartModalVisible = true;
  }

  getNetworkChart(id, item) {
    this.isGettingChart = true;
    this.pubNetworkService.getMetrics(id, item)
        .then(rs => {
          let inList = [], outList = [];
          for (const item of rs) {
            if (item.name === 'in') {
              inList = this.formatArray(item.data);
            } else if (item.name === 'out') {
              outList = this.formatArray(item.data);
            }
          }

          if (!inList[0].length && !outList[1].length) {
            setTimeout(() => {
              this.ipDataEmpty = true;
            }, 300);
          } else {
            this.ipDataEmpty = false;
          }

          this.elasticChartOption.xAxis['data'] = inList[0];
          this.elasticChartOption.series[0]['data'] = inList[1];
          this.elasticChartOption.series[1]['data'] = outList[1];
          this.elasticChartOption = Object.assign({}, this.elasticChartOption);
          this.isGettingChart = false;
        })
        .catch(err => {
          this.isGettingChart = false;
        })
  }

  checkTimeChange(checkTime) {
    this.getNetworkChart(this.checkId, checkTime);
  }

  refreshChart() {
    this.getNetworkChart(this.checkId, this.checkTime);
  }

  formatArray(arr) {
    const timeList = [], valueList = [];
    for (const item of arr) {
      timeList.push(item.time);
      if (item.value || item['value'] == 0) {
        valueList.push(item.value);
      } else {
        valueList.push(item.doubleValue);
      }
    }
    return [timeList, valueList];
  }

  ngOnDestroy() {
    while(this.pollingTimer.length) {
      let timer = this.pollingTimer.shift();
      clearInterval(timer);
    }
  }

  onResize({ width }: NzResizeEvent, col: string): void {
    this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
    if (this.index !== undefined && this.index !== '') {
      this.cols[this.index].allowSort = this.sort;
    }
    this.openFlag = false;
  }

  onParamsChange(params: NzTableQueryParams) {
    if (this.fristQuery) {
      if (this.openFlag) {
        var checkData = false;
        var getIndex
        var index = -1;
        params.sort.forEach(sortDate => {
          index ++;
          if(sortDate.value) {
            this.sortName = sortDate.key;
            if (sortDate.value === 'ascend') {
              this.sort = sortDate.value;
              this.sortValue = false;
            } else {
              this.sortValue = true;
            }
            checkData = true;
            getIndex = index;
          }
        })
        this.index = getIndex;
        if (checkData) {
          var names = this.sortName.split(',');
          if (names.length == 2) {
            this.filters.orderBy2 = this.sortValue;
            this.filters.orderName2 = names[1];
          } else {
            this.filters.orderBy2 = this.sortValue;
            this.filters.orderName2 = '';
          }
          this.filters.orderBy1 = this.sortValue;
          this.filters.orderName1 = names[0];
          this.getPublicNetworkList();
        } else {
          this.filters.orderBy1 = false;
          this.filters.orderName1 = '';
          this.filters.orderBy2 = false;
          this.filters.orderName2 = '';
          this.getPublicNetworkList();
        }
      } else {
        this.openFlag = true;
      }
    } else {
      this.fristQuery = true;
    }
  }
  // setSnat(item): void {
  //     if (item.snat) {
  //         return;
  //     }
  //     this.busyStatus[item.id] = 'snat';
  //     this.pubNetworkService.snat(item.id)
  //         .then(rs => {
  //             if (rs.success) {
  //                 this.msg.success('访问互联网出口设置成功');
  //                 this.getPublicNetworkList();
  //             } else {
  //                 this.msg.error(`访问互联网出口设置失败${ rs.message ? ': ' + rs.message : '' }`);
  //             }

  //             this.busyStatus[item.id] = '';
  //         })
  //         .catch(err => {
  //             this.msg.error('访问互联网出口设置失败');
  //             this.busyStatus[item.id] = '';
  //         });
  // }
  permission(param: string) {
    return this.isAdmin || this.rules.includes(param)
  }
}
