// 增加 EventEmitter 最大监听器数量
require('events').EventEmitter.defaultMaxListeners = 30;

// 导入 http-proxy-middleware
const { createProxyMiddleware } = require('http-proxy-middleware');
const express = require('express');
const path = require('path');
const proxyConfig = require('./proxy.conf.json');

// 创建 Express 应用
const app = express();
const port = 4200;

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'dist/my-app')));

// 配置代理
Object.keys(proxyConfig).forEach(context => {
  const options = proxyConfig[context];
  const proxy = createProxyMiddleware(options);
  
  // 确保代理实例设置了足够的监听器数量
  if (proxy.server && typeof proxy.server.setMaxListeners === 'function') {
    proxy.server.setMaxListeners(30);
  }
  
  app.use(context, proxy);
});

// 所有其他请求返回 index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/my-app/index.html'));
});

// 启动服务器
const server = app.listen(port, () => {
  console.log(`应用运行在 http://localhost:${port}`);
});

// 设置服务器实例的最大监听器数量
server.setMaxListeners(30);

// 处理服务器关闭
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
