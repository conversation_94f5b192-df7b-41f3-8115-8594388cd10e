# Angular 14 到 Angular 15 升级总结

## 升级内容

1. **Angular核心包升级**
   - 从Angular 14.3.0升级到Angular 15.2.10
   - 使用命令：`ng update @angular/core@15 @angular/cli@15 --force`

2. **ng-zorro-antd升级**
   - 从ng-zorro-antd 14.3.0升级到ng-zorro-antd 15.1.1
   - 使用命令：`ng update ng-zorro-antd@15 --force`

3. **Angular CDK升级**
   - 从@angular/cdk 14.2.7升级到@angular/cdk 15.2.9
   - 通过手动修改package.json实现

4. **其他依赖升级**
   - ngx-filesaver从14.0.0升级到15.0.0
   - 将ngx-qrcode2替换为angularx-qrcode 16.0.0

5. **代码修改**
   - 更新shared.module.ts中的QR码模块导入
   - 更新recharge.component.html中的QR码组件用法

## 升级结果

1. **构建成功**
   - 使用`npm run build`命令成功构建项目
   - 没有出现编译错误

2. **开发服务器启动成功**
   - 使用`npm start`命令成功启动开发服务器
   - 服务器在http://localhost:4200/上运行

## 注意事项

1. **TypeScript警告**
   - 仍有TypeScript编译器选项"target"和"useDefineForClassFields"的警告
   - 可以在tsconfig.json中设置"target"为"ES2022"来消除此警告

2. **CSS选择器错误**
   - 构建过程中有3个CSS规则因选择器错误被跳过：
     - span.ant-radio+* -> Cannot read properties of undefined (reading 'type')
     - .ant-segmented-item-icon+* -> Cannot read properties of undefined (reading 'type')
     - .custom-file-input:lang(en)~.custom-file-label -> unmatched pseudo-class :lang

3. **性能考虑**
   - 开发服务器启动时间较长，可能需要优化项目结构或减少依赖
   - 初始包大小约为9.94MB，可以考虑进一步优化

## 后续建议

1. **进一步测试**
   - 全面测试应用程序的各个功能，确保升级没有引入新的问题
   - 特别关注使用QR码功能的部分，因为我们替换了相关库

2. **优化构建**
   - 考虑启用更多的构建优化选项，减小包大小
   - 检查并移除未使用的依赖

3. **解决CSS警告**
   - 调查并修复CSS选择器错误

4. **更新文档**
   - 更新项目文档，记录Angular 15的新特性和变化
