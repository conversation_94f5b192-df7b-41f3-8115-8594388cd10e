<div class="table-content">
    <ol class="on-breadcrumb">
        <li><span>云专线</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">云专线</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
               <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                    [routerLink]="'../cloud-provider-config'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建云专线
                     </a>
                </div>
                <!-- <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <a nz-button nzType="primary"
                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                    [routerLink]="isArchiveUser === 'true'? null : '../cloud-provider-config-quota'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建云专线
                     </a>
                </div> -->
            </div>
            <nz-table #wareList style="overflow-x: auto"
            [nzItemRender]="renderItemTemplate"
            [nzLoading]="isLoading"
            [nzLoadingDelay]="300"
            [nzFrontPagination]="false"
            [nzTotal]="pager.total"
            [nzPageIndex]="pager.page"
            [nzPageSize]="pager.pageSize"
            (nzPageIndexChange)="pageChanged($event)"
            (nzQueryParams)="onParamsChange($event)"
            [nzData]="cloudProviderList">
            <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th
                          *ngIf="col.width"
                          nz-resizable
                          nzBounds="window"
                          nzPreview
                          [nzWidth]="col.width"
                          [nzMaxWidth]="300"
                          [nzMinWidth]="60"
                          [nzShowSort]="col.showSort"
                          [nzSortFn]="col.sortFlag"
                          [nzSortOrder]="col.allowSort"
                          [nzColumnKey]="col.ColumnKey"
                          (nzResizeEnd)="onResize($event, col.title)"
                        >
                          {{ col.title }}
                          <nz-resize-handle nzDirection="right">
                            <div class="resize-trigger"></div>
                          </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width" style="min-width: 120px;">
                          {{ col.title }}
                        </th>
                      </ng-container>
                </tr>
            </thead>
            <tbody>
                <tr
                    *ngFor="let item of wareList.data; trackBy: trackById">
                    <td>{{ item.name }}</td>
                    <td>{{ item.localSubNetwork }}</td>
                    <td class="break-all">{{ item.otherSubNetwork }}</td>
                    <td>
                        <div class="on-table-actions"
                            [hidden]="busyStatus[item.id]">
                            <!-- <div class="on-table-action-item"
                                nz-popconfirm
                                (nzOnConfirm)="cloudProviderTest(item);">
                                <i nzTooltipTitle="测试"
                                    nzTooltipContent="bottom"
                                    nz-tooltip
                                    class="iconfont icon-ceshi"></i>
                            </div> -->
                           <!-- <div class="on-table-action-item"
                                nz-popconfirm
                                nzPlacement="top"
                                [nzTitle]="isArchiveUser === 'true' ? null : '确定要删除该云专线吗？'"
                                (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteWare(item);"
                                [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                <i nzTooltipTitle="删除"
                                nzTooltipContent="bottom"
                                    nz-tooltip
                                    class="icon fa fa-trash-o">
                                </i>
                            </div>-->
                        </div>
                        <div class="on-table-actions"
                            [hidden]="!busyStatus[item.id]">
                            <div
                                class="action-loading-placeholder">
                                <i class="icon" nz-icon
                                    [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="wareList.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
            </tbody>
        </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
