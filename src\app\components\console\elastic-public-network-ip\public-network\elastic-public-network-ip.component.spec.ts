import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';

import { ElasticPublicNetworkIpComponent } from './elastic-public-network-ip.component';

describe('IndexComponent', () => {
    let component: ElasticPublicNetworkIpComponent;
    let fixture: ComponentFixture<ElasticPublicNetworkIpComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            declarations: [ElasticPublicNetworkIpComponent]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(ElasticPublicNetworkIpComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
