<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><span>专有网络</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入网络名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <!--                <div class="pull-right" *ngIf="isAdmin === 'true'">-->
            <!--                    <button nz-button nzType="primary"-->
            <!--                        [ngClass]="{'disabled': true}"-->
            <!--                        (click)="networkModalVisible = true">-->
            <!--                        <i nz-icon nzType="plus"-->
            <!--                            nzTheme="outline"></i>-->
            <!--                        创建专有网络-->
            <!--                    </button>-->
            <!--                </div>-->
            <div class="right-button-group">
                <div *ngIf="permission('refresh')">
                    <a nz-button [ngClass]="{'disabled': isArchiveUser === 'true'}" class="default"
                       (click)="refreshVPC()">
                        刷&nbsp;新
                    </a>
                </div>
                <div *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary dtp"
                       [ngClass]="{'disabled': isArchiveUser === 'true'}"
                       (click)="isArchiveUser === 'true' ? null : networkModalVisible = true">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建专有网络
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">专有网络</span>
            </div>
            <nz-table #vpcs style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="vpcList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                        <!-- <th width="30%">网络名称</th>
                        <th width="20%">状态</th>
                        <th width="30%">网段</th>
                        <th width="20%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of vpcs.data; trackBy: trackById">
                        <td>{{ item.resourceId }}</td>
                        <td>{{ item.name }}</td>
                        <td><span class="dot dot-green">{{item.ecStatus}}</span></td>
<!--                        <td>{{ item.routerId }}</td>-->
<!--                        <td>-->
<!--                            <div *ngFor="let ovdcNetworkList of item.ovdcNetworkList">-->
<!--                                <span *ngIf="ovdcNetworkList">-->
<!--                                    <span *ngFor="let ipScopeList of ovdcNetworkList.ipScopeList">-->
<!--                                    {{ getIpScopeList(ipScopeList) }}-->
<!--                                    </span>-->
<!--                                </span>-->
<!--                            </div>-->
<!--                        </td>-->
                        <td>
                            <div class="on-table-actions" *ngIf="permission('view')"
                                [hidden]="busyStatus[item.id]">
                                <a class="on-table-action-item"
                                    [routerLink]="['../detail', item.id]">
                                    <i nzTooltipTitle="详情"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        nz-icon
                                        nzType="cluster"
                                        nzTheme="outline"
                                        class="icon"></i>
                                </a>

                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该专有网络吗？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteVpc(item);"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="vpcs.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-network-config
    [isVisible]="networkModalVisible"
    type="vpc"
    (submit)="reload()"
    (close)="networkModalVisible = false">
</app-network-config>

<app-network-config (refreshParent)="getVpcList(null)"></app-network-config>