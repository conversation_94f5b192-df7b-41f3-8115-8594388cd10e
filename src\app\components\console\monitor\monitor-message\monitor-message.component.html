<div class="table-content">
<!--  <ol class="on-breadcrumb">-->
<!--    <li><a routerLink="../monitor-manage">云监控</a></li>-->
<!--    <li><span>告警通知</span></li>-->
<!--  </ol>-->
  <div class="on-panel">
    <div class="on-panel-header">
      <h3 class="title">告警通知</h3>
    </div>
    <div class="on-panel-body">
        <div class="action-bar clearfix">
            <!-- 选择虚拟机 -->
            <div class="choose-box">
                <p>告警源</p>
                <nz-select style="width: 300px;"
                    [(ngModel)]="selectSourceValue" >
                    <nz-option *ngFor="let option of selectSourceOption" 
                                [nzValue]="option.resourceId" 
                                [nzLabel]="option.resourceName">
                    </nz-option>
                </nz-select>
            </div>
            <!-- 选择告警状态 -->
            <div class="choose-box">
                <p>告警状态</p>
                <nz-select style="width: 180px"
                    nzPlaceHolder="请选择"
                    [(ngModel)]="selectMsgType">
                    <nz-option *ngFor="let item of msgTypeOption"
                                [nzLabel]="item.title"
                                [nzValue]="item.value">
                    </nz-option>
                </nz-select>
            </div>
            <!-- 选择告警等级 -->
            <div class="choose-box">
                <p>告警等级</p>
                <nz-select style="width: 180px;" 
                    nzShowSearch
                    [(ngModel)]="msgDegree">
                    <nz-option nzCustomContent nzLabel="全部" nzValue="all">
                        <i class="fa fa-warning"></i> 全部
                    </nz-option>
                    <nz-option nzCustomContent nzLabel="警告" nzValue="WARNING">
                        <i class="fa fa-warning warning"></i> 警告
                    </nz-option>
                    <nz-option nzCustomContent nzLabel="紧急" nzValue="IMMEDIATE">
                        <i class="fa fa-warning immediate"></i> 紧急
                    </nz-option>
                    <nz-option nzCustomContent nzLabel="严重" nzValue="CRITICAL">
                        <i class="fa fa-warning critical"></i> 严重
                    </nz-option>
                </nz-select>
            </div>
            <!-- 选择时间 -->
            <div class="choose-box">
                <p>时间范围</p>
                <nz-range-picker [nzAllowClear]=false
                                 [(ngModel)]="selectTimeValue"
                                 (ngModelChange)="selectTime($event)"
                                 [nzShowTime]="{ nzFormat: 'HH:mm' }"
                                 nzFormat="yyyy-MM-dd HH:mm">
                </nz-range-picker>
            </div>
            <div class="choose-box">
                <button nz-button nzType="primary" (click)="refresh()">
                    <i nz-icon nzType="redo"
                        nzTheme="outline"></i>
                    刷新
                </button>
            </div>
        </div>
      <!--内容-->
      <nz-table #messageList
            [nzItemRender]="renderItemTemplate"
            [nzBordered]=true
            [nzFrontPagination]="false"
            [nzTotal]="page.total"
            [nzPageIndex]="page.current"
            [nzPageSize]="page.size"
            (nzPageIndexChange)="pageChange($event)"
            [nzData]="dataList">
      <thead>
      <tr>
          <th width="20%">名称</th>
          <th width="20%">告警源</th>
          <th>告警等级</th>
          <th>开始时间</th>
          <th>状态</th>
          <th>更多操作</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let item of messageList.data">
          <td>{{ item.alertDefinitionName || '-' }}</td>
          <td>{{ item.resourceName }}</td>
          <td>
            <i class="fa fa-warning {{getStatusClass(item)}}"></i>
            <span class="margin-span">{{ item.alertLevel ? getStatusText(item) : '-' }}</span>
          </td>
          <td>{{ getdate(item.startTimeUTC) }}</td>
          <td>
            <span class="dot {{ getStatusTypeClass(item.status) }}">{{ getStatusTypeText(item.status) }}</span>
          </td>
          <!--更多操作-->
          <td>
            <div class="on-table-actions"
                [hidden]="busyStatus[item.alertId]">
                <div class="on-table-action-item"
                    nz-popconfirm
                    [ngClass]="{'disabled': !canCancel(item)}"
                    [nzCondition]="!canCancel(item)"
                    nzTooltipContent="top"
                    nzTitle="确定要取消该告警通知吗？"
                    (nzOnConfirm)="cancelAlert(item)">
                    <i nzTitle="取消"
                        nzTooltipContent="bottom"
                        nz-tooltip
                        class="icon fa fa-bell-slash-o"></i>
                </div>
                <div class="on-table-action-item"
                    nz-popconfirm
                    nzTooltipContent="top"
                    nzTitle="确定要删除该告警通知吗？"
                    (nzOnConfirm)="deleteAlert(item)">
                    <i nzTitle="删除"
                        nzTooltipContent="bottom"
                        nz-tooltip
                        class="icon fa fa-trash-o"></i>
                </div>
            </div>
            <div class="on-table-actions"
                [hidden]="!busyStatus[item.alertId]">
                <div
                    class="action-loading-placeholder">
                    <i class="icon" nz-icon
                        [nzType]="'loading'"></i>
                    {{ getBusyText(item) }}
                </div>
            </div>
          </td>
      </tr>
      </tbody>
      </nz-table>
      <ng-template #renderItemTemplate let-type let-page="page">
        <a *ngIf="type === 'prev'">« 上一页</a>
        <a *ngIf="type === 'next'">下一页 »</a>
        <a *ngIf="type === 'page'">{{ page }}</a>
      </ng-template>
    </div>
  </div>
</div>