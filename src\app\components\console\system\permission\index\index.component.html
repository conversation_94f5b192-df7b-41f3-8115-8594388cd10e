<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">运维管理</a></li>-->
<!--        <li><a routerLink="../permission"><span>权限管理</span></a></li>-->
<!--        <li><span>默认权限</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">
                默认权限
<!--                <small class="danger" *ngIf="showResLimit">-->
<!--                    <i class="fa fa-exclamation-circle"></i>-->
<!--                    每个用户最多可免费创建1个Kubernetes集群，如有其它需求请提交工单联系！-->
<!--                </small>-->
            </h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
<!--                <form nz-form nzLayout="inline" [formGroup]="searchForm">-->
                    <span style="padding-left:20px">服务类别：</span>
                    <nz-select [(ngModel)]="service" nzShowSearch nzPlaceHolder="请选择类型" style="margin-left: 10px;width:150px" (ngModelChange)="getDataList()">
                        <nz-option *ngFor="let item of SERVICE" [nzValue]="item.key"
                                   [nzLabel]="item.value">
                        </nz-option>
                    </nz-select>
<!--                </form>-->
<!--                <div class="pull-right">-->
<!--                    <a nz-button nzType="primary" style="color:#fff;"-->
<!--                       [ngClass]="{'disabled': resLimit}"-->
<!--                       (click)="refresh()">-->
<!--                        <i class="icon-shuaxin iconfont icon"></i>-->
<!--                        &nbsp;&nbsp;刷新-->
<!--                    </a>-->
<!--                </div>-->
            </div>
            <nz-table #tableList [nzLoading]="isLoading"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                                    *ngIf="col.width"
                                    nz-resizable
                                    nzBounds="window"
                                    nzPreview
                                    [nzWidth]="col.width"
                                    [nzMaxWidth]="400"
                                    [nzMinWidth]="60"
                                    [nzShowSort]="col.showSort"
                                    [nzSortFn]="col.sortFlag"
                                    [nzSortOrder]="col.allowSort"
                                    [nzColumnKey]="col.ColumnKey"
                                    (nzResizeEnd)="onResize($event, col.title)"
                            >
                                {{ col.title }}
                                <nz-resize-handle nzDirection="right">
                                    <div class="resize-trigger"></div>
                                </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style=" min-width:275px">
                                {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.serviceText}}</td>
                        <td>{{data.permissionTypeText}}
                        </td>
                        <td>
<!--                            <button class="btn" [ngClass]="{'active-btn': data.enabledStatus}" (click)="active(data)">{{data.enabledStatus?'启用':'禁用'}}</button>-->
                            <nz-switch [(ngModel)]="data.enabledStatus" (ngModelChange)="active(data)"></nz-switch>
                        </td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item"
                                     (click)="editUserTemplate(data);"
                                     [ngClass]="{'disabled': data.enabledStatus}"
                                >
                                    <i nzTooltipTitle="配置用户权限"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="user" nzTheme="outline"></i>
                                </div>
<!--                                <div class="on-table-action-item"-->
<!--                                     (click)="downloadDialog(data.id);"-->
<!--                                     [ngClass]="{'disabled': !canGoToDetail(data)}"-->
<!--                                >-->
<!--                                    <i nzTitle="下载kubectl配置文件"-->
<!--                                       nzTooltipContent="bottom"-->
<!--                                       nz-tooltip-->
<!--                                       class="icon iconfont icon-download"></i>-->
<!--                                </div>-->
<!--                                <div class="on-table-action-item"-->
<!--                                    (click)="turnOn(data);"-->
<!--                                    [ngClass]="{'disabled': !canTurnOn(data)}">-->
<!--                                    <i nzTitle="启动"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        class="icon fa fa-play-circle-o"></i>-->
<!--                                </div>-->
<!--                                <div class="on-table-action-item"-->
<!--                                    nz-popconfirm-->
<!--                                    nzTooltipContent="top"-->
<!--                                    [nzCondition]="!canDelete(data)"-->
<!--                                    nzTitle="确定要删除该集群吗？"-->
<!--                                    (nzOnConfirm)="delete(data);"-->
<!--                                    [ngClass]="{'disabled': !canDelete(data)}">-->
<!--                                    <i nzTitle="删除"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        class="icon fa fa-trash-o"></i>-->
<!--                                </div>-->
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[data.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
<!--                                    {{ getBusyText(data) }}-->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="editUserTemplateWindow" nzTitle="配置用户权限"
          (nzOnCancel)="editUserTemplateWindow = false" [nzWidth]="600"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submit()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">选择用户：</span>
<!--        <nz-checkbox-wrapper style="width: 100%;" [(ngModel)]="userTemplateList">-->
<!--            <nz-row>-->
<!--                <nz-col nzSpan="8" *ngFor="let user of userTemplateList">-->
<!--                    <label nz-checkbox [nzValue]="user.value" [ngModel]="user.checked">{{user.label}}</label>-->
<!--                </nz-col>-->
<!--            </nz-row>-->
<!--        </nz-checkbox-wrapper>-->

        <nz-checkbox-group class="action-bar-btn" [(ngModel)]="userTemplateList">
        </nz-checkbox-group>
    </div>
    </ng-container>
</nz-modal>