<nz-modal [(nzVisible)]="isVisible" [nzMaskClosable]="false"
          nzTitle="编辑IPSec VPN"
          nzOkText="提交" [nzOkLoading]="isCreating"
          [nzWidth]="600"
          [nzBodyStyle]="{padding: '0 24px'}"
          (nzAfterOpen)="modalOpened()"
          (nzOnCancel)="handleCancel()"
          (nzOnOk)="updateVpn()">
    <ng-container *nzModalContent>
    <div class="rds-config config-content">
    <div class="panel">
        <div class="panel-body">
            <form [formGroup]="vpn">
                <section class="field-section">
                    <div class="field-title">
                        基本信息
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <div class="field-item">
                                <label>
                                    <span class="label-text">名称</span>
                                    <input type="text" formControlName="name" READONLY/>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        共享密钥
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">共享密钥</span>
                                <div class="input-group-suffix">
                                    <input [type]="passwordVisible ? 'text' : 'password'" formControlName="password"
                                        (focus)="checkPasswordStatus()" (input)="validatePassword()" nz-tooltip
                                        nzTrigger="focus" placeholder="请输入密钥"
                                        (keydown.enter)="keyEnter()" class="clspass">
                                    <i class="suffix" [title]="passwordVisible ? '隐藏密钥' : '查看密钥'" nz-icon
                                        [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
                                        (click)="passwordVisible = !passwordVisible"></i>
                                </div>
                                <p class="small tip label-padding">密钥须包含大小写字母和数字，支持常用特殊字符，长度为8-20位</p>
                            </label>
                            <div class="label-padding">
                                <div class="password-hint" [ngClass]="{'unfold': passwordStatus.focus}">
                                    <ul>
                                        <li [ngClass]="{'checked': passwordStatus.uppercase}">
                                            <i class="icon" nz-icon
                                                [nzType]="passwordStatus.uppercase ? 'check-circle' : 'exclamation-circle'"
                                                nzTheme="fill"></i>
                                            至少包含一个大写字母
                                        </li>
                                        <li [ngClass]="{'checked': passwordStatus.lowercase}">
                                            <i class="icon" nz-icon
                                                [nzType]="passwordStatus.lowercase ? 'check-circle' : 'exclamation-circle'"
                                                nzTheme="fill"></i>
                                            至少包含一个小写字母
                                        </li>
                                        <li [ngClass]="{'checked': passwordStatus.digital}">
                                            <i class="icon" nz-icon
                                                [nzType]="passwordStatus.digital ? 'check-circle' : 'exclamation-circle'"
                                                nzTheme="fill"></i>
                                            至少包含一个数字
                                        </li>
                                        <li [ngClass]="{'checked': passwordStatus.length}">
                                            <i class="icon" nz-icon
                                                [nzType]="passwordStatus.length ? 'check-circle' : 'exclamation-circle'"
                                                nzTheme="fill"></i>
                                            长度在8-20位之间
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="form-hint error" *ngIf="isInvalid(vpn.get('password'))">
                                <div *ngIf="vpn.get('password').hasError('required')">
                                    密钥不能为空
                                </div>
                                <div *ngIf="vpn.get('password').hasError('pwError')">
                                    密钥格式不符合要求
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        本地网络
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">本地ID</span>
                                <input required maxlength="50" type="text" formControlName="locaid"
                                    placeholder="请输入本地ID" (keydown.enter)="keyEnter()" (keyup)="keyup1(vpn.get('locaid'))" (blur)="checkLocalId(vpn.get('locaid'))" />
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vpn.get('locaid'))">
                                <div *ngIf="vpn.get('locaid').hasError('required')">
                                    本地ID不能为空
                                </div>
                                <div *ngIf="vpn.get('locaid').hasError('maxlength')">
                                    本地ID长度不能超过{{ vpn.get('locaid').errors.maxlength.requiredLength }}个字符
                                </div>
                            </div>
                            <div *ngIf="localIdFlag" class="form-hint error">
                                本地ID不符合规范
                            </div>
                        </div>
                    </div>

                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">本地端点</span>
                                <nz-select style="width: 240px;" nzDropdownMatchSelectWidth formControlName="memoryGb" nzShowSearch nzPlaceHolder="请选择本地端点">
                                    <nz-option *ngFor="let item of initData.localIPList" [nzValue]="item"
                                        [nzLabel]="item">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>

                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">本地子网</span>
                                <input required maxlength="50" type="text" formControlName="localsubnet" (blur)="validateLocalsubnet()"
                                    placeholder="请输入本地子网" (keydown.enter)="keyEnter()" />
                                <p class="small tip label-padding tip-p">应采用CIDR格式输入子网并以逗号分为分隔符</p>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vpn.get('localsubnet'))">
                                <div *ngIf="vpn.get('localsubnet').hasError('required')">
                                    本地子网不能为空
                                </div>
                                <div *ngIf="vpn.get('localsubnet').hasError('maxlength')">
                                    本地子网长度不能超过{{ vpn.get('localsubnet').errors.maxlength.requiredLength }}个字符
                                </div>
                            </div>
                            <div class="form-hint error" *ngIf="localsubnetValid">
                                本地子网不符合规范
                            </div>
                            <div class="form-hint error" *ngIf="!localsubnetValid && localsubnetUnqValid">
                                本地子网之间不能重复
                            </div>
                        </div>
                    </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        对等网络
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">对等ID</span>
                                <input required maxlength="50" type="text" formControlName="reciprocityid"
                                    placeholder="请输入对等ID" (keydown.enter)="keyEnter()" (keyup)="keyup2(vpn.get('reciprocityid'))" (blur)="checkReciprocityId(vpn.get('reciprocityid'))"/>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vpn.get('reciprocityid'))">
                                <div *ngIf="vpn.get('reciprocityid').hasError('required')">
                                    对等ID不能为空
                                </div>
                                <div *ngIf="vpn.get('reciprocityid').hasError('maxlength')">
                                    对等ID长度不能超过{{ vpn.get('reciprocityid').errors.maxlength.requiredLength }}个字符
                                </div>
                            </div>
                            <div *ngIf="reciprocityidFlag" class="form-hint error">
                                对等ID不符合规范
                            </div>
                        </div>
                    </div>

                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">对等端点</span>
                                <input required maxlength="50" type="text" formControlName="reciprocityEndpoint"
                                    placeholder="请输入对等端点" (keydown.enter)="keyEnter()" />
                                <p class="small tip label-padding tip-p">端点应为有效的IP。</p>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vpn.get('reciprocityEndpoint'))">
                                <div *ngIf="vpn.get('reciprocityEndpoint').hasError('required')">
                                    对等端点不能为空
                                </div>
                                <div *ngIf="vpn.get('reciprocityEndpoint').hasError('maxlength')">
                                    对等端点长度不能超过{{ vpn.get('reciprocityEndpoint').errors.maxlength.requiredLength }}个字符
                                </div>
                                <div *ngIf="vpn.get('reciprocityEndpoint').hasError('pattern')">
                                    对等端点不符合规范
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">对等子网</span>
                                <input required maxlength="50" type="text" formControlName="reciprocitysubnet" (blur)="validateOthersubnet()"
                                    placeholder="请输入对等子网" (keydown.enter)="keyEnter()" />
                                <p class="small tip label-padding tip-p">应采用CIDR格式输入子网并以逗号分为分隔符</p>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vpn.get('reciprocitysubnet'))">
                                <div *ngIf="vpn.get('reciprocitysubnet').hasError('required')">
                                    对等子网不能为空
                                </div>
                                <div *ngIf="vpn.get('reciprocitysubnet').hasError('maxlength')">
                                    对等子网长度不能超过{{ vpn.get('reciprocitysubnet').errors.maxlength.requiredLength }}个字符
                                </div>
                            </div>
                            <div class="form-hint error" *ngIf="othersubnetValid">
                                对等子网不符合规范
                            </div>
                            <div class="form-hint error" *ngIf="!othersubnetValid && othersubnetUnqValid">
                                对等子网之间不能重复
                            </div>
                        </div>
                    </div>
                </section>

                <!-- NSX-V -->
                <section class="field-section" *ngIf="!vpn.value.nsxtSupport">
                    <div class="field-title">
                        安全配置
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">IKE 版本</span>
                                <nz-select formControlName="ikeVersion" nzShowSearch nzPlaceHolder="请选择IKE 版本">
                                    <nz-option *ngFor="let item of initData.IKEVersion" [nzValue]="item"
                                               [nzLabel]="item">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>

                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">摘要算法</span>
                                <nz-select formControlName="digest" nzShowSearch nzPlaceHolder="请选择摘要算法">
                                    <nz-option *ngFor="let item of initData.IPSecDigest" [nzValue]="item"
                                               [nzLabel]="item">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>

                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">加密算法</span>
                                <nz-select formControlName="encryption" nzShowSearch nzPlaceHolder="请选择加密算法">
                                    <nz-option *ngFor="let item of initData.IPSecEncryption" [nzValue]="item"
                                               [nzLabel]="item">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">身份验证</span>
                                <label  style="font-size:13px;">PSK</label>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">Hellman 组</span>
                                <nz-select formControlName="hellmanGroup" nzShowSearch nzPlaceHolder="请选择Hellman 组">
                                    <nz-option *ngFor="let item of initData.IPSecHellmanGroup" [nzValue]="item"
                                               [nzLabel]="item">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                </section>

                <!-- NSX-T -->
                <section class="field-section" *ngIf="vpn.value.nsxtSupport">
                    <div class="field-title">
                        安全配置
                    </div>
                    <h5>IKE 配置文件</h5>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">版本</span>
                                <nz-select formControlName="ikeConfigurationVersion" nzShowSearch nzPlaceHolder="请选择版本" (ngModelChange)="changeIkeConfigurationVersion()">
                                    <nz-option *ngFor="let item of config.IkeVersionType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">加密</span>
                                <nz-select formControlName="ikeConfigurationEncryption" nzShowSearch nzPlaceHolder="请选择加密">
                                    <nz-option *ngFor="let item of config.IkeEncryptionAlgorithmType" [nzValue]="item.key"
                                               [nzLabel]="item.value" [nzDisabled]="item.disabled">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group" *ngIf="vpn.value.ikeConfigurationEncryption.indexOf('GCM') < 0">
                        <div class="field-item">
                            <label>
                                <span class="label-text">摘要</span>
                                <nz-select formControlName="ikeConfigurationDigest" nzShowSearch nzPlaceHolder="请选择摘要">
                                    <nz-option *ngFor="let item of config.IkeDigestAlgorithmType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text">Diffie-Hellman 组</span>
                                <nz-select formControlName="ikeConfigurationDhGroup" nzShowSearch nzPlaceHolder="请选择 Diffie-Hellman 组">
                                    <nz-option *ngFor="let item of config.DhGroupType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <h5>通道配置</h5>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text" style="width:150px">启用完全向前保密：</span>
                                <nz-select formControlName="perfectForwardSecrecyEnabled" nzShowSearch nzPlaceHolder="请选择">
                                    <nz-option *ngFor="let item of perfectForwardSecrecyEnabledList" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text" style="width:150px">碎片整理策略：</span>
                                <nz-select formControlName="dfPolicy" nzShowSearch nzPlaceHolder="请选择碎片整理策略">
                                    <nz-option *ngFor="let item of config.DfPolicyType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text" style="width:150px">加密：</span>
                                <nz-select formControlName="tunnelConfigurationEncryption" nzShowSearch nzPlaceHolder="请选择加密">
                                    <nz-option *ngFor="let item of config.TunnelEncryptionAlgorithmType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group" *ngIf="vpn.value.tunnelConfigurationEncryption === 'AES_128' || vpn.value.tunnelConfigurationEncryption === 'AES_256'">
                        <div class="field-item">
                            <label>
                                <span class="label-text" style="width:150px">摘要：</span>
                                <nz-select formControlName="tunnelConfigurationDigest" nzShowSearch nzPlaceHolder="请选择加密">
                                    <nz-option *ngFor="let item of config.TunnelDigestAlgorithmType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <span class="label-text" style="width:150px">Diffie-Hellman 组：</span>
                                <nz-select formControlName="tunnelConfigurationDhGroup" nzShowSearch nzPlaceHolder="请选择加密">
                                    <nz-option *ngFor="let item of config.DhGroupType" [nzValue]="item.key"
                                               [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                </section>
            </form>
        </div>
    </div>
    </div>
    </ng-container>
</nz-modal>

