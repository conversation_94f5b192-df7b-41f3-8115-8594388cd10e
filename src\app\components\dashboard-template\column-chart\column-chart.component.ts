import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { EChartOption } from 'echarts';

export enum ColumnChartStyle {
  DEFAULT = 'default',
  DARK = 'dark',
  LIGHT = 'light',
  MINIMAL = 'minimal'
}

@Component({
  selector: 'app-column-chart',
  templateUrl: './column-chart.component.html',
  styleUrls: ['./column-chart.component.less']
})
export class ColumnChartComponent implements OnInit {
  @ViewChild('chartContainer') chartContainer!: ElementRef;
  @Input() xAxisData: string[] = [];
  @Input() seriesData: number[] = [];
  @Input() style: ColumnChartStyle = ColumnChartStyle.DEFAULT;
  @Input() height: string = '400px';

  private chart: echarts.ECharts | null = null;

  ngOnInit(): void {
    this.initChart();
  }

  private initChart(): void {
    if (!this.chartContainer) return;

    this.chart = echarts.init(this.chartContainer.nativeElement);
    this.updateChart();
  }

  private updateChart(): void {
    if (!this.chart) return;

    const option = this.getChartOption();
    this.chart.setOption(option);
  }

  private getChartOption(): EChartOption {
    const baseOption: EChartOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: this.xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: this.seriesData,
        type: 'bar'
      }]
    };

    switch(this.style) {
      case ColumnChartStyle.DARK:
        return {
          ...baseOption,
          backgroundColor: '#2c3e50',
          textStyle: {
            color: '#ecf0f1'
          },
          series: [{
            ...baseOption.series[0],
            itemStyle: {
              color: '#3498db'
            }
          }]
        };
      case ColumnChartStyle.LIGHT:
        return {
          ...baseOption,
          backgroundColor: '#f5f5f5',
          textStyle: {
            color: '#333'
          },
          series: [{
            ...baseOption.series[0],
            itemStyle: {
              color: '#e74c3c'
            }
          }]
        };
      case ColumnChartStyle.MINIMAL:
        return {
          ...baseOption,
          grid: {
            top: '10%',
            left: '3%',
            right: '3%',
            bottom: '3%',
            containLabel: true
          },
          series: [{
            ...baseOption.series[0],
            itemStyle: {
              color: '#2ecc71'
            }
          }]
        };
      default:
        return baseOption;
    }
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.dispose();
    }
  }
}
