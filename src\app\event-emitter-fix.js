// 增加 Node.js EventEmitter 的最大监听器数量
// 这个文件会在应用启动时被加载
if (typeof window !== 'undefined' && window.process && window.process.EventEmitter) {
  window.process.EventEmitter.defaultMaxListeners = 20;
}

// 如果直接访问 Node.js 的 EventEmitter
if (typeof process !== 'undefined' && process.EventEmitter) {
  process.EventEmitter.defaultMaxListeners = 20;
}

// 对于 Node.js v0.12 或更高版本
// 注意：在浏览器环境中不需要此代码，已移除
// if (typeof require === 'function') {
//   try {
//     const events = require('events');
//     if (events && events.EventEmitter) {
//       events.EventEmitter.defaultMaxListeners = 20;
//     }
//   } catch (e) {
//     // 忽略错误，可能在浏览器环境中
//   }
// }
