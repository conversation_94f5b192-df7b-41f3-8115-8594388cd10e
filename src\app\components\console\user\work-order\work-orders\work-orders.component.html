<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="..">用户中心</a></li>-->
<!--        <li><a routerLink="../work-orders">工单管理</a></li>-->
<!--        <li><span>我的工单</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">我的工单</h3>
        </div>
        <div class="on-panel-body">
            <nz-table #workOrders
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                [nzData]="workOrderList">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="15%">编号</th>
                        <th width="30%">标题</th>
                        <th width="15%">分类</th>
                        <th width="15%">提交时间</th>
                        <th width="10%">状态</th>
                        <th width="10%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of workOrders.data; trackBy: trackById; index as index">
                        <td>{{ (pager.page - 1) * pager.pageSize + index + 1 }}</td>
                        <td>{{item.code}}</td>
                        <td class="fixed-td"
                            title="{{item.name}}">
                            {{item.name}}</td>
                        <td>{{initData.workSheetType[item.type]}}
                        </td>
                        <td>{{item.createTm}}</td>
                        <td>
                            <span class="dot {{ getStatusClass(item) }}">
                                {{initData.workSheetStatus[item.workSheetStatus]}}
                            </span>
                        </td>
                        <td>
                            <div class="on-table-actions">
                                <a class="on-table-action-item"
                                    [routerLink]="['../work-order-detail', item.id]">
                                    <i nzTitle="查看工单"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        nz-icon
                                        nzType="reconciliation"
                                        nzTheme="outline">
                                    </i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="workOrders.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>