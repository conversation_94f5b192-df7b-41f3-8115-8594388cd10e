# Angular 16 到 Angular 17 升级总结

本文档记录了将项目从Angular 16升级到Angular 17的过程、遇到的问题以及解决方案。

## 升级内容

1. **Angular核心包升级**
   - 从Angular 16.2.12升级到Angular 17.2.3
   - 使用命令：`npm install @angular/core@17 @angular/cli@17 --save --legacy-peer-deps`

2. **ng-zorro-antd升级**
   - 从ng-zorro-antd 16.2.2升级到ng-zorro-antd 17.2.0
   - 使用命令：`npm install ng-zorro-antd@17 --save --legacy-peer-deps`

3. **Angular CDK升级**
   - 从@angular/cdk 16.2.12升级到@angular/cdk 17.2.3
   - 使用命令：`npm install @angular/cdk@17 --save --legacy-peer-deps`

4. **其他依赖升级**
   - 升级所有Angular相关包到17.2.3版本
   - ngx-filesaver从16.0.0升级到17.0.0
   - ngx-bootstrap从11.0.2升级到12.0.0
   - angularx-qrcode从16.0.0升级到17.0.0
   - TypeScript从5.1.6升级到5.4.5

5. **新增依赖**
   - 添加@angular-eslint/builder和相关ESLint依赖，用于替代已弃用的tslint

6. **代码修改**
   - 更新main.ts文件，添加Angular 17的启动方式支持
   - 创建app.config.ts文件，用于Angular 17的应用配置
   - 更新tsconfig.json和tsconfig.base.json文件，添加esModuleInterop和strictInputAccessModifiers选项
   - 更新polyfills.ts文件，移除不再需要的IE11支持
   - 更新.browserslistrc文件，移除IE支持
   - 更新angular.json文件，将tslint替换为eslint
   - 创建.eslintrc.json文件，配置ESLint规则

## 升级结果

1. **Node.js版本要求**
   - Angular 17需要Node.js 18.13.0或更高版本
   - 推荐使用Node.js 18.17.1 LTS版本

2. **构建和开发服务器**
   - 更新了package.json中的scripts，添加了lint命令
   - 保留了原有的构建和启动命令

3. **新的应用架构**
   - 添加了app.config.ts文件，用于Angular 17的应用配置
   - 更新了main.ts文件，支持Angular 17的启动方式
   - 使用ESLint替代了TSLint进行代码检查

4. **性能改进**
   - Angular 17提供了更好的构建性能和运行时性能
   - 减少了应用程序的包大小
   - 改进了变更检测机制

## 遇到的问题及解决方案

1. **Node.js版本兼容性**
   - 问题：Angular 17需要Node.js 18.13.0或更高版本，而当前系统使用的是Node.js 16.20.2
   - 解决方案：需要安装Node.js 18.17.1或更高版本
   - 安装步骤：
     1. 下载Node.js 18.17.1 LTS版本：https://nodejs.org/download/release/v18.17.1/
     2. 安装Node.js
     3. 验证安装：`node -v` 应显示v18.17.1
     4. 清除npm缓存：`npm cache clean --force`
     5. 重新安装依赖：`npm install --legacy-peer-deps`

2. **ESLint替代TSLint**
   - 问题：Angular 17已经弃用TSLint，推荐使用ESLint
   - 解决方案：安装@angular-eslint/builder和相关依赖，创建.eslintrc.json配置文件

3. **TypeScript配置更新**
   - 问题：Angular 17需要更新的TypeScript配置
   - 解决方案：更新tsconfig.json和tsconfig.base.json文件，添加esModuleInterop和strictInputAccessModifiers选项

4. **浏览器兼容性**
   - 问题：Angular 17不再支持IE浏览器
   - 解决方案：更新.browserslistrc文件，移除IE支持

5. **依赖安装问题**
   - 问题：由于依赖关系复杂，直接使用ng update命令可能会失败
   - 解决方案：使用npm install命令手动安装各个依赖，并使用--legacy-peer-deps标志解决依赖冲突

6. **启动应用程序**
   - 问题：升级后可能会遇到各种运行时错误
   - 解决方案：
     1. 确保Node.js版本正确
     2. 运行 `npm start` 启动应用
     3. 根据控制台错误信息逐一修复问题
     4. 常见问题包括：
        - 模块导入路径错误
        - 组件API变更
        - 模板语法变更
        - 依赖版本不兼容
