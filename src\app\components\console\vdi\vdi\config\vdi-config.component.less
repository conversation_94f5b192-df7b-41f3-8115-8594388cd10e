.panel-header{margin-bottom: 20px}
.field-section{margin: 30px 0 5px;}
.config-content .field-group .field-item input[type=text],.config-content .field-group .field-item input[type=number]{
    margin:0 10px 0 0;
    width: 340px;
}
// 配置列表样式 - 参考vm-config
.server-config-list {
    display: inline-block;
    padding: 0;
    margin: 0;
    vertical-align: middle;

    li {
        border: 1px solid;
        display: inline-block;
        width: 67px;
        height: 70px;
        cursor: pointer;
        margin: 0 10px 10px 0;
        text-align: center;
        padding: 15px 0;
        background: #fff;
        color: #333333;
        border: 1px solid #333333;
        border-radius: 4px;
        font-size: 12px;
        transition: all 0.3s ease;

        &.active {
            color: #fff;
            border-color: #1890ff;
            background-color: #1890ff;
        }

        &:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        &.active:hover {
            color: #fff;
        }

        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f5f5f5;
            color: #999;
            border-color: #d9d9d9;

            &:hover {
                border-color: #d9d9d9;
                color: #999;
                background: #f5f5f5;
            }
        }

        p {
            margin-top: 3px;
            margin-bottom: 0;

            &:first-child {
                margin-top: 0;
            }

            &.charge {
                margin-top: 15px;
            }
        }
    }
}
