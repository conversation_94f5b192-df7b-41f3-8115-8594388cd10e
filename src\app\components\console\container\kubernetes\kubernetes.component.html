<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">容器服务</a></li>-->
<!--        <li><span>集群管理</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入集群名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div *ngIf="permission('refresh')">
                    <a nz-button *ngIf="permission('refresh')"
                       [ngClass]="{'disabled': resLimit}"
                       (click)="refresh()">
                        刷&nbsp;&nbsp;新
                    </a>
                </div>
                <div *ngIf="permission('create')">
                    <a nz-button class="primary"
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="resLimit ? null : '../k8s-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建Kubernetes
                    </a>
                </div>
            </div>

        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    集群管理
                    <!--                <small class="danger" *ngIf="showResLimit">-->
                    <!--                    <i class="fa fa-exclamation-circle"></i>-->
                    <!--                    每个用户最多可免费创建1个Kubernetes集群，如有其它需求请提交工单联系！-->
                    <!--                </small>-->
                </span>
            </div>
            <nz-table #tableList [nzLoading]="isLoading"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <th>集群名称</th>
                        <th>集群版本</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>子网</th>
                        <th>节点数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.displayName || '-'}}</td>
                        <td>{{data.platformVersion }}</td>
                        <td>{{data.k8sClusterType }} - {{data.k8sClusterSubType}}</td>
                        <td>
                            <!--                            <span-->
                            <!--                                class="dot {{ getStatusClass(data) }}">{{ getStatusText(data) }}</span>-->
                            {{data.phase }}
                        </td>
                        <td>
                            {{data.subnetName }}
                        </td>
                        <td>{{data.nodeList ? data.nodeList.length : '-'}}</td>
                        <td>{{data.creationTimestamp}}</td>
                        <td>
                            <div class="on-table-actions" *ngIf="permission('view')"
                                [hidden]="busyStatus[data.id]">
                                <div class="on-table-action-item"
                                     (click)="toDetail(data);"
                                     [ngClass]="{'disabled': !canGoToDetail(data)}">
                                    <i nzTitle="查看详情"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-search"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('export')"
                                     (click)="downloadDialog(data.id);"
                                     [ngClass]="{'disabled': !canGoToDetail(data)}"
                                >
                                    <i nzTitle="下载kubectl配置文件"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-download"></i>
                                </div>
<!--                                <div class="on-table-action-item"-->
<!--                                    (click)="turnOn(data);"-->
<!--                                    [ngClass]="{'disabled': !canTurnOn(data)}">-->
<!--                                    <i nzTitle="启动"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        class="icon fa fa-play-circle-o"></i>-->
<!--                                </div>-->
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzCondition]="!canDelete(data)"
                                    nzTitle="确定要删除该集群吗？"
                                    (nzOnConfirm)="delete(data);"
                                    [ngClass]="{'disabled': !canDelete(data)}">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[data.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(data) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="downloadModalVisible" nzTitle="下载kubectl配置文件" (nzOnCancel)="handleCancel()"
          (nzOnOk)="download()" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading" [nzWidth]="400">
    <ng-container *nzModalContent>
    <form class="config-content md network-form modalForm" [formGroup]="downloadItem" (submit)="download()">
        <section class="field-section">
            <div class="field-group">
                <div class="field-item topSty">
                    <label>
                        <span class="label-text">有效期：</span>
                        <nz-select formControlName="duration" nzShowSearch nzPlaceHolder="请选择有效期" style="width:240px">
                            <nz-option *ngFor="let item of periodList" [nzValue]="item.key"
                                       [nzLabel]="item.value">
                            </nz-option>
                        </nz-select>
                    </label>
                </div>
                <div class="field-item topSty">
                    <label>
                        <span class="label-text">文件类型：</span>
                        <nz-select formControlName="type" nzShowSearch nzPlaceHolder="请选择类型" style="width:240px">
                            <nz-option *ngFor="let item of fileTypeList" [nzValue]="item.key"
                                       [nzLabel]="item.value">
                            </nz-option>
                        </nz-select>
                    </label>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>