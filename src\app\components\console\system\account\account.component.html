<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">运维管理</a></li>-->
<!--        <li><span>账户管理</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入账户名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right">
                    <a nz-button nzType="primary" class="primary"
                            [ngClass]="{'disabled': isArchiveUser === 'true'}"
                            (click)="isArchiveUser === 'true' ? null : editModalVisible = true">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建账户
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">账户管理</span>
            </div>
            <nz-table #vpcs style="overflow-x: auto"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="userList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of vpcs.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>{{ item.tenantAdmin ? '管理员' : '用户' }}</td>
<!--                        <td>{{ item.isDelete }}</td>-->
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"  *ngIf="false"
                                    [routerLink]="['../edit', item.id]">
                                    <i nzTooltipTitle="修改"
                                       zTooltipContent="bottom"
                                       (click)="edit(item)"
                                       nz-tooltip
                                       class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="false"
                                     (click)="editPermission(item)"
                                     [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="权限"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-list">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该用户吗？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : delete(item);"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="vpcs.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">‹</a>
                <a *ngIf="type === 'next'">›</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-system-account-edit
    [isVisible]="editModalVisible"
    type="add"
    [bean]="bean"
    (submit)="reload()"
    (refreshParent)="getUserList(null)"
    (close)="editModalVisible = false">
</app-system-account-edit>

<nz-modal [(nzVisible)]="editPermissionWindow" nzTitle="权限设置"
          (nzOnCancel)="editPermissionWindow = false" [nzWidth]="800"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submitPermission()">
    <ng-container *nzModalContent>
    <div class="select-container" *ngFor="let item of editPermissionList">
        <p class="select-tips">{{item.key}}</p>
        <div class="per">
            <nz-checkbox-group class="action-bar-btn" [(ngModel)]="item.value">
            </nz-checkbox-group>
        </div>
    </div>
    </ng-container>
</nz-modal>