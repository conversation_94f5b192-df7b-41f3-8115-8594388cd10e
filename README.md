# Angular控制台组件优化

## 项目说明

webui是Angular 16项目，console控制台的整体样式是左边菜单，右边主控区。

## 全局要求（持续生效）

1.  **交互语言**：所有交互和沟通请使用中文。
2.  **文件查找**：当需要查找文件时，请直接索引整个项目进行查找，而不是询问文件位置。

## 服务计划模块整合

本次优化将服务计划模块整合到系统模块下作为二级目录：

1. 将服务计划从独立模块调整为系统模块的子功能
2. 调整菜单结构，将服务计划菜单项移动到运维管理下
3. 更新路由配置，使服务计划通过system模块访问
4. 删除不再需要的service-plan.module.ts和service-plan-routing.module.ts文件

### 优化内容

1. 系统模块整合：
   - 将服务计划组件添加到system.module.ts的declarations中
   - 在system-routing.module.ts中添加服务计划路由

2. 菜单调整：
   - 移除顶级服务计划菜单项
   - 在运维管理菜单下添加服务计划子菜单项

3. 路由配置：
   - 移除console-routing.module.ts中的独立服务计划路由
   - 在system-routing.module.ts中添加服务计划路由

4. 面包屑导航：
   - 更新服务计划组件的面包屑导航，添加返回运维管理的链接

### 优化效果

1. 简化了模块结构，减少了不必要的模块文件
2. 提高了代码的组织性，将相关功能整合到一起
3. 优化了菜单结构，使服务计划功能更合理地归类到运维管理下
4. 保持了原有功能的完整性，确保服务计划功能正常运行

## 服务计划搜索功能增强

本次优化为服务计划列表页面添加了类型筛选功能：

1. 在搜索栏旁边添加类型下拉框
2. 实现根据类型和搜索内容进行组合筛选
3. 优化搜索区域的样式布局

### 优化内容

1. 类型筛选功能：
   - 添加类型下拉框，显示所有服务计划类型
   - 实现类型选择后的筛选功能
   - 支持类型和关键词的组合筛选

2. 接口参数优化：
   - 修改查询参数构建逻辑，支持多条件查询
   - 添加类型变更事件处理

3. 样式优化：
   - 调整搜索区域布局，使其更加美观
   - 优化表单项间距和对齐方式
   - 确保类型下拉框垂直居中对齐

### 优化效果

1. 提升了用户体验，使筛选更加便捷
2. 增强了数据查询的灵活性，支持更精确的数据筛选
3. 保持了界面风格的一致性，与系统其他模块保持统一

## 服务计划表单优化

本次优化调整了服务计划新增和编辑表单的布局：

1. 统一同一行内所有输入框和下拉框的宽度
2. 优化表单项的布局和对齐方式
3. 改进服务组件列表的显示效果
4. 修复服务组件行中数量输入框换行问题
5. 参考network-config.component.html调整样式结构

### 优化内容

1. 控件宽度统一：
   - 为所有输入框、下拉框和日期选择器添加width: 100%样式
   - 修改flex布局属性，确保同一行的控件宽度一致

2. 特殊行处理：
   - 为价格和日期行添加特殊样式，使三个控件宽度相等
   - 为服务组件行添加特殊样式，根据内容重要性分配不同宽度
   - 设置服务组件行为flex-wrap: nowrap，防止控件换行

3. 样式优化：
   - 调整表单项的flex属性，从flex: 1改为flex: 1 1 0
   - 为不同类型的表单项添加特定的CSS类，便于精确控制样式
   - 优化删除按钮的垂直对齐方式，使其与其他控件垂直居中
   - 为服务组件行添加align-items: center，确保所有元素垂直居中

4. 结构调整：
   - 采用系统通用的field-section和field-group结构
   - 使用field-title替代section-title，保持与系统其他模块一致
   - 调整label结构，添加label-text类，符合系统规范
   - 将error-message替换为form-hint error，保持错误提示样式一致
   - 将"新增服务组件"按钮调整为右对齐，提升界面布局的一致性
   - 修复区域下拉框内容无法正常显示的问题，优化多选下拉框的样式和交互
   - 将生效日期和到期日期合并为一行，并添加时分秒选择功能
   - 为所有类型下拉框（服务计划类型、服务组件类型和子类型）添加"请选择"选项，对应空值
   - 实现服务组件子类型的条件禁用功能，只有当类型选择为"磁盘"时才能选择子类型
   - 将服务编码字段设置为只读，仅在编辑模式下显示
   - 提交表单时，如果regionsStr为空字符串，则传null
   - 修改服务组件子类型的处理逻辑，使其在为空值时传递null而不是空字符串
   - 统一日期时间处理，确保新增和修改时都以"yyyy-MM-dd HH:mm:ss"格式的字符串传递给后端
   - 优化列表页面操作按钮样式，参考cloud-server/instance组件的按钮样式
   - 优化服务组件删除按钮样式，与列表页面操作按钮保持一致

### 优化效果

1. 提升了表单的美观度和专业性
2. 改善了用户体验，使表单填写更加直观
3. 优化了空间利用，根据内容重要性合理分配控件宽度
4. 解决了服务组件行中控件换行的问题，使界面更加整洁
5. 确保了删除按钮与其他表单元素垂直居中对齐，提升了视觉一致性
6. 保持了与系统其他模块的样式一致性，提升了整体用户体验

## 第一阶段优化：菜单组件合并

本次优化将左侧菜单结构进行了整合和精简：

1. 原先结构：
   - console-layout.component.ts：主菜单组件
   - menuData.ts：主菜单数据源
   - console-sub-menu.component.ts：子菜单组件
   - subMenuData.ts：子菜单数据源

2. 优化后结构：
   - console-layout.component.ts：合并后的主菜单和子菜单组件
   - menuData.ts：合并后的菜单数据源

### 优化内容

#### 1. 数据源合并

将 `menuData.ts` 与 `subMenuData.ts` 合并成一组数据源：
- 为每个主菜单项添加 `subMenus` 数组属性
- 将原 `subMenuData` 中的子菜单数据整合到对应的主菜单项中

#### 2. 组件合并

将 `console-layout.component.ts` 与 `console-sub-menu.component.ts` 合并成一个文件：
- 整合了子菜单相关的方法和事件处理
- 将相关路由导航和菜单展开/折叠逻辑合并

#### 3. 样式合并

将子菜单的样式整合到主菜单样式中：
- 将 `console-sub-menu.component.less` 的样式添加到 `console-layout.component.less`
- 保持了原有的样式结构和效果

#### 4. 模板更新

更新HTML模板，将子菜单直接嵌入到主菜单组件中：
- 移除了对 `app-console-sub-menu` 组件的引用
- 直接在主菜单组件中展示子菜单内容

#### 5. 模块调整

更新 `console.module.ts`，移除对子菜单组件的引用。

### 问题修复

在合并过程中，我们解决了以下问题：

1. 数据结构处理：
   - 确保每个菜单项都有 `subMenus` 属性
   - 对没有子菜单的项目设置空数组，避免在运行时出现 undefined 问题

2. 模板条件渲染：
   - 添加了 `menu.subMenus && menu.subMenus.length > 0` 条件判断
   - 确保只有当菜单有子菜单项且菜单未折叠时才渲染子菜单

3. 保留组件引用：
   - 由于系统依赖，需要保留原始组件文件引用，而不是直接删除文件
   - 通过优化 module.ts 来移除不必要的引用

4. TypeScript编译错误修复：
   - 替换了 `flat()` 方法：使用 `reduce((acc, val) => acc.concat(val), [])` 代替，解决不兼容ES2019的问题
   - 修复属性类型错误：对动态属性 `unfold` 使用类型断言 `(item as any).unfold`，避免TypeScript类型检查错误

### 优化效果

1. 简化了组件结构，减少了组件嵌套层级
2. 减少了数据传递和组件通信的复杂性
3. 提高了代码的可维护性和理解性
4. 保持了原有UI样式和功能
5. 修复了TypeScript编译问题，确保项目能正常构建

## 第二阶段优化：菜单系统扩展

本次优化将菜单系统进行了扩展，支持多套菜单数据，以及在不同场景下的菜单切换功能。

### 优化内容

#### 1. 菜单数据区分

- 现有菜单数据：
  - `menuData.ts`：主菜单数据
  - `menuData.prod.ts`：生产环境菜单数据

- 新增菜单数据：
  - `k8sMenuData.ts`：Kubernetes专用菜单
  - `k8sMenuData.prod.ts`：Kubernetes生产环境菜单

#### 2. 菜单切换功能

- 实现了动态菜单数据源切换功能
- 提供了全局接口用于切换不同的菜单数据
- 默认使用menuData作为初始菜单数据

#### 3. Kubernetes集成

- 在kubernetes.component.ts中实现了菜单切换
- 进入Kubernetes模块时切换到k8sMenuData
- 退出Kubernetes模块时恢复默认菜单

### 实现细节

1. 菜单服务实现：
   - 创建了MenuService用于管理菜单数据和提供切换功能
   - 使用BehaviorSubject实现响应式数据流

2. 菜单组件集成：
   - 修改console-layout.component.ts，支持动态菜单数据源
   - 通过订阅菜单服务获取当前菜单数据

3. Kubernetes集成：
   - 在ngOnInit中切换到Kubernetes菜单
   - 在ngOnDestroy中恢复默认菜单

### 优化效果

1. 提供了更灵活的菜单系统，支持场景化菜单定制
2. 为不同的功能模块提供了专属菜单配置
3. 保持了一致的UI体验和交互模式
4. 简化了菜单管理逻辑，提高了可维护性

## 第三阶段优化：K8s详情页面模块化拆分

本次优化将K8s详情页面中的功能模块进行了拆分，使其更加模块化和可维护。

### 优化内容

#### 1. 节点池模块独立

- 从k8s-detail.component.ts中提取节点池相关代码
- 创建独立的节点池组件(node-pool.component.ts)
- 保留原有功能和UI交互体验

#### 2. 节点模块独立

- 从k8s-detail.component.ts中提取节点相关代码
- 创建独立的节点组件(node.component.ts)
- 实现了与secret-key-pair.component.ts相似的页面排版、样式和分页功能
- 保留原有功能和UI交互体验

#### 3. 命名空间模块独立

- 从k8s-detail.component.ts中提取命名空间相关代码
- 创建独立的命名空间组件(namespace.component.ts)
- 实现了与secret-key-pair.component.ts相似的页面排版、样式和分页功能
- 保留原有功能和UI交互体验

#### 4. 服务部署模块独立

- 从k8s-detail.component.ts中提取服务部署相关代码
- 创建独立的服务部署组件(service-deploy.component.ts)
- 实现了与secret-key-pair.component.ts相似的页面排版、样式和分页功能
- 保留原有功能和UI交互体验

#### 5. 路由配置更新

- 在container-routing.module.ts中添加节点池、节点、命名空间和服务部署路由
- 路由支持动态ID参数传递

#### 6. 模块注册

- 在container.module.ts中注册新的节点池、节点、命名空间和服务部署组件
- 确保组件可以正确导出和使用

#### 7. 菜单配置

- 确保k8sMenuData.ts中的节点池、节点、命名空间和服务部署路由链接正确配置
- 支持通过菜单直接访问节点池、节点、命名空间和服务部署页面

### 优化效果

1. 提高了代码的模块化程度，降低了组件间的耦合
2. 简化了各个功能模块的代码，提高了可维护性
3. 保持了一致的UI体验和交互模式
4. 为后续其他模块的拆分提供了参考模式
5. 改进了页面排版和样式，提供了更好的用户体验

## 第四阶段优化：K8s详情页面简化

本次优化将K8s详情页面简化，只保留基本信息部分，删除其他所有tab。

### 优化内容

#### 1. 简化页面结构

- 删除了节点池、节点、命名空间、服务部署等所有非基本信息的tab
- 只保留基本信息部分
- 简化了组件代码，去除了不必要的方法和变量

#### 2. 优化组件代码

- 简化了ngOnInit和selectMenu方法
- 去除了与其他tab相关的代码

### 优化效果

1. 提高了页面加载速度和性能
2. 简化了组件结构，使其更加清晰和易于维护
3. 为用户提供了更简洁的界面

## 项目依赖优化：清理未使用控件

本次优化将检查项目中的依赖项，删除未被实际使用的控件，以减小项目体积并提高维护性。

### 任务清单

#### 1. 依赖分析准备
[√] 分析package.json中的dependencies和devDependencies
[√] 创建完整的依赖项清单
[√] 确定检查方法和工具

#### 2. 核心Angular依赖检查
[√] 检查@angular/*核心包的使用情况
[√] 检查ng-zorro-antd组件库的使用情况
[√] 检查@angular/cdk的使用情况

#### 3. 第三方UI组件检查
[√] 检查ngx-bootstrap的使用情况
[√] 检查ngx-echarts的使用情况
[√] 检查angularx-qrcode的使用情况
[√] 检查ngx-filesaver的使用情况
[√] 检查ngx-pagination的使用情况

#### 4. 工具库依赖检查
[√] 检查aws-amplify和aws-amplify-angular的使用情况
[√] 检查bootstrap的使用情况
[√] 检查clipboard的使用情况
[√] 检查date-fns的使用情况
[√] 检查echarts的使用情况
[√] 检查file-saver的使用情况
[√] 检查highlight.js和marked的使用情况
[√] 检查jquery的使用情况
[√] 检查js-base64的使用情况
[√] 检查json2yaml的使用情况
[√] 检查react-bootstrap的使用情况
[√] 检查其他工具库的使用情况

#### 5. 开发依赖检查
[√] 检查测试相关依赖的使用情况
[√] 检查构建工具相关依赖的使用情况
[√] 检查代码质量工具相关依赖的使用情况

#### 6. 依赖清理
[√] 创建未使用依赖项清单
[√] 验证删除这些依赖项不会影响项目功能
[√] 更新package.json，移除未使用的依赖项

##### 未使用依赖项清单

1. **aws-amplify** 和 **aws-amplify-angular**：未在项目中找到导入和使用
2. **ngx-bootstrap**：未在项目中找到导入和使用，项目使用ng-zorro-antd作为UI组件库
3. **react-bootstrap**：未在项目中找到导入和使用
4. **@types/clipboard**、**@types/highlight.js**、**@types/marked**：这些类型定义包可以保留，但如果相应的库被移除，也应一并移除
5. **classlist.js**：现代浏览器已经支持classList API，不再需要此polyfill

## 移除扫码模块

本次优化移除了项目中的扫码模块及相关内容：

### 移除内容

1. **依赖项移除**
   - 从package.json中移除angularx-qrcode依赖
   - 从angular.json中移除qrcode依赖

2. **组件移除**
   - 移除src/app/components/console/user/expense-center/recharge目录下的所有文件
   - 包括recharge.component.ts、recharge.component.html、recharge.component.less、recharge.component.spec.ts

3. **路由配置更新**
   - 从user-routing.module.ts和user-routing.module.prod.ts中移除recharge相关的路由配置
   - 从user.module.ts中移除对RechargeComponent的导入和声明

4. **资源文件清理**
   - 移除src/assets/images/sweepCode-1.jpg图片文件

### 优化效果

1. **减小项目体积**
   - 减少了不必要的依赖和组件
   - 减少了构建输出的大小

2. **简化项目结构**
   - 移除了不再使用的功能模块
   - 使项目结构更加清晰

3. **提高代码质量**
   - 移除了冗余代码
   - 减少了潜在的维护问题

6. **web-animations-js**：现代浏览器已经支持Web Animations API，不再需要此polyfill
7. **proto-polyfill**：未在项目中找到使用，且现代浏览器已经支持大多数ES6特性
8. **tslint** 和 **tslint-plugin-prettier**：项目已经迁移到ESLint，不再需要TSLint

#### 7. 项目验证
[√] 安装更新后的依赖
[√] 启动项目验证功能正常
[√] 构建项目验证打包正常

## 依赖清理总结

本次优化对项目的依赖进行了全面检查和清理，移除了未使用的依赖项，提高了项目的维护性和构建性能。

### 清理内容

1. **移除未使用的依赖**
   - aws-amplify 和 aws-amplify-angular
   - ngx-bootstrap
   - react-bootstrap
   - classlist.js
   - web-animations-js

2. **移除过时的开发依赖**
   - proto-polyfill
   - tslint 和 tslint-plugin-prettier（已迁移到ESLint）

3. **移除未使用的JS文件**
   - src/assets/config/commonData.js：未在项目中被引用
   - src/assets/iconfont/iconfont.js：未在项目中被引用

4. **修复相关文件**
   - 修复了polyfills.ts中对web-animations-js的导入
   - 修复了event-emitter-fix.js中对events模块的引用
   - 移除了tsconfig.json中不支持的suppressOutputPathCheck选项

### 优化效果

1. **减小项目体积**
   - 减少了node_modules的大小（移除了89个包）
   - 减少了构建输出的大小

2. **提高构建性能**
   - 减少了依赖解析和加载时间
   - 减少了构建过程中的内存占用

3. **提高代码质量**
   - 移除了过时和冗余的依赖
   - 使项目结构更加清晰

4. **减少潜在问题**
   - 减少了依赖冲突的可能性
   - 减少了安全漏洞的风险

### 验证结果

1. **安装依赖**
   - 使用npm install成功安装了更新后的依赖

2. **启动项目**
   - 使用npm start成功启动了项目
   - 编译过程中没有错误

3. **构建项目**
   - 使用npm run build成功构建了项目
   - 构建输出大小为2.89 MB（压缩后为562.92 KB）

## EventEmitter 内存泄漏修复

本次优化解决了项目启动时出现的 Node.js EventEmitter 内存泄漏警告问题：

```
(node:19608) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 close listeners added to [Server]. Use emitter.setMaxListeners() to increase limit
```

### 修复内容

1. **代理配置优化**
   - 修改了 proxy.conf.js 文件，增加 EventEmitter 最大监听器数量从 20 到 30
   - 添加了 EventEmitter.defaultMaxListeners 全局设置
   - 为代理服务器实例添加了最大监听器数量设置

2. **自定义修复脚本**
   - 创建了 fix-event-emitter.js 脚本，专门用于修复 EventEmitter 内存泄漏警告

## VDI管理中心创建

本次新增了VDI管理中心功能，包含虚拟机池管理模块：

### 功能特性

1. **菜单结构**
   - 在运维管理下新增VDI管理中心一级菜单
   - 包含虚拟机池二级菜单（默认显示）

2. **虚拟机池列表页面**
   - 支持名称、镜像、网络、虚拟机数量、创建时间、架构名字段排序
   - 显示虚拟机配置、用户组等信息
   - 支持搜索和分页功能
   - 提供刷新和创建虚拟机池按钮

3. **虚拟机池操作功能**
   - 绑定用户组
   - 解绑用户组
   - 删除虚拟机池

4. **创建虚拟机池弹框**
   - 名称输入（必填）
   - 虚拟机配置选择（必选，包含4c16g和8c32g两种配置）
   - 镜像选择（必填，包含ubuntu和windows）
   - 专有网络和子网选择（必填）
   - 提交到/cloud/api/aic/order/vdiPool/save接口

### 技术实现

1. **组件结构**
   - VdiPoolComponent：虚拟机池主组件
   - VdiPoolService：虚拟机池服务类
   - VdiPoolModule：虚拟机池模块
   - VdiPoolRoutingModule：路由配置

2. **样式设计**
   - 参考app-system/index组件的页面排版和样式
   - 使用ng-zorro-antd组件库
   - 响应式设计，支持移动端适配

3. **路由配置**
   - 在system模块下添加vdi-pool路由
   - 支持懒加载模块

4. **API集成**
   - 创建VDI_POOL_URL常量配置
   - 实现列表查询、创建、删除等API调用
   - 集成专有网络API获取VPC数据

### 优化效果

1. **功能完整性**
   - 提供了完整的虚拟机池管理功能
   - 支持CRUD操作和用户组管理

2. **用户体验**
   - 界面简洁美观，操作直观
   - 支持搜索、排序、分页等常用功能

3. **代码质量**
   - 模块化设计，代码结构清晰
   - 遵循Angular最佳实践
   - 与现有系统风格保持一致

## VDI管理中心菜单修复

本次修复了VDI管理中心的菜单显示问题：

### 问题描述

当访问VDI管理中心（/console/system/vdi-pool）时，左侧菜单显示的是menuData.ts的内容，而不是operationMenuData.ts的内容。

### 问题原因

在MenuService的menuRoutePatterns配置中，缺少VDI Pool的路由模式匹配规则，导致系统无法识别/console/system/vdi-pool路径应该使用运维管理菜单。

### 修复内容

1. **路由模式更新**
   - 在MenuService的operation路由模式中添加`/\/console\/system\/vdi-pool/`
   - 确保VDI管理中心路径能正确匹配到运维管理菜单

2. **修复文件**
   - `src/app/service/common/menu/menu.service.ts`：添加VDI Pool路由模式

### 修复效果

1. **菜单显示正确**
   - 访问VDI管理中心时，左侧菜单正确显示operationMenuData.ts的内容
   - 菜单标题显示"运维管理"
   - 菜单项包含VDI管理中心及其子菜单

2. **路由切换正常**
   - 从其他模块切换到VDI管理中心时，菜单自动切换
   - 从VDI管理中心切换到其他模块时，菜单正确恢复

3. **用户体验提升**
   - 菜单显示逻辑一致，符合用户预期
   - 导航体验流畅，无异常跳转

## 云桌面功能开发

本次开发完成了云桌面功能的基础架构和新建桌面虚拟机功能：

### 功能内容

#### 1. 菜单配置
- 在menuData.ts和menuData.prod.ts中的网络菜单后面添加了云桌面一级菜单
- 配置了云桌面的二级菜单：桌面虚拟机
- 点击云桌面一级目录会展开菜单，默认选中"桌面虚拟机"二级菜单

#### 2. VDI模块创建
- 创建了src/app/modules/console/vdi/vdi.module.ts模块
- 创建了src/app/modules/console/vdi/vdi-routing.module.ts路由配置
- 在console-routing.module.ts和console-routing.module.prod.ts中添加了VDI模块路由

#### 3. 桌面虚拟机列表页面
- 创建了src/app/components/console/vdi/vdi.component.ts组件
- 实现了完整的列表字段：名称、镜像、用户名、IP地址、CPU、内存、数据盘、桌面状态、任务、电源、重启还原、GPU、共享、VIP、锁定、主机、组
- 实现了所有操作按钮：开机、关机、重启、远程桌面、开启VIP、设置共享、绑定用户、设置重启还原、快照、删除、锁定/解锁、重置状态
- 参考了instance.component.ts的页面排版、样式和分页功能
- 实现了搜索、刷新、分页等基础功能
- 添加了权限控制和状态管理

#### 4. 新建桌面虚拟机页面
- 创建了src/app/components/console/vdi/config/vdi-config.component.ts配置组件
- 参考了k8s-config.component.ts和vm-config.component.ts的格式和结构
- 实现了以下字段：
  - 名称：name，输入框，必填
  - 镜像：imageId，选择框，必填，镜像列表：[{id: 1000, name: "ubuntu"},{id:1001, name: "windows"}]
  - 配置：servicePlan，选择框，必填，包括多个cpu+memory组合：[{id: 1000, cpu: 4, memory: 16},{id: 1001,cpu:8, memory: 32}, {id: 1002, cpu: 16, memory: 32}]
  - 数据盘：disk，输入框，数字，选填
  - 专有网络：vpcId和networkId，严格参考vm-config.component.ts的写法
- 点击创建按钮提交到/cloud/api/aic/order/vdi/save接口
- 提交的JSON格式：{"name": "...", "imageId": ..., "servicePlanId": ..., "cpu": ..., "memory": ..., "disk": ..., "vpcId": ..., "networkId": ...}

### 技术实现

1. **组件架构**
   - 采用Angular响应式表单进行数据绑定和验证
   - 使用ng-zorro-antd组件库构建UI界面
   - 实现了完整的表单验证和错误提示

2. **路由配置**
   - 配置了懒加载模块，提高应用性能
   - 实现了页面间的导航和参数传递

3. **样式设计**
   - 采用响应式设计，支持移动端适配
   - 保持与系统其他模块的样式一致性
   - 实现了美观的配置摘要和服务计划选择界面

### 优化效果

1. **功能完整性**
   - 提供了完整的云桌面管理功能
   - 支持桌面虚拟机的创建、管理和操作

2. **用户体验**
   - 界面美观，操作直观
   - 提供了详细的表单验证和错误提示
   - 实现了配置摘要功能，方便用户确认配置信息

3. **代码质量**
   - 代码结构清晰，易于维护
   - 遵循Angular最佳实践
   - 实现了良好的错误处理和用户反馈

### VDI配置页面样式调整

根据用户要求，将vdi-config.component.ts的整体页面样式、DOM结构和class定义调整为与k8s-config.component.ts保持一致：

#### 调整内容

1. **DOM结构调整**
   - 将原有的config-content结构调整为panel-left和panel-right的双栏布局
   - 左侧面板包含表单内容，右侧面板包含配置概要和操作按钮
   - 采用与k8s-config相同的panel-header、panel-body、panel-aside结构

2. **CSS类名统一**
   - 使用与k8s-config相同的CSS类名：panel、panel-left、panel-right、panel-aside、pined等
   - 移除自定义的config-header、config-body、config-footer类
   - 简化样式文件，依赖共享样式

3. **表单布局调整**
   - 将配置选择从卡片式改为下拉选择框，与其他字段保持一致
   - 调整字段间距和布局，使用field-section和field-group结构
   - 统一表单验证错误提示的显示方式

4. **配置概要面板**
   - 在右侧面板添加配置概要表格，实时显示用户选择的配置信息
   - 包含名称、镜像、配置、数据盘、专有网络、子网等信息
   - 添加创建按钮到右侧面板底部

5. **功能优化**
   - 添加getSelectedNetworkName()方法获取选中的子网名称
   - 调整selectServicePlan()方法的参数处理逻辑
   - 保持所有原有功能不变，仅调整UI展示

#### 技术改进

1. **样式一致性**：确保与系统其他配置页面的视觉一致性
2. **用户体验**：右侧配置概要提供更好的信息预览
3. **代码维护性**：使用共享样式减少重复代码
   - 实现了对 http.Server 和 https.Server 实例的监听器数量设置
   - 添加了未捕获异常和未处理 Promise 拒绝的全局处理

3. **启动脚本优化**
   - 添加了 start:fixed 脚本，在启动前运行修复脚本
   - 添加了 start:express 脚本，使用自定义 Express 服务器提供服务
   - 为 Node.js 进程增加了内存限制 (--max-old-space-size=4096)

### 优化效果

1. **消除警告信息**
   - 成功消除了 EventEmitter 内存泄漏警告
   - 提高了服务器稳定性

2. **提供多种启动方式**
   - 提供了多种启动脚本，适应不同场景
   - 支持使用修复脚本或自定义服务器启动项目

3. **改进错误处理**
   - 添加了全局错误处理，提高了应用程序的健壮性
   - 优化了代理错误处理，提供更详细的错误信息

4. **性能优化**
   - 增加了 Node.js 内存限制，减少内存不足导致的崩溃
   - 优化了代理配置，提高了请求处理性能

### 使用方法

1. **使用修复脚本启动**
   ```
   npm run start:fixed
   ```

2. **使用自定义 Express 服务器启动**
   ```
   npm run start:express
   ```

3. **使用默认方式启动**
   ```
   npm run start
   ```
