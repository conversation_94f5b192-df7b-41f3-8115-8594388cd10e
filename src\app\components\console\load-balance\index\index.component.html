<div class="table-content">
    <ol class="on-breadcrumb">
        <li><span>负载均衡</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">负载均衡</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="keyword" nz-input
                            placeholder="请输入名称或绑定的弹性公网IP" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                        routerLink="../load-balance-config">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建负载均衡
                    </a>
                </div>
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <a nz-button nzType="primary"
                        [ngClass]="{'disabled': isArchiveUser === 'true'}"
                        [routerLink]="isArchiveUser === 'true' ? null : '../load-balance-config-quota'">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建负载均衡
                    </a>
                </div>
            </div>
            <nz-table #lbs style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="loadBalanceList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                                <!--
                              <i nz-icon *ngIf="col.title === '是否关联其他服务'"
                              class="question-icon"
                              nzTitle="若关联了其他服务，监听详情及删除功能将无法使用！"
                              nzPlacement="bottomCenter"
                              nz-tooltip
                              nzType="question-circle"
                              theme="outline"></i>
                              -->
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width">
                              {{ col.title }}
                            </th>
                        </ng-container>
                        <!-- <th width="20%">名称</th>
                        <th width="10%">状态</th>
                        <th width="10%">弹性IP地址</th>
                        <th width="15%">网络类型</th>
                        <th width="15%">
                            是否关联其他服务
                            <i nz-icon
                                class="question-icon"
                                nzTitle="若关联了其他服务，监听详情及删除功能将无法使用！"
                                nzPlacement="bottomCenter"
                                nz-tooltip
                                nzType="question-circle"
                                theme="outline"></i>
                        </th>
                        <th width="15%">计费方式</th>
                        <th width="15%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of lbs.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>
                            <span class="dot {{getStatusClass(item)}}">
                                {{ getStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.ipAddress }}</td>
                        <td>{{ item.public_ip }}</td>

                        <td style="min-width: 120px;">
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <a class="on-table-action-item"
                                    [ngClass]="{'disabled': !canCheckDetail(item)}"
                                    (click)="checkDetail(item)">
                                    <i nzTitle="监听详情"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        nz-icon
                                        nzType="cluster"
                                        nzTheme="outline"
                                        class="icon"></i>
                                </a>
                                <a class="on-table-action-item"
                                    [ngClass]="{'disabled': !canCheckChart(item)}"
                                    (click)="checkChart(item)">
                                    <i nzTitle="查看"
                                        nzPlacement="bottom"
                                        nz-tooltip nz-icon
                                        nzType="fund"
                                        nzTheme="outline"
                                        class="icon"></i>
                                </a>
                                <div class="on-table-action-item"
                                    [ngClass]="{'disabled': !canDeleteLB(item) || isArchiveUser === 'true'}"
                                    nz-popconfirm
                                    nzPlacement="top"
                                    [nzTitle]="isArchiveUser === 'true'? null : '确定要删除该负载均衡吗？'"
                                    [nzCondition]="!canDeleteLB(item)"
                                    (nzOnConfirm)="isArchiveUser === 'true'? null :deleteLBCheck(item);">
                                    <i nzTitle="删除"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                     [hidden]="!canBindIp(item)"
                                     [ngClass]="{'disabled': !canBindIp(item) || isArchiveUser === 'true'}"
                                     (click)="isArchiveUser === 'true' ? null : bindIp(item)">
                                    <i nzTooltipTitle="绑定公网IP"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-bangding"></i>
                                </div>
                                <div class="on-table-action-item"
                                     [hidden]="!canUnbindIp(item)"
                                     [ngClass]="{'disabled': busyStatus[item.id]}"
                                     nz-popconfirm
                                     nzPlacement="top"
                                     [nzTitle]="isArchiveUser === 'true'? null : '确定要解绑公网IP？'"
                                     [nzCondition]="!canUnbindIp(item)"
                                     (nzOnConfirm)="isArchiveUser === 'true'? null :unbindIp(item);">
                                    <i nzTooltipTitle="解绑公网IP"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-jiebang"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="lbs.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="createModalVisible" nzTitle="绑定弹性公网IP" nzOkText="绑定" [nzMaskClosable]="createLoading ? false : true"
          [nzOkLoading]="createLoading" (nzOnCancel)="createLoading ? false : handleCreateCancel()" [nzCancelDisabled]="createLoading" (nzOnOk)="handleCreateOk()">
    <ng-container *nzModalContent>
    <form [formGroup]="bind" class="config-content sm">
        <div class="field-group">
            <div class="field-item required">
                <label for="">
                    <span class="label-text">负载均衡名称</span>
                    <span class="lable-span">{{ vmObject.name }}</span>
                </label>
            </div>
            <div class="field-item required">
                <label for="">
                    <span class="label-text">公网IP </span>
                    <nz-select nzShowSearch formControlName="elasticIpId" nzPlaceHolder="请选择公网IP">
                        <nz-option *ngFor="let item of elasticIpList" [nzValue]="item.id" [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(bind.get('elasticIpId'))" class="form-hint error">
                    <div *ngIf="bind.get('elasticIpId').hasError('required')">
                        请选择要绑定的公网IP
                    </div>
                </div>
            </div>
            <!--<div class="field-item"
                 [ngClass]="{'required': protocolShowRequired}">
                <label for="">
                    <span class="label-text">云主机端口</span>
                    <nz-input-number [nzPrecision]=0 formControlName="vm_port" [nzDisabled]="!protocolShow" [(ngModel)]="vm_input"
                                     [nzMin]="0" [nzMax]="65535" [nzStep]="1"></nz-input-number>
                    <span class="small tip">端口号范围在0~65535之间</span>
                </label>
                <div *ngIf="isInvalid(bind.get('vm_port'))" class="form-hint error">
                    <div *ngIf="bind.get('vm_port').hasError('required')">
                        云主机端口不能为空
                    </div>
                    <div *ngIf="bind.get('vm_port').hasError('min')">
                        云主机端口范围在 0-65535
                    </div>
                    <div *ngIf="bind.get('vm_port').hasError('max')">
                        云主机端口范围在 0-65535
                    </div>
                </div>
            </div>-->
            <div class="field-item" [ngClass]="{'required': publicShow}">
                <label for="">
                    <span class="label-text">公网IP端口</span>
                    <nz-input-number [nzPrecision]=0 formControlName="public_port" [nzDisabled]="!publicShow" [(ngModel)]="public_input"
                                     [nzMin]="0" [nzMax]="65535" [nzStep]="1"></nz-input-number>
                    <span class="small tip">端口号范围在0~65535之间</span>
                </label>
                <div *ngIf="isInvalid(bind.get('public_port'))" class="form-hint error">
                    <div *ngIf="bind.get('public_port').hasError('required')">
                        公网IP端口不能为空
                    </div>
                    <div *ngIf="bind.get('public_port').hasError('min')">
                        公网IP端口范围在 0-65535
                    </div>
                    <div *ngIf="bind.get('public_port').hasError('max')">
                        公网IP端口范围在 0-65535
                    </div>
                </div>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>
