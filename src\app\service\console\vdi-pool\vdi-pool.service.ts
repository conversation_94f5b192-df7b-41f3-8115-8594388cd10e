import { Injectable } from '@angular/core';
import { RequestService } from '../../common/request/request.service';
import * as URL from '../../common/URL';

@Injectable({
    providedIn: 'root'
})
export class VdiPoolService {
    constructor(
        private req: RequestService
    ) {}

    /**
     * 获取虚拟机池列表
     * @param params 查询参数
     */
    getVdiPoolList(params: any) {
        return this.req.post(URL.VDI_POOL_URL + '/query', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 创建虚拟机池
     * @param params 创建参数
     */
    createVdiPool(params: any) {
        return this.req.post('/cloud/api/aic/order/vdiPool/save', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 删除虚拟机池
     * @param id 虚拟机池ID
     */
    deleteVdiPool(id: string | number) {
        return this.req.delete(URL.VDI_POOL_URL + '/delete/' + id)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 绑定用户组
     * @param params 绑定参数
     */
    bindUserGroup(params: any) {
        return this.req.post(URL.VDI_POOL_URL + '/bindUserGroup', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 解绑用户组
     * @param id 虚拟机池ID
     */
    unbindUserGroup(id: string | number) {
        return this.req.delete(URL.VDI_POOL_URL + '/unbindUserGroup/' + id)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 刷新虚拟机池数据
     */
    refreshVdiPool() {
        return this.req.get(URL.VDI_POOL_URL + '/refresh', null)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }
}
