<div class="map-chart-container">
  <div class="chart-title" *ngIf="title">{{ title }}</div>
  <div class="content-wrapper">
    <div class="chart-area">
      <div class="chart-container" [style.height.px]="chartHeight"></div>
    </div>
    <div class="details-list" *ngIf="mapPoints && mapPoints.length > 0">
      <ul>
        <li *ngFor="let point of mapPoints; let i = index"
            (mouseenter)="showMapTooltip(i)"
            (mouseleave)="hideMapTooltip()">
          <strong>{{ point.name }}</strong>
          <div class="details-content" *ngIf="point.details">
             <ng-container *ngIf="isObject(point.details) else primitiveDetails">
                <div *ngFor="let detailItem of point.details | keyvalue">
                  <span class="detail-key">{{ detailItem.key }}:</span>
                  <span class="detail-value">{{ detailItem.value }}</span>
                </div>
             </ng-container>
             <ng-template #primitiveDetails>{{ point.details }}</ng-template>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div> 