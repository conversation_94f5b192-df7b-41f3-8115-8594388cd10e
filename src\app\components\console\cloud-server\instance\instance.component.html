<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword" style="border-radius: 4px 0 0 4px;"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入实例名称或IP地址" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" style="border-radius: 0 4px 4px 0;"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div *ngIf="permission('export')">
                    <a nz-button [ngClass]="{'disabled': isArchiveUser === 'true'}" class="default"
                       (click)="downloadVM()">
                        导&nbsp;出
                    </a>
                </div>
                <div *ngIf="permission('refresh')">
                    <a nz-button [ngClass]="{'disabled': isArchiveUser === 'true'}" class="default"
                       (click)="refresh()">
                        刷&nbsp;新
                    </a>
                </div>
                <div *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary"
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="resLimit ? null : '../instance-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建云服务器
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">实例</span>
            </div>
            <nz-table #instances style="overflow:hidden;overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="instanceList" [nzScroll]="{ x: '1450px' }">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              [nzWidth]="col.width"
                              [nzMaxWidth]="400"
                              [nzMinWidth]="60"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                              [nzRight]="col.nzRight"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style=" min-width:275px">
                              {{ col.title }}
                            </th>
                        </ng-container>

                        <!-- <th width="25%" nzColumnKey="name" [nzSortFn]="true">名称</th>
                        <th nzColumnKey="status" [nzSortFn]="true">状态</th>
                        <th nzColumnKey="ipAddress" [nzSortFn]="true">IP地址</th>
                        <th>网络类型</th>
                        <th nzColumnKey="cpuNumAndMemoryGB" [nzSortFn]="true">配置</th>
                        <th width="25%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of instances.data; trackBy: trackById">
                        <td>
                            {{item.name}}
                            <div class="deploy-progress"
                                *ngIf="item.deployStatus === 'INIT'">
                                <nz-progress
                                    nzStatus="active"
                                    nzSize="small"
                                    [nzPercent]="item.progres || 0">
                                </nz-progress>
                            </div>
                        </td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-green': item.ecStatus === 'ACTIVE', 'dot-gray': !item.ecStatus === 'ACTIVE'}">
                                {{ getPowerStatusText(item) }}
                                <app-refresh-btn style="margin-left: 10px" (refresh)="refreshPowerStatus(item);"></app-refresh-btn>
                            </span>
                        </td>
                        <td style="white-space: nowrap">
                            {{ item.ipAddress || '-'}}
                            <div *ngFor="let date of item.protocol; let i = index">
                                 <span *ngIf="item.ipAddress && item.publicIpAddress[i] && item.bindingType[i] === 'OUT'">
                                    {{ '('+ item.protocol[i] +')'+ (item.port[i] || '') }}
                                     <i nz-icon nzType="arrow-right" nzTheme="outline" class="port"  *ngIf="item.bindingType[i] === 'OUT'"></i>
                                     {{ item.publicIpAddress[i] + ':any'}}
                                </span>
                                <span *ngIf="item.ipAddress && item.publicIpAddress[i] && item.bindingType[i] === 'IN'">
                                    {{ '('+ item.protocol[i] +')'+ (item.port[i] || '') }}
                                    <i nz-icon nzType="arrow-left" nzTheme="outline" class="port"  *ngIf="item.bindingType[i] === 'IN'"></i>
                                    {{ item.publicIpAddress[i] +':'+ (item.publicPort[i] || 'any')}}
                                </span>
                            </div>
                        </td>
                        <td>专有网络</td>
                        <td>
                            {{item.cpuNum}}核&nbsp;{{item.memoryGB}}G内存
                        </td>
                        <td class="owner-div" nzTooltipTitle="{{item.vmOwnerBindingString}}" nzTooltipPlacement="left" nz-tooltip>
                            {{item.vmOwnerBindingString ? item.vmOwnerBindingString : '/'}}
                        </td>
<!--                        <td>-->
<!--                            {{item.cpuNum}}核&nbsp;{{item.memoryGB}}G内存-->
<!--                        </td>-->
                         <!-- 20200520 删除 -->
                        <!-- <td>后付费</td> -->
                        <td nzRight>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item" *ngIf="permission('monitor')"
                                     (click)="isArchiveUser === 'true' ? null : viewMonitor(item);"
                                     [ngClass]="{'disabled' : !canViewMonitor(item) || isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="监控"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="fund" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="isAdmin"
                                     (click)="isArchiveUser === 'true' ? null : bindUser(item);">
                                    <i nzTooltipTitle="用户"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="user" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('vnc')"
                                    (click)="isArchiveUser === 'true' ? null : accessVM(item);"
                                    [ngClass]="{'disabled': !canAccessVM(item) || isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="访问"
                                    nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-desktop"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('binding')"
                                    [ngClass]="{'disabled': !canBindIp(item) || isArchiveUser === 'true'}"
                                    (click)="isArchiveUser === 'true' ? null : bindIp(item)">
                                    <i nzTooltipTitle="绑定公网IP"
                                    nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-bangding"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要关闭该云服务器吗？"
                                    (nzOnConfirm)="powerOffVM(item);"
                                    [nzCondition]="!canPowerOffVM(item)"
                                    [hidden]="!(canPowerOffVM(item) || item.deployStatus === 'INIT')"
                                    [ngClass]="{'disabled': !canPowerOffVM(item)}">
                                    <i nzTooltipTitle="关机"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-guanji">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    (click)="powerOnVM(item);"
                                    [hidden]="!canPowerOnVM(item)"
                                    [ngClass]="{'disabled': !canPowerOnVM(item)}">
                                    <i nzTooltipTitle="开机"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-qidong"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要重启该云服务器吗？"
                                    [nzCondition]="!canRebootVM(item)"
                                    (nzOnConfirm)="rebootVM(item);"
                                    [ngClass]="{'disabled': !canRebootVM(item)}">
                                    <i nzTooltipTitle="重启"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-haikezhangguizhushou_zhongqi">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                     nz-popconfirm
                                     nzTooltipContent="top"
                                     [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要回收该云服务器吗？'"
                                     (nzOnConfirm)="isArchiveUser === 'true' ? null : recycleVM(item);"
                                     [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="回收"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-trash-o">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该云服务器吗？'"
                                    [hidden]="!canDeleteVM(item)"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteVM(item);"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon-close1 iconfont icon">
                                    </i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('update')"
                                (click)="isArchiveUser === 'true'?null :showChangeService(item)"
                                    [hidden]="!canChange(item)"
                                    [ngClass]="{'disabled': !canChange(item) || isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="变更"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont iconfontBianji3 icon-bianji3"></i>
                                </div>
<!--                                <div class="on-table-action-item"-->
<!--                                (click)="isArchiveUser === 'true'?null :showCreateMirror(item)"-->
<!--                                    [ngClass]="{'disabled': !canCreateMirror(item) || isArchiveUser === 'true'}">-->
<!--                                    <i nzTooltipTitle="新建镜像"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        class="icon iconfont iconfontjingxiang icon-jingxiang"></i>-->
<!--                                </div>-->
                                <!-- <div class="on-table-action-item"
                                    (click)="isArchiveUser === 'true' ? null : showBackupStrategy(item);"
                                    [ngClass]="{'disabled': !canShowBackupStrategy(item) || isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="备份策略"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-tasks"></i>
                                </div> -->
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="instances.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
<nz-modal [(nzVisible)]="changeModalVisible" nzTitle="云服务器变更" (nzOnCancel)="handleCancelChange()"
(nzOnOk)="changeService()" [nzOkLoading]="isChanging" [nzCancelLoading]="isChanging" [nzWidth]="620">
    <ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="serviceItem" (submit)="changeService()">
    <section class="field-section">
        <div class="field-group">
            <div class="field-item topSty">
                <label>
                    <span class="label-text">云服务器名称</span>
                    <input class="inputWidth_Name" type="text" formControlName="serviceName" disabled value="{{ serviceItem.value.instanceBefore.name }}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label class="label01">
                    <span class="label-text">CPU</span>
                    <input required type="text" formControlName="cpuBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.cpuNum + '核'}}"/>
                </label>

                <label class="label02">
                    <span class="label-text">CPU变更为</span>
                    <input required type="text" formControlName="cpuAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.cpuUnit + '核'}}"/>
                </label>
            </div>
        </div>
        <div class="field-group">
            <div class="field-item bandwidthDiv">
                <label class="label01">
                    <span class="label-text">内存</span>
                    <input required type="text" formControlName="memoryBefore" class="inputWidth" disabled value="{{ serviceItem.value.instanceBefore.memoryGB + 'G'}}"/>
                </label>

                <label class="label02">
                    <span class="label-text">内存变更为</span>
                    <input required type="text" formControlName="memoryAfter" class="inputWidth" disabled value="{{ serviceItem.value.instanceAfter.memoryUnit + 'G'}}"/>
                </label>
            </div>
        </div>
    </section>
</form>
    </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="createMirrorModalVisible" nzTitle="新建镜像" (nzOnCancel)="handleCancelCreateMirror()"
(nzOnOk)="createMirror()" [nzCancelLoading]="isLoading" [nzWidth]="420">
    <ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="mirrorItem" (submit)="createMirror()">
    <section class="field-section">
        <div class="field-group">
            <div class="field-item topSty bottomSty required">
                <label>
                    <span class="label-text">镜像名称：</span>
                    <input  required type="text" formControlName="mirrorName" value="{{ serviceItem.value.mirrorName}}"/>
                </label>
            </div>
            <div class="form-hint error"
            *ngIf="isInvalid(mirrorItem.get('mirrorName'))">
            <div
                *ngIf="mirrorItem.get('mirrorName').hasError('required')">
                镜像名称不能为空
            </div>
        </div>
        </div>
    </section>
</form>
    </ng-container>
</nz-modal>

<app-backup-strategy [readonly]="true"
    [isVisible]="backupModalVisible"
    [strategyId]="currentStrategyId"
    [cloudServer]="currentCloudServer"
    (close)="backupModalVisible = false"
    (submit)="getInstanceList()">
</app-backup-strategy>

<app-bind-ip [isVisible]="bindModalVisible"
             [vmObject]="vmObject" [ipBindingList]="ipBindingList" (submit)="reload()"
             (close)="bindModalVisible = false">
</app-bind-ip>

<app-bind-user [isVisible]="userbindModalVisible" (refreshParent)="getInstanceList()"
             [vmObject]="vmObject" [userBindingList]="userBindingList" (submit)="reload()"
             (close)="userbindModalVisible = false">
</app-bind-user>