<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword" autocomplete="off" [(ngModel)]="keyword" nz-input placeholder="请输入节点池名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch><i nz-icon nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <button nz-button nzType="primary" (click)="!resLimit && showAddNodeWindow()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新增节点池
                    </button>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">节点池</span>
            </div>
            <nz-table #nodeList nzSize="small"
                      style="overflow-x: auto"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="nodePageChange($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="nodePoolListData">
                <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)">
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width">
                            {{ col.title }}
                        </th>
                    </ng-container>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of nodeList.data; trackBy: trackById">
                    <td><span style="color:#1890ff;">{{ item.name || '-' }}</span><br>{{item.uid }}</td>
                    <td>{{item.flavor}}</td>
                    <td>{{ item.initialNodeCount }}</td>
                    <td>{{ item.rootVolumeSize }}</td>
                    <td>{{ item.dataVolumeSize }}</td>
                    <td>{{item.subnetName}}</td>
                    <td>
                        <span style="color:{{!item.autoscalingEnable? '#bdb5b5':'#44cc11'}};">{{item.autoscalingEnable ? '开启' : '关闭'}}</span><br>
                        最小/最大数：{{item.autoscalingMinNodeCount}}/{{item.autoscalingMaxNodeCount}}
                    </td>
                    <td>{{item.creationTimestamp}}</td>
                    <td>
                        <div class="on-table-actions" *ngIf="permission('create')"
                             [hidden]="busyStatus[item.id]">
                            <div class="on-table-action-item"
                                 (click)="scaleNode(item);">
                                <i nzTooltipTitle="节点扩缩容"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon-vpn iconfont icon"></i>
                            </div>
                            <div class="on-table-action-item" *ngIf="permission('delete')"
                                 nz-popconfirm
                                 nzTooltipContent="top"
                                 [nzCondition]="false"
                                 [nzPopconfirmTitle]="'确定要删除该节点池吗？'"
                                 (nzOnConfirm)="deleteNodePool(item);">
                                <i nzTooltipTitle="删除"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon fa fa-trash-o"></i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                             [hidden]="!busyStatus[item.id]">
                            <div class="action-loading-placeholder">
                                <i class="icon" nz-icon [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="nodeList.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 节点池编辑弹窗 -->
<app-k8s-node-pool-edit
    [isVisible]="editNodePoolWindow"
    [bean]="bean"
    (close)="editNodePoolWindow = false"
    (submit)="editNodePoolWindow = false"
    (refreshParent)="refreshList()">
</app-k8s-node-pool-edit>

<!-- 节点扩缩容弹窗 -->
<nz-modal
    [(nzVisible)]="showCreateNodeWindow"
    nzTitle="节点扩缩容"
    (nzOnCancel)="showCreateNodeWindow = false"
    (nzOnOk)="submitScaleNode()"
    [nzOkText]="'确认'"
    [nzCancelText]="'取消'">
    <ng-container *nzModalContent>
        <div class="field-item">
            <label>
                <span class="add-node-tips">节点数量</span>
                <nz-input-number [(ngModel)]="initialNodeCount" [nzMin]="0" [nzMax]="10" [nzStep]="1"></nz-input-number>
            </label>
        </div>
    </ng-container>
</nz-modal>
