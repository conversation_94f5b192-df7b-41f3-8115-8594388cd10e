import { Component, Input, OnInit, OnChanges, SimpleChanges, ChangeDetectorRef, ElementRef } from '@angular/core';

@Component({
  selector: 'app-horizontal-column-chart-template',
  templateUrl: './horizontal-column-chart-template.component.html',
  styleUrls: ['./horizontal-column-chart-template.component.less']
})
export class HorizontalColumnChartTemplateComponent implements OnInit, OnChanges {

  @Input() title: string;
  @Input() chartHeight: number;
  @Input() chartData: { key: string, value: number, color?: string }[];

  colorScheme = {
    domain: ['#155EEF', '#00A36C', '#F9812A', '#A855F7', '#EF4444']
  };

  formattedData: any[];
  maxValue: number;
  tooltipData: { key: string, value: number, top: number, left: number } | null;

  constructor(private cdr: ChangeDetectorRef, private elRef: ElementRef) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.chartData) {
      this.formatChartData();
    }
  }

  formatChartData() {
    this.maxValue = Math.max(...this.chartData.map(item => item.value));
    this.formattedData = this.chartData.map((item, index) => ({
      key: item.key,
      value: item.value,
      color: item.color || this.colorScheme.domain[index % this.colorScheme.domain.length],
      width: (item.value / this.maxValue) * 100 + '%'
    }));
  }

  showTooltip(data: any, event: MouseEvent) {
    const tooltipOffset = 10; // Tooltip offset from cursor
    const containerRect = this.elRef.nativeElement.getBoundingClientRect(); // Get container bounds

    // Calculate position relative to the container
    const top = event.clientY - containerRect.top + tooltipOffset;
    const left = event.clientX - containerRect.left + tooltipOffset;

    this.tooltipData = {
      key: data.key,
      value: data.value,
      top: top, // Use relative top
      left: left // Use relative left
    };
    // console.log('Tooltip Data (Relative):', this.tooltipData); // Log relative data
    this.cdr.markForCheck();
  }

  hideTooltip() {
    // Restore hiding logic
    this.tooltipData = null;
    this.cdr.markForCheck(); // Make sure to restore this if it was used
  }

  valueFormat(value: number) {
    return value.toLocaleString();
  }
}
