<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">云服务器</a></li>-->
<!--        <li><span>实例</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入实例名称或IP地址" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">回收站</span>
            </div>
            <nz-table #instances
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="instanceList">
                <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th
                                *ngIf="col.width"
                                nz-resizable
                                nzBounds="window"
                                nzPreview
                                [nzWidth]="col.width"
                                [nzMaxWidth]="400"
                                [nzMinWidth]="60"
                                [nzShowSort]="col.showSort"
                                [nzSortFn]="col.sortFlag"
                                [nzSortOrder]="col.allowSort"
                                [nzColumnKey]="col.ColumnKey"
                                (nzResizeEnd)="onResize($event, col.title)"
                        >
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width" style=" min-width:275px">
                            {{ col.title }}
                        </th>
                    </ng-container>

                    <!-- <th width="25%" nzColumnKey="name" [nzSortFn]="true">名称</th>
                    <th nzColumnKey="status" [nzSortFn]="true">状态</th>
                    <th nzColumnKey="ipAddress" [nzSortFn]="true">IP地址</th>
                    <th>网络类型</th>
                    <th nzColumnKey="cpuNumAndMemoryGB" [nzSortFn]="true">配置</th>
                    <th width="25%">操作</th> -->
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of instances.data; trackBy: trackById">
                    <td>
                        {{item.name}}
                        <div class="deploy-progress"
                             *ngIf="item.deployStatus === 'INIT'">
                            <nz-progress
                                    nzStatus="active"
                                    nzSize="small"
                                    [nzPercent]="item.progres || 0">
                            </nz-progress>
                        </div>
                    </td>
                    <td>
                        {{item.recycleStatusDisplay}}
                    </td>
                    <td style="white-space: nowrap">
                        {{ item.ipAddress || '-'}}
                        <div *ngFor="let date of item.protocol; let i = index">
                                 <span *ngIf="item.ipAddress && item.publicIpAddress[i] && item.bindingType[i] === 'OUT'">
                                    {{ '('+ item.protocol[i] +')'+ (item.port[i] || '') }}
                                     <i nz-icon nzType="arrow-right" nzTheme="outline" class="port"  *ngIf="item.bindingType[i] === 'OUT'"></i>
                                     {{ item.publicIpAddress[i] + ':any'}}
                                </span>
                            <span *ngIf="item.ipAddress && item.publicIpAddress[i] && item.bindingType[i] === 'IN'">
                                    {{ '('+ item.protocol[i] +')'+ (item.port[i] || '') }}
                                <i nz-icon nzType="arrow-left" nzTheme="outline" class="port"  *ngIf="item.bindingType[i] === 'IN'"></i>
                                {{ item.publicIpAddress[i] +':'+ (item.publicPort[i] || 'any')}}
                                </span>
                        </div>
                    </td>
                    <td>专有网络</td>
                    <td>
                        {{item.cpuNum}}核&nbsp;{{item.memoryGB}}G内存
                    </td>
                    <!-- 20200520 删除 -->
                    <!-- <td>后付费</td> -->
                    <td>

                        <div class="on-table-actions"
                             [hidden]="busyStatus[item.id]">
                            <div class="on-table-action-item" *ngIf="permission('restore')"
                                 nz-popconfirm
                                 nzTooltipContent="top"
                                 [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要恢复该云服务器吗？'"
                                 (nzOnConfirm)="isArchiveUser === 'true' ? null : recoverVM(item);"
                                 [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                <i nzTooltipTitle="恢复"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon-beifenhuifu iconfont icon"></i>
                            </div>
                            <div class="on-table-action-item" *ngIf="permission('delete')"
                                 nz-popconfirm
                                 nzTooltipContent="top"
                                 [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该云服务器吗？'"
                                 (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteVM(item);"
                                 [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                <i nzTooltipTitle="删除"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon-close1 iconfont icon">
                                </i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                             [hidden]="!busyStatus[item.id]">
                            <div
                                    class="action-loading-placeholder">
                                <i class="icon" nz-icon
                                   [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="instances.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

