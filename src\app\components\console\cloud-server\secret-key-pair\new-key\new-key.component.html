<nz-modal
	[(nzVisible)]="isVisible"
	nzOkText="创建"
	nzTitle="创建密钥对"
	[nzOkLoading]="isCreating"
	(nzOnCancel)="handleCancel()"
	[nzWidth]="400"
	(nzOnOk)="createKeyPair()">
    <ng-container *nzModalContent>
	<form [formGroup]="keyPair" class="config-content sm">
<!--		<section class="field-section">-->
			<div class="field-group">
				<div class="field-item required">
					<label>
						<span class="label-text">密钥对</span>
                        <input type="text" formControlName="name" placeholder="请输入密钥对名称">
                        <div class="small tip label-padding">
                                密钥对名称必须以字母开头，只能输入英文、数字、中划线
                        </div>
					</label>
					<div *ngIf="isDirty(keyPair.get('name'))" class="form-hint error">
						<div *ngIf="keyPair.get('name').hasError('required')">
							密钥对名称不能为空
                        </div>
                        <div *ngIf="keyPair.get('name').hasError('pattern')">
							密钥对名称不符合规范
						</div>
						<div *ngIf="keyPair.get('name').hasError('maxlength')">
							密钥对名称长度不能超过{{ keyPair.get('name').errors.maxlength.requiredLength }}个字符
						</div>
					</div>
				</div>
			</div>
<!--		</section>-->
	</form>
    </ng-container>
</nz-modal>
	