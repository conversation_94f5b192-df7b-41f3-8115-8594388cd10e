export enum ServicePlanType {
    ECS = 'ecs',
    EVS = 'evs',
    K8S = 'k8s',
    RDS = 'rds',
    REDIS = 'redis',
    MSG = 'msg'
}

export const ServicePlanTypeMap = {
    [ServicePlanType.ECS]: '云服务器',
    [ServicePlanType.EVS]: '存储',
    [ServicePlanType.K8S]: 'K8S',
    [ServicePlanType.RDS]: 'RDS',
    [ServicePlanType.REDIS]: 'REDIS',
    [ServicePlanType.MSG]: 'msg'
};

export enum ServicePlanItemType {
    CPU = 'CPU',
    DISK_GB = 'DISK_GB',
    MEMORY_GB = 'MEMORY_GB',
    BANDWIDTH_MB = 'BANDWIDTH_MB'
}

export const ServicePlanItemTypeMap = {
    [ServicePlanItemType.CPU]: 'CPU',
    [ServicePlanItemType.DISK_GB]: '磁盘(GB)',
    [ServicePlanItemType.MEMORY_GB]: '内存(GB)',
    [ServicePlanItemType.BANDWIDTH_MB]: '带宽(MB)'
};

export enum ServicePlanItemSubType {
    HDD = 'HDD',
    SSD = 'SSD'
}

export const ServicePlanItemSubTypeMap = {
    [ServicePlanItemSubType.HDD]: 'HDD',
    [ServicePlanItemSubType.SSD]: 'SSD'
};
