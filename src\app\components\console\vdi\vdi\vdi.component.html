<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword" style="border-radius: 4px 0 0 4px;"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入桌面虚拟机名称或IP地址" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" style="border-radius: 0 4px 4px 0;"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div *ngIf="permission('refresh')">
                    <a nz-button [ngClass]="{'disabled': isArchiveUser === 'true'}" class="default"
                       (click)="refresh()">
                        刷&nbsp;新
                    </a>
                </div>
                <div *ngIf="poolId">
                    <a nz-button class="default" (click)="backToPoolList()">
                        ← 返回虚拟机池列表
                    </a>
                </div>
                <div *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary" routerLink="../config">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建桌面虚拟机
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    桌面虚拟机
                    <span *ngIf="poolName" style="color: #666; font-size: 14px; margin-left: 10px;">
                        （虚拟机池：{{ poolName }}）
                    </span>
                </span>
            </div>
            <nz-table #vdiTable style="overflow:hidden;overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="vdiList" [nzScroll]="{ x: '2000px' }">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              [nzWidth]="col.width"
                              [nzMaxWidth]="400"
                              [nzMinWidth]="60"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                              [nzRight]="col.nzRight"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width:275px">
                              {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of vdiTable.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>{{ item.imageName || '-' }}</td>
                        <td>{{ item.account || '-' }}</td>
                        <td>{{ item.ipAddress || '-' }}</td>
                        <td>{{ item.cpu || '-' }} {{ item.memory || '-' }}</td>
                        <td>{{ item.disk || '-' }}</td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-green': item.vdiStatus === 'ACTIVE', 'dot-gray': item.vdiStatus !== 'ACTIVE'}">
                                {{ getVdiStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.vdiTask || '-' }}</td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-green': item.powerStatus === 'ACTIVE', 'dot-gray': item.powerStatus !== 'ACTIVE'}">
                                {{ getPowerStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.resetOnReboot === true ? '是' : '否' }}</td>
                        <td>{{ item.gpuSupport === true ? '是' : '否' }}</td>
                        <td>{{ item.share === true ? '是' : '否' }}</td>
                        <td>{{ item.vip === true ? '是' : '否' }}</td>
                        <td>{{ item.locked === true ? '是' : '否' }}</td>
                        <td>{{ item.host || '-' }}</td>
                        <td>{{ item.group || '-' }}</td>
                        <td nzRight>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <!-- 前3个按钮：开机、关机、重启 -->
                                <!-- 开机 -->
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    (click)="powerOn(item);"
                                    [hidden]="!canPowerOn(item)"
                                    [ngClass]="{'disabled': !canPowerOn(item)}">
                                    <i nzTooltipTitle="开机"
                                        nzTooltipPlacement="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-qidong"></i>
                                </div>
                                <!-- 关机 -->
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipPlacement="top"
                                    nzPopconfirmTitle="确定要关闭该桌面虚拟机吗？"
                                    (nzOnConfirm)="powerOff(item);"
                                    [nzCondition]="!canPowerOff(item)"
                                    [hidden]="!canPowerOff(item)"
                                    [ngClass]="{'disabled': !canPowerOff(item)}">
                                    <i nzTooltipTitle="关机"
                                        nzTooltipPlacement="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-guanji">
                                    </i>
                                </div>
                                <!-- 重启 -->
                                <div class="on-table-action-item" *ngIf="permission('power')"
                                    nz-popconfirm
                                    nzTooltipPlacement="top"
                                    nzPopconfirmTitle="确定要重启该桌面虚拟机吗？"
                                    [nzCondition]="!canReboot(item)"
                                    (nzOnConfirm)="reboot(item);"
                                    [ngClass]="{'disabled': !canReboot(item)}">
                                    <i nzTooltipTitle="重启"
                                        nzTooltipPlacement="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-haikezhangguizhushou_zhongqi">
                                    </i>
                                </div>

                                <!-- 更多操作按钮 -->
                                <div class="on-table-action-item more-actions-trigger"
                                     nz-dropdown
                                     [nzDropdownMenu]="moreActionsMenu"
                                     nzPlacement="bottomRight"
                                     [nzTrigger]="'click'"
                                     [nzClickHide]="true">
                                    <i nzTooltipTitle="更多操作"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon
                                       nzType="plus"
                                       nzTheme="outline">
                                    </i>
                                </div>

                                <!-- 更多操作下拉菜单 -->
                                <nz-dropdown-menu #moreActionsMenu="nzDropdownMenu">
                                    <ul nz-menu class="more-actions-menu">
                                        <!-- 远程桌面 -->
                                        <li nz-menu-item *ngIf="permission('vnc')"
                                            (click)="remoteDesktop(item);"
                                            [class.disabled]="!canRemoteDesktop(item)">
                                            <span>远程桌面</span>
                                        </li>
                                        <!-- 开启VIP -->
                                        <li nz-menu-item *ngIf="permission('vip')"
                                            (click)="enableVip(item);">
                                            <span>开启VIP</span>
                                        </li>
                                        <!-- 设置共享 -->
                                        <li nz-menu-item *ngIf="permission('share')"
                                            (click)="setShare(item);">
                                            <span>设置共享</span>
                                        </li>
                                        <!-- 绑定用户 -->
                                        <li nz-menu-item *ngIf="permission('binding')"
                                            (click)="bindUser(item);">
                                            <span>绑定用户</span>
                                        </li>
                                        <!-- 设置重启还原 -->
                                        <li nz-menu-item *ngIf="permission('reset')"
                                            (click)="setResetOnReboot(item);">
                                            <span>设置重启还原</span>
                                        </li>
                                        <!-- 快照 -->
                                        <li nz-menu-item *ngIf="permission('snapshot')"
                                            (click)="snapshot(item);">
                                            <span>快照</span>
                                        </li>
                                        <!-- 删除 -->
                                        <li nz-menu-item *ngIf="permission('delete')"
                                            nz-popconfirm
                                            nzPopconfirmTitle="确定要删除该桌面虚拟机吗？"
                                            (nzOnConfirm)="delete(item);"
                                            [class.disabled]="!canDelete(item)">
                                            <span>删除</span>
                                        </li>
                                        <!-- 锁定/解锁 -->
                                        <li nz-menu-item *ngIf="permission('lock')"
                                            (click)="item.locked ? unlock(item) : lock(item);">
                                            <span>{{ item.locked ? '解锁' : '锁定' }}</span>
                                        </li>
                                        <!-- 重置状态 -->
                                        <li nz-menu-item *ngIf="permission('resetStatus')"
                                            (click)="resetStatus(item);">
                                            <span>重置状态</span>
                                        </li>
                                    </ul>
                                </nz-dropdown-menu>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="vdiTable.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>