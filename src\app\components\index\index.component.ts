import {Component, OnInit, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import {ObservableService} from '../../service/common/observable/observable.service';
import { Router } from '@angular/router';
// import { IMG_PUBLIC_PATH } from '@/utils/constants';
import * as URL from './../../service/common/URL';
import {ConfigService} from "../../service/config/config.service";
import { MessageService } from 'src/app/service/console/utils/message.service';
import {Base64} from 'js-base64';
import { PermissionService } from 'src/app/service/console/system/permission.service';
import {AuthenticationService} from "../../service/common/Authentication/authentication.service";

@Component({
    selector: 'app-index',
    templateUrl: './index.component.html',
    styleUrls: ['./index.component.less']
})
export class IndexComponent implements OnInit, AfterViewInit, OnDestroy {

    constructor(
        private config: ConfigService,
        private observable: ObservableService,
        private router: Router,
        private msg: MessageService,
        private permissionService: PermissionService,
        private authService: AuthenticationService,
    ) {
    }

    fnText = ['华为云','移动云','阿里云','vCenter','OpenStack','K8S'];
    fnText2 = ['应用','中间件','数据库','K8S','操作系统','虚拟机',];
    fnText3 = ['用户管理','云账号管理','数据管理','邮件管理','项目管理','部门管理','许可管理','日志管理'];

    valueText = ['智能混合云管理','智能云知识库','增强业务稳定性','推动数字化转型','高效的云资源管理','提升运维效率','优化资源配置','统一多云管理'];

    images= [{title:'', src: 'hengshi.png'}, {title:'', src: 'hexinchuangtian.png'}, {title:'', src: 'qinming.png'}, {title:'', src: 'thinkstation.png'},]

    isLogin = false;
    username = '';
    // ai_url = '';
    logo = '';
    title = '';
    ngOnInit() {
        this.config.initTitle().then(res => {
            if(res.success){
                this.title = res.data;
                window.localStorage.setItem('title', res.data);
            }
        });
        this.config.initLogo().then(res => {
            if(res.success){
                this.logo = res.data;
                window.localStorage.setItem('logo', res.data);
            }
        })
        this.isLogin = (new Date().getTime() / 1000 - parseFloat(window.localStorage.getItem('tokenTime') || '0')) < 0;
        this.username = window.localStorage.getItem('username') || '';
        // this.ai_url = window.localStorage.getItem('ai_url');
    }

    @ViewChild('slider') sliderRef!: ElementRef<HTMLElement>;

    private scrollSpeed = 1;
    private animationFrameId: number | null = null;
    private imagesLoadedCount = 0;
    private requiredLoads = this.images.length * 2;
    private isScrolling = false; // 新增滚动状态标识

    ngAfterViewInit() {
        // 检查sliderRef是否存在，如果不存在则不执行相关操作
        if (this.sliderRef && this.sliderRef.nativeElement) {
            this.cloneImagesForSeamlessScroll();
            this.setupImageLoadCheck();
        }
    }

    ngOnDestroy() {
        this.stopScroll();
        // 清理所有图片的load监听
        if (this.sliderRef && this.sliderRef.nativeElement) {
            this.sliderRef.nativeElement.querySelectorAll('img').forEach(img => {
                img.removeEventListener('load', this.onImageLoad);
            });
        }
    }

    private cloneImagesForSeamlessScroll() {
        if (!this.sliderRef || !this.sliderRef.nativeElement) return;

        const slider = this.sliderRef.nativeElement;
        // 先克隆再清空重加，避免重复绑定事件
        const clone = slider.innerHTML;
        slider.innerHTML = clone + clone;
    }

    onImageLoad = () => {
        if (++this.imagesLoadedCount >= this.requiredLoads && !this.isScrolling) {
            this.isScrolling = true;
            setTimeout(() => this.startScroll(), 100);
        }
    }
    toConsole(){
        const isLogin = (new Date().getTime() / 1000 - parseFloat(window.localStorage.getItem('tokenTime') || '0')) < 0;
        if(isLogin){
            window.location.href = '/console';
            // this.router.navigateByUrl('/console')
        }else{
            window.location.href = '/login';
            // this.router.navigateByUrl('/login')
        }
    }

    private setupImageLoadCheck() {
        if (!this.sliderRef || !this.sliderRef.nativeElement) return;

        const checkImages = () => {
            if (!this.sliderRef || !this.sliderRef.nativeElement) return;

            const imgs = this.sliderRef.nativeElement.querySelectorAll('img');
            imgs.forEach(img => {
                if (img.complete) this.onImageLoad();
            });
        };

        // 使用rxjs的timer替代原生setTimeout
        import('rxjs').then(({ timer }) => {
            timer(0).subscribe(checkImages);
        });
    }

    private startScroll() {
        if (this.animationFrameId || !this.sliderRef || !this.sliderRef.nativeElement) return; // 防止重复启动或无效引用

        const animate = () => {
            if (!this.sliderRef?.nativeElement) return;

            const slider = this.sliderRef.nativeElement;
            const maxScroll = slider.scrollWidth / 2;

            if (slider.scrollLeft >= maxScroll) {
                slider.scrollLeft = 0;
            } else {
                slider.scrollLeft += this.scrollSpeed;
            }

            this.animationFrameId = requestAnimationFrame(animate);
        };

        this.animationFrameId = requestAnimationFrame(animate);
    }

    private stopScroll() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
            this.isScrolling = false;
        }
    }

    logout() {
        this.authService.logout();
    }
}
