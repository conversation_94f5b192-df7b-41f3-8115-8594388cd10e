<div class="rds-config config-content">
    <h4 class="title">
        文件系统创建
        <a routerLink=".." class="back"><i class="icon"
                nz-icon nzType="left"
                nzTheme="outline"></i>返回列表</a>
    </h4>

    <div class="panel">
        <div class="panel-body">
            <section class="field-section">
                <div class="field-title">
                选择用户
                </div>
                <div class="field-group">                 
                    <div style="width: 280px;">
                        <nz-input-group [nzAddOnAfter]="userIconButton" (click)="backupModalVisible = true" nzSearch >
                            <input type="text" nz-input placeholder="请选择用户" readonly="true" value="{{username}}" />
                        </nz-input-group>
                        <ng-template #userIconButton>
                            <button nz-button nzType="primary" nzSearch ><i nz-icon nzType="plus"></i></button>
                        </ng-template>
                    </div>
                 </div>
            </section>
            <form [formGroup]="dilatation" >

                <section class="field-section">
                    <div class="field-title">
                    文件存储
                    </div>
                    <div class="field-group">                 
                        <div style="width: 280px;">
                            <nz-input-group [nzAddOnAfter]="userIconButton" (click)="isVisible = true" nzSearch >
                                <input type="text" nz-input placeholder="请选择文件存储" readonly="true" value="{{ fileName }}" />
                            </nz-input-group>
                            <ng-template #userIconButton>
                                <button nz-button nzType="primary" nzSearch ><i nz-icon nzType="plus"></i></button>
                            </ng-template>
                        </div>
                     </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        内存调整
                    </div>

                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">内存调整</span>
                                    <span class="select-tips">总容量调整至：</span>
                                    <nz-input-number [nzPrecision]=0 [(ngModel)]="size" formControlName="sizeG" 
                                    [nzMin]="10" [nzMax]="500" [nzStep]="1"></nz-input-number>
                                    <span class="select-tip"> GB (范围：10-500GB)</span>
                            </label>
                        </div>
                    </div>
                </section>

                <section class="field-section action" style="padding-top:200px;">
                    <button
                        [nzLoading]="isCreating"
                        type="submit"
                        nz-button
                        nzSize="large"
                        nzType="primary" 
                        (click)="createFileStore()">保存</button>
                </section>
            </form>
        </div>
        <div class="panel-aside pined">
            <section class="field-section">
                <div class="field-title">
                    配置概要
                </div>
                <table class="form-info" *ngIf="dilatation">
                    <tbody>

                        <tr>
                            <td width="20%">用户</td>
                            <td>{{ username || '-' }}
                            </td>
                        </tr>
                        <tr>
                            <td>文件存储</td>
                            <td>{{ fileName || '-' }}
                            </td>
                        </tr>
                        <tr>
                            <td>调整后内存</td>
                            <td>{{ dilatation.value.sizeG || '-'}}
                            </td>
                        </tr> 
                    </tbody>
                </table>
            </section>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="isVisible"
    nzTitle="文件存储"
    [nzOkLoading]="okLoading"
    [nzMaskClosable]="false"
    [nzBodyStyle]="{padding: '24px 8px 8px 8px'}"
    (nzAfterOpen)="strategyModalOpened()"
    [nzWidth]="700"
    (nzOnCancel)="isVisible = false" (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <section class="field-section">
        <div class="order_search">
            <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                <input type="text" [(ngModel)]="searchFile" nz-input placeholder="输入关键字搜索" (keydown.enter)="searchUser()" (ngModelChange)="searchUser()" />
              </nz-input-group>
              <ng-template #suffixIconButton>
                <button nz-button nzType="primary" (click)="searchUser()" nzSearch><i nz-icon nzType="search"></i></button>
              </ng-template>
        </div>
        <div class="field-group">
            <nz-collapse nzAccordion class="order-accordion">
                <section class="field-section">

                    <div class="field-group">
                        <div class="field-item required">
                            <nz-table #tableList
                            [nzPageSize]=99999
                            [nzShowPagination]=false
                            [nzLoading]="isSpinning"
                            [nzData]="fileData">
                                <thead>
                                <tr>
                                    <ng-container *ngFor="let col of cols">
                                        <th
                                          *ngIf="col.width"
                                          nz-resizable
                                          nzBounds="window"
                                          nzPreview
                                          nzColumnKey="col.ColumnKey"
                                          [nzWidth]="col.width"
                                          [nzMaxWidth]="400"
                                          [nzMinWidth]="60"
                                          [nzSortFn]="true"
                                          (nzResizeEnd)="onResize($event, col.title)"
                                        >
                                          {{ col.title }}
                                          <nz-resize-handle nzDirection="right">
                                            <div class="resize-trigger"></div>
                                          </nz-resize-handle>
                                        </th>
                                        <th *ngIf="!col.width">
                                          {{ col.title }}
                                        </th>
                                      </ng-container>
                                    <!-- <th width="10%"></th>
                                    <th width="30%">文件系统</th>
                                    <th>容量</th>
                                    <th width="30%">共享目录</th> -->
                                </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of tableList.data">
                                        <td><nz-radio-group [(ngModel)]="check" nzName="radiogroup">
                                            <label nz-radio nzValue="{{ item.id }}'*'{{ item.sizeG }}'*'{{ item.name }}'*'{{item.ownerId}}"></label>
                                            </nz-radio-group></td>
                                        <td>{{ item.name }}</td>
                                        <td>{{ item.sizeG }}GB</td>
                                        <td>{{ item.sharePath }}</td>
                                    </tr>
                                </tbody>
                            </nz-table>
                        </div>
                    </div>
                </section>
            </nz-collapse>
        </div>
        <div class="text-center" style="padding: 20px 0 15px 0;">
            <nz-pagination [nzPageIndex]="pageIndex" [nzTotal]="total" [nzPageSize]="2"
            (nzPageIndexChange)="PageIndexChange($event)" [nzShowQuickJumper]="false"></nz-pagination>
        </div>
    </section>
    </ng-container>
</nz-modal>

<app-user-vm-selection [isVisible]="backupModalVisible" [strategyId]="currentStrategyId" [cloudServer]="currentCloudServer"
    (close)="backupModalVisible = false" (toParent)="resData($event)">
</app-user-vm-selection>