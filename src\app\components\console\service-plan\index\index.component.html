<div class="service-plan table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-form-item>
                    <nz-form-control>
                        <nz-input-group nzSearch
                                    [nzAddOnAfter]="suffixIconButton">
                            <input type="text" name="keyword"
                                autocomplete="off"
                                [(ngModel)]="keyword" nz-input
                                placeholder="请输入服务计划名称" />
                        </nz-input-group>
                        <ng-template #suffixIconButton>
                            <button nz-button nzType="primary"
                                    nzSearch
                                    (click)="search()"><i nz-icon
                                                nzType="search"></i></button>
                        </ng-template>
                    </nz-form-control>
                </nz-form-item>
                <nz-form-item class="type-select-item">
                    <nz-form-label>类型</nz-form-label>
                    <nz-form-control>
                        <nz-select
                            [(ngModel)]="selectedType"
                            name="selectedType"
                            nzPlaceHolder="请选择类型"
                            nzAllowClear
                            style="width: 150px;"
                            (ngModelChange)="onTypeChange()">
                            <nz-option *ngFor="let option of servicePlanTypeOptions"
                                [nzValue]="option.value"
                                [nzLabel]="option.label">
                            </nz-option>
                        </nz-select>
                    </nz-form-control>
                </nz-form-item>
            </form>
            <div class="right-button-group">
                <div class="pull-right">
                    <button nz-button nzType="primary"
                            (click)="openEditModal()">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建服务计划
                    </button>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">服务计划</span>
            </div>
            <nz-table #servicePlans style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="servicePlanList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th
                              *ngIf="!col.width"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                            >
                              {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of servicePlans.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>{{ getServicePlanType(item.servicePlanType) }}</td>
                        <td>{{ item.servicePlanCode }}</td>
                        <td>{{ item.price }}</td>
                        <td>{{ formatDate(item.autoEffectiveDate) }}</td>
                        <td>{{ formatDate(item.autoExpiryDate) }}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="openEditModal(item)">
                                    <i nzTooltipTitle="编辑"
                                        nzTooltipPlacement="bottom"
                                        nz-tooltip
                                        class="icon iconfont iconfontBianji3 icon-bianji3">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipPlacement="top"
                                    nzPopconfirmTitle="确定要删除该服务计划吗？"
                                    (nzOnConfirm)="deleteServicePlan(item)">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="servicePlans.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-edit *ngIf="editModalVisible"
    [isVisible]="editModalVisible"
    [editData]="editData"
    (close)="closeEditModal()"
    (submit)="handleEditSubmit()">
</app-edit>
