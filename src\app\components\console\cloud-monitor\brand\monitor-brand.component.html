<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" [formGroup]="searchForm" >
                <input type="text" autocomplete="off" formControlName="keyword"
                       nz-input placeholder="请输入事件描述、对象或对象ID" style="width: 220px; margin-right: 10px;" (keyup.enter)="search()"/>
                <nz-select formControlName="selectType" style="margin-left: 20px;width: 200px" nzPlaceHolder="请选择类型" (ngModelChange)="changeType($event)">
                    <nz-option nzValue="ecs" nzLabel="ECS"></nz-option>
                    <nz-option nzValue="k8s" nzLabel="K8S"></nz-option>
                    <nz-option nzValue="rds" nzLabel="RDS"></nz-option>
                    <nz-option nzValue="redis" nzLabel="Redis"></nz-option>
                </nz-select>
            </form>
            <div class="right-button-group">
                <div class="pull-right">
                    <div class="pull-right">
                        <a nz-button class="primary" nzType="primary" (click)="search()">
                            查&nbsp;&nbsp;询
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="on-panel-body table-content">
            <div class="action-bar clearfix">
                <span class="title">{{this.brandName === 'huawei' ? '华为云' : '阿里云'}}</span>
            </div>
            <nz-table #vpcs style="overflow-x: auto"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="dataList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of vpcs.data; trackBy: trackById">
                        <td>{{ item.ip }}</td>
                        <td><a href="javascript:void(0)" (click)="navigateToDetail(item)">{{ item.name }}</a></td>
                        <td>{{ item.type }}</td>
                        <td class="{{item.cpu === 0 ? '' : item.cpu < 60 ? 'green' : item.cpu > 80 ? 'red' : 'yellow'}}">{{ item.cpu }} %</td>
                        <td class="{{item.memory === 0 ? '' : item.memory < 60 ? 'green' : item.memory > 80 ? 'red' : 'yellow'}}">{{ item.memory }} %</td>
                        <td class="{{item.disk === 0 ? '' : item.disk < 60 ? 'green' : item.disk > 80 ? 'red' : 'yellow'}}">{{ item.disk }} %</td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item" *ngIf="isAdmin" style="cursor: pointer"
                                     (click)="navigateToDetail(item)">
                                    <i nzTooltipTitle="详情"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="search" nzTheme="outline"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="vpcs.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">‹</a>
                <a *ngIf="type === 'next'">›</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>

        </div>
    </div>
</div>
