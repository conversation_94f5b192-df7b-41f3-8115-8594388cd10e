import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { EChartsOption, BarSeriesOption } from 'echarts';

export enum BarChartStyle {
  STYLE1 = 'style1', // 默认样式，tooltip 简单显示
  STYLE2 = 'style2', // 堆叠显示，tooltip 详细显示
  STYLE3 = 'style3', // 更多预设样式...
  STYLE4 = 'style4'
}

@Component({
  selector: 'app-column-chart-template',
  templateUrl: './column-chart-template.component.html',
  styleUrls: ['./column-chart-template.component.less']
})
export class ColumnChartTemplateComponent implements OnInit, OnChanges {

  @Input() xData: string[] = [];
  @Input() yData: any[] = [];
  @Input() chartStyle: BarChartStyle = BarChartStyle.STYLE1;
  @Input() yAxisUnit: string = '';
  @Input() title: string = '';
  @Input() isHorizontal: boolean = false; // 横向/纵向开关
  @Input() hideXAxis: boolean = false; // 是否隐藏 X 轴
  @Input() chartHeight: number = 280; // 图表高度，默认280px

  chartOptions: EChartsOption = {};
  updateOptions: EChartsOption = {};
  dataKeys: string[] = [];
  colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']; // 颜色列表

  ngOnInit(): void {
    this.chartOptions = this.getChartOptions(this.chartStyle);
  }

  ngOnChanges(): void {
    this.dataKeys = Object.keys(this.yData[0] || {});
    this.updateOptions = {
      xAxis: {
        type: (this.isHorizontal ? 'value' : 'category') as any,
        data: this.xData,
        show: !this.hideXAxis
      },
      yAxis: {
        type: (this.isHorizontal ? 'category' : 'value') as any,
        name: this.yAxisUnit
      },
      series: this.getSeriesData(this.yData)
    };
  }

  getSeriesData(yData: any[]): any[] {
    if (yData.length > 0 && typeof yData[0] === 'object') {
      const seriesData: any[] = [];
      const dataKeys = Object.keys(yData[0]);

      dataKeys.forEach((key, index) => {
        seriesData.push({
          name: key,
          type: 'bar',
          stack: this.chartStyle === BarChartStyle.STYLE2 ? '总量' : undefined, // 堆叠显示
          data: yData.map(item => {
            const value = item[key];
            return value === null || value === 0 ? undefined : value;
          }),
          barWidth: this.chartStyle === BarChartStyle.STYLE2 ? 30 : undefined,
          label: {
            show: (params: any) => params.data !== undefined,
            position: 'inside' // 将数值放在柱状图内部
          },
          itemStyle: {
            color: this.colors[index % this.colors.length] // 循环使用颜色
          }
        });
      });
      return seriesData;
    } else {
      return [{
        data: yData,
        type: 'bar',
        label: {
          show: (params: any) => params.value !== 0,
          position: 'inside' // 将数值放在柱状图内部
        },
        itemStyle: {
          color: '#5470c6' // 默认颜色
        }
      }];
    }
  }

  getChartOptions(style: BarChartStyle): EChartsOption {
    let tooltipFormatter: any;

    switch (style) {
      case BarChartStyle.STYLE2:
        tooltipFormatter = (params: any) => {
          if (Array.isArray(params)) {
            return params.map(param => {
              const value = param.value === 0 || param.value == null ? undefined : param.value;
              return value !== undefined ? `${param.seriesName}: ${value}${this.yAxisUnit}` : undefined;
            }).filter(Boolean).join('<br />');
          } else {
            const value = params.value === 0 || params.value == null ? undefined : params.value;
            return value !== undefined ? `${params.seriesName}: ${value}${this.yAxisUnit}` : undefined;
          }
        };
        break;
      default:
        tooltipFormatter = (params: any) => {
          if (Array.isArray(params)) {
            return params.map(param => {
              const value = param.data === 0 || param.data == null ? undefined : param.data;
              return value !== undefined ? `${param.seriesName}: ${value}${this.yAxisUnit}` : undefined;
            }).filter(Boolean).join('<br />');
          } else {
            const value = params.data === 0 || params.data == null ? undefined : params.data;
            return `${params.seriesName}: ${value}${this.yAxisUnit}`;
          }
        };
        break;
    }

    const options: EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none'
        },
        formatter: tooltipFormatter
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: (this.isHorizontal ? 'value' : 'category') as any,
        data: this.xData,
        show: !this.hideXAxis
      },
      yAxis: {
        type: (this.isHorizontal ? 'category' : 'value') as any,
        name: this.yAxisUnit
      },
      series: this.getSeriesData(this.yData),
      legend:{
        show: false
      }
    };

    return options;
  }

}
