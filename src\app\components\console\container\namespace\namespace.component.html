<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword" autocomplete="off" [(ngModel)]="keyword" nz-input placeholder="请输入命名空间名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch><i nz-icon nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <button nz-button nzType="primary" (click)="showAddNamespaceWindow()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新增命名空间
                    </button>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">命名空间</span>
            </div>
            <nz-table #namespaceList
                      style="overflow-x: auto"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="namespacePage.total"
                      [nzPageIndex]="namespacePage.current"
                      [nzPageSize]="namespacePage.size"
                      (nzPageIndexChange)="namespacePageChange($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="namespaceListData">
                <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)">
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width">
                            {{ col.title }}
                        </th>
                    </ng-container>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of namespaceList.data; trackBy: trackById">
                    <td class="fixed-td">{{ item.name }}</td>
                    <td class="fixed-td">{{ item.des || '-' }}</td>
                    <td>{{ getdate(item.createTm) }}</td>
                    <td>
                        <span class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status, 'namespace') }}</span>
                    </td>
                    <td>
                        <div class="on-table-actions" *ngIf="permission('update')"
                             [hidden]="busyStatus[item.id]">
                            <div class="on-table-action-item"
                                 (click)="showUpdateNamespaceWindow(item)"
                                 [ngClass]="{'disabled': !namespaceCanOperate(item)}">
                                <i nzTooltipTitle="修改"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon fa fa-wrench"></i>
                            </div>
                            <div class="on-table-action-item" *ngIf="permission('delete')"
                                 nz-popconfirm
                                 nzTooltipContent="top"
                                 [nzCondition]="!namespaceCanOperate(item)"
                                 nzTitle="删除命名空间会同时删除该命名空间下的所有服务！确定要删除该命名空间吗？"
                                 (nzOnConfirm)="deleteNamespace(item)"
                                 [ngClass]="{'disabled': !namespaceCanOperate(item)}">
                                <i nzTooltipTitle="删除"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon fa fa-trash-o"></i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                             [hidden]="!busyStatus[item.id]">
                            <div class="action-loading-placeholder">
                                <i class="icon" nz-icon [nzType]="'loading'"></i>
                                {{ getBusyText(item.id) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="namespaceList.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 创建命名空间弹出层 -->
<nz-modal [(nzVisible)]="showCreateNamespaceWindow"
          nzTitle="{{ createOrUpdateNamespaceTitle }}"
          (nzOnCancel)="showCreateNamespaceWindow = false"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submitAddOrUpdateNamespace()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">命名空间名称：</span>
        <input nz-input
               style="width: 300px;"
               maxlength="63"
               [disabled]="!ifCreateNamespace"
               (blur)="checkNamespaceName()"
               [(ngModel)]="namespaceName"/>
        <p [ngClass]="{'warning-tips': !namespaceNameReg}">*长度为2-63个字符之间(含),可包含小写字母、数字及分隔符("-"),不能以分隔符开头或结尾</p>
    </div>
    <div class="select-container">
        <span class="select-tips">命名空间描述：</span>
        <textarea style="width: 300px; height: 120px"
                  nz-input rows="2"
                  (blur)="checkNamespaceDes()"
                  [(ngModel)]="namespaceDes"
                  placeholder="命名空间描述(长度200字符以内)">
        </textarea>
        <p [ngClass]="{'warning-tips': !namespaceDesReg}">*长度需在200字符以内</p>
    </div>
    </ng-container>
</nz-modal>
