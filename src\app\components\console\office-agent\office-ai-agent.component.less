.office-content{
  //background-color: #fff;
  //background-image: linear-gradient(to bottom, rgba(44, 88, 231, .1) 0%, rgb(255, 255, 255) 100%);
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0 0;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  background-color: #e0e0e0;
  border-radius: 10px;
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
}

.subtitle {
  text-align: center;
  color: #666;
  margin: 20px 0 30px;
}

.user-profile {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #2c6cf7;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  cursor: pointer;
}

.main-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
}

.features-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  min-width: 750px;
  //max-width: 1000px;
}

.feature-col {
  margin-bottom: 20px;
}

/* 第一列：文稿创作 */
.feature-col:first-child {
  width: calc(25% - 20px);
}

/* 第二列：文档解析和多言互译 */
.feature-col:nth-child(2) {
  width: calc(25% - 20px);
}

/* 第三列：智能校对和数据分析 */
.feature-col:nth-child(3) {
  width: calc(25% - 20px);
}

/* 第四列：知识问答和智能绘图 */
.feature-col:last-child {
  width: calc(25% - 20px);
}

.half-width{
  width: calc(50%);
}

.feature-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-card {
  background-color: #fff;
  border-radius: 10px;
  //padding: 20px;
  height: 180px;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #E8E8E8;
  .mask{
    position: absolute;
    bottom: 0;
    right: 0;
    display: none;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100% - 48px);
    background: linear-gradient(#F7F8F9 0%, rgba(255, 255, 255, .6) 10%, rgba(255, 255, 255, .6) 100%);
    backdrop-filter: blur(10px);
    z-index: 18;
    border-radius: 10px;
    transition: display 1s ease;
    span{
      padding-bottom: 30px;
      font-weight: 700;
      font-size: 14px;
      background-image: linear-gradient(to right, #AD72FF 0%, #314CF2 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

.feature-card:hover {
  box-shadow: 0 2px 20px rgba(0, 0, 0, .06);;
}

.disabled{
  cursor: default;
}

.disabled:hover .mask{
  display: flex;
}

.feature-content{
  padding:20px;
}
.full-height{
  height: 100%;
}

.feature-icon {
  width: 24px;
  height: 24px;
  //margin-bottom: 10px;
  font-size: 14px;
}

.feature-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.feature-desc {
  font-size: 12px;
  font-weight: 400;
  color: #7f7f7f;

  flex-grow: 1;
  display: flex;
  align-items: center;
}

.wide-feature-card {
  width: 100%;
}

.input-container {
  margin: 20px auto 5px;
  padding: 0 20px;
  max-width: 1100px;
}

.input-box {
  background: #fff;
  width: 100%;
  height: 120px;
  border: 1px solid #2c6cf7;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.input-textarea {
  flex-grow: 1;
  border: none;
  resize: none;
  outline: none;
  font-size: 14px;
}

.input-footer {
  display: flex;
  margin-top: 10px;
  align-items: flex-end;
}

.input-button {
  background-color: #f5f7fa;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 0 10px;
  margin-right: 10px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 24px;
}

.input-button i {
  margin-right: 5px;
}

.input-button:hover{
  background-color: #e9eefd;
  border-color: #e9eefd;
}

.input-button.active{
  color: #345de3;
  background-color: #e9eefd;
  border-color: #d3ddfe;
}

.send-button {
  margin-left: auto;
  background-color: #345de3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: not-allowed;
}

.footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 20px 0;
}

.bg-item{
  top: 28.24%;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 10px;
}

.manuscript-creation-animation-wrapper{
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .one{
    position: absolute;
    bottom: -10.79%;
    right: -17.36%;
    width: 106.36%;
    z-index: 5;
  }
  .two{
    position: absolute;
    bottom: -7.7%;
    right: -28.45%;
    width: 115.91%;
    z-index: 4;
    transition: transform 1s ease-in-out;
    transform: rotate(0);
  }
  .three{
    position: absolute;
    bottom: -10.6%;
    right: -42.09%;
    width: 121.36%;
    z-index: 3;
    transition: transform 1s ease-in-out;
    transform: rotate(-16deg) translateY(20px);
  }

}

.manuscript-creation-animation-wrapper:hover{
  .two{
    transform: rotate(5deg);
  }
  .three{
    transform: rotate(10deg) translateY(0);
  }
}

.document-parsing-animation-wrapper{
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .one{
    position: absolute;
    top: 29.9%;
    left: 27.18%;
    width: 137%;
    transition: transform .5s ease-in-out;
    transform: rotate(0);
  }
  .two{
    position: absolute;
    top: 35.6%;
    left: 13.68%;
    width: 140%;
  }
}

.document-parsing-animation-wrapper.document-parsing-animation:hover .one{
  transform: rotate(5deg);
}

.language-animation-wrapper{
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .one{
    position: absolute;
    bottom: -16.56%;
    right: -8.3%;
    width: 48.89%;
  }
  .two{
    position: absolute;
    bottom: 6.13%;
    right: 47.22%;
    width: 33.3%;
  }
  .three{
    position: absolute;
    bottom: 35.58%;
    right: 22.77%;
    width: 21.11%;
  }
}

.language-animation-wrapper:hover{
  .one{
    animation: float 2s ease-in-out infinite;
  }

  .two{
    animation: float 1.8s ease-in-out infinite;
  }

  .three{
    animation: float 1.6s ease-in-out infinite;
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0);
  }
}

.knowledgeQA-animation-wrapper{
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .one{
    position: absolute;
    bottom: -3.91%;
    right: -1.87%;
    width: 29.41%;
    z-index: 8;
    border-radius: 8px;
  }
  .two{
    position: absolute;
    bottom: -19.63%;
    right: -3.87%;
    width: 38.77%;
    z-index: 7;
    transition: transform 1s ease-in-out;
    transform: rotate(0);
  }
  .three{
    position: absolute;
    bottom: -30.67%;
    right: -6.14%;
    width: 45.19%;
    z-index: 6;
    transition: transform 1s ease-in-out;
    transform: rotate(0);
  }
}

.knowledgeQA-animation-wrapper:hover{
  .two,.three{
    transform: rotate(-10deg);
  }
}

.intelligent-draw-animation-wrapper{
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .one,.two,.three{
    position: absolute;
    bottom: -12.56%;
    right: -24.78%;
    width: 110%;
    img{
      border-radius: 8px;}
  }
  .one{z-index: 3}
  .two{z-index: 2}
  .three{z-index: 1}
}
.intelligent-draw-animation-wrapper:hover{
  .one{
    animation: moveRight 2s ease-in-out 0.3s 1 forwards;
  }
  .two{
    animation: moveRight 4s ease-in-out 2.3s 1 forwards;
  }
}
@keyframes moveRight {
  0% { transform: translateX(0); }
  100% { transform: translateX(500px); }
}
.el-image{
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.animation {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10
}
.el-image__error, .el-image__placeholder, .el-image__wrapper, .el-image__inner {
  width: 100%;
  height: 100%;
}

