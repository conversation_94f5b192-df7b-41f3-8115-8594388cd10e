# BPMN.js 中文汉化实现

## 📋 概述

本项目已成功集成 `bpmn-js-i18n-zh` 包，实现了 BPMN.js 编辑器的中文汉化功能，包括：

- ✅ BPMN 编辑器界面中文化
- ✅ 属性面板中文化  
- ✅ Camunda 扩展属性中文化
- ✅ 工具栏和上下文菜单中文化

## 🚀 已安装的依赖

```json
{
  "bpmn-js-i18n-zh": "^1.3.0"
}
```

## 📁 文件结构

```
src/app/
├── utils/
│   └── bpmn-translate.ts          # 中文翻译模块
└── components/console/system/bpmn/
    ├── editor/
    │   └── bpmn-editor.component.ts    # 主编辑器（已集成翻译）
    └── config/
        └── bpmn-config.component.ts    # 配置弹窗（已集成翻译）
```

## 🔧 实现细节

### 1. 翻译模块 (`src/app/utils/bpmn-translate.ts`)

```typescript
// 导入所有翻译资源
import bpmn from 'bpmn-js-i18n-zh/lib/bpmn-js';
import properties from 'bpmn-js-i18n-zh/lib/properties-panel';
import camunda from 'bpmn-js-i18n-zh/lib/camunda-properties-panel';
import zeebe from 'bpmn-js-i18n-zh/lib/zeebe-properties-panel';

// 合并翻译资源
const zhCN = {
  ...bpmn,
  ...properties,
  ...camunda,
  ...zeebe,
  // 自定义翻译内容
};

// 自定义翻译函数
export function customTranslate(template: string, replacements?: Record<string, any>): string {
  // 翻译逻辑
}

// 导出翻译模块
export default {
  translate: ['value', customTranslate]
};
```

### 2. 编辑器集成

在 BPMN 建模器配置中添加翻译模块：

```typescript
this.bpmnModeler = new BpmnModeler({
  container: this.bpmnContainer.nativeElement,
  propertiesPanel: {
    parent: this.propertiesPanel.nativeElement
  },
  additionalModules: [
    BpmnPropertiesPanelModule,
    CamundaPlatformPropertiesProviderModule,
    TranslateModule  // 🎯 中文翻译模块
  ],
  moddleExtensions: {
    camunda: camundaModdleDescriptor
  }
});
```

## 🌟 支持的中文化内容

### 基础 BPMN 元素
- ✅ 开始事件 (Start Event)
- ✅ 结束事件 (End Event)  
- ✅ 用户任务 (User Task)
- ✅ 服务任务 (Service Task)
- ✅ 排他网关 (Exclusive Gateway)
- ✅ 并行网关 (Parallel Gateway)
- ✅ 子流程 (Sub Process)

### 工具栏和菜单
- ✅ 创建元素工具
- ✅ 连接工具
- ✅ 手型工具
- ✅ 套索工具
- ✅ 上下文菜单

### 属性面板
- ✅ 通用属性
- ✅ Camunda 扩展属性
- ✅ 表单字段
- ✅ 监听器配置

## 🎯 自定义翻译

如需添加自定义翻译，可在 `bpmn-translate.ts` 中的 `zhCN` 对象中添加：

```typescript
const zhCN = {
  ...bpmn,
  ...properties,
  ...camunda,
  ...zeebe,
  // 🎯 自定义翻译
  'Custom Element': '自定义元素',
  'My Custom Property': '我的自定义属性'
};
```

## 🚀 使用方法

1. **启动项目**：
   ```bash
   npm run start
   ```

2. **访问 BPMN 编辑器**：
   - 导航到流程管理页面
   - 点击"新增流程"或"设计流程"
   - 编辑器界面将显示中文

3. **验证中文化**：
   - 工具栏按钮显示中文提示
   - 右键菜单显示中文选项
   - 属性面板显示中文标签

## 📝 注意事项

1. **兼容性**：支持 bpmn-js 18.x 版本
2. **性能**：翻译模块不会影响编辑器性能
3. **扩展性**：可以轻松添加自定义翻译内容
4. **维护性**：翻译资源集中管理，便于维护

## 🔄 更新翻译

如需更新翻译包：

```bash
npm update bpmn-js-i18n-zh
```

## 🎉 完成状态

- ✅ 依赖包安装完成
- ✅ 翻译模块创建完成
- ✅ 主编辑器集成完成
- ✅ 配置弹窗集成完成
- ✅ 项目编译成功
- ✅ 中文化功能可用

现在您的 BPMN 编辑器已经完全支持中文界面！🎊
