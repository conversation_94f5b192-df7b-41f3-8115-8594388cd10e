<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <a nz-button disabled="true"
                       (click)="createModalVisible = true;">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建RabbitMQ
                    </a>
                </div>
            </div>
            <h3 class="title"></h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">RabbitMQ</span>
            </div>
            <nz-table
                #mqs
                [nzItemRender]="renderItemTemplate"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChange($event)"
                [nzData]="rabbitMQ">
                <thead>
                    <tr>
                        <th width="15%">名称</th>
                        <th width="20%">VPC</th>
                        <th width="20%">子网</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of mqs.data">
                        <td>
                            <a nz-popover
                                [nzPopoverContent]="contentTemplate"
                                href="javascript:void(0)"
                                class="access-detail">
                                {{ item.name }}
                                <i nz-icon nzType="info-circle" nzTheme="outline"></i>
                            </a>
                            <ng-template #contentTemplate>
                                <table class="access-table">
                                    <tbody>
                                        <tr>
                                            <td>访问地址</td>
                                            <td>
                                                <span>{{ item.host }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>访问端口</td>
                                            <td>
                                                <span> {{ item.port }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>管理账号</td>
                                            <td>{{ item.adminName }}</td>
                                        </tr>
                                        <tr>
                                            <td>管理密码</td>
                                            <td>
                                                <span class="show-password"
                                                    [hidden]="item.showPassword"
                                                    (click)="item.showPassword = true;">点击查看</span>
                                                <span [hidden]="!item.showPassword"
                                                    (click)="item.showPassword = false;"
                                                    title="点击隐藏"
                                                    class="admin-password">{{ item.adminPassword }}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </ng-template>
                        </td>
                        <td>
                            {{ item.vpcName }}
                        </td>
                        <td>
                            {{ item.networkName }}
                        </td>
                        <td>
                            <span [class]="getStatusClass(item)">{{ item.status }}</span>
                        </td>
                        <td>
                            {{ item.createTm | date: 'yyyy-MM-dd hh:mm:ss' }}
                        </td>
                        <td class="operation">
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzCondition]="!canDelete(data)"
                                    nzTitle="确定要删除该容器吗？"
                                    (nzOnConfirm)="deleteMQ(item);"
                                    [ngClass]="{'disabled': !canDelete(item)}">
                                <i nzTitle="删除"
                                    nzTooltipContent="bottom"
                                    nz-tooltip
                                    class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-rabbitmq-config
[isVisible]="createModalVisible"
[initData]="initData"
(submit)="getMQList()"
(close)="createModalVisible = false;"></app-rabbitmq-config>