import { ApplicationConfig } from '@angular/core';
import { provideRouter } from '@angular/router';
import { appRoutes } from './app-routing.module';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { HttpInterceptorService } from './service/common/httpInterceptor.service';
import { HttpListenerInterceptor } from './interceptors/http-listener-interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(appRoutes),
    provideAnimations(),
    provideHttpClient(
      withInterceptors([
        HttpInterceptorService,
        HttpListenerInterceptor
      ])
    )
  ]
};
