<div class="instance-config config-content">
    <h4 class="title">
        Kubernetes服务部署
        <a (click)="toK8sDetail()" class="back">
            <i class="icon" nz-icon nzType="left" nzTheme="outline">
            </i>返回列表
        </a>
    </h4>
    <div class="panel">
        <div class="panel-body">
            <!--基本信息-->
            <div class="choose-module">
                <div class="choose-module-txt">
                    <span class="line"></span>
                    <span class="txt">基本信息</span>
                    <div class="clear"></div>
                </div>
                <div class="choose-module-inp">
                    <div class="inp">
                        <label>服务名称*</label>
                        <div class="choosable-box">
                            <input class="margin-input" maxlength="50" [(ngModel)]="name" (blur)="checkName()"
                                type="text" style="width: 40%" />
                            <p class="form-label-tips" [ngStyle]="{'color':nameCheck ? '#999': '#E01B2F'}">
                                最长50个字符，只能包含小写字母、数字及分隔符("-")，且必须以小写字母开头，数字或小写字母结尾</p>
                        </div>
                    </div>
                </div>
                <div class="choose-module-inp">
                    <div class="inp">
                        <label>命名空间*</label>
                        <div class="choosable-box">
                            <nz-select style="width: 40%; margin-left: 10px" [(ngModel)]="selectNamespace">
                                <nz-option *ngFor="let item of namespaceList" [nzValue]="item" [nzLabel]="item">
                                </nz-option>
                            </nz-select>
                        </div>
                    </div>
                </div>
            </div>
            <!--部署设置-->
            <div class="choose-module">
                <div class="choose-module-txt">
                    <span class="line"></span>
                    <span class="txt">部署设置</span>
                    <div class="clear"></div>
                </div>
                <!-- 数据卷 -->
                <div class="choose-module-inp">
                    <div class="inp">
                        <label>数据卷(可选)</label>
                        <div class="choosable-box">
                            <div class="choosable-box-item" *ngFor="let item1 of volumeList; index as i">
                                <nz-select style="width: 200px; margin-right: 10px" [(ngModel)]="item1.selectVolumeId">
                                    <nz-option *ngFor="let item2 of item1.volumeTypeOption" [nzValue]="item2.value"
                                        [nzLabel]="item2.name">
                                    </nz-option>
                                </nz-select>
                                <input class="margin-input" [(ngModel)]="item1.name"
                                    (blur)="showMountPoint(item1.name, i)" type="text" placeholder="名称" />
                                <input class="margin-input" [(ngModel)]="item1.path" type="text" placeholder="原路径" />
                                <span class="fa fa-times-circle" nzTitle="删除数据卷" (click)="deleteVolume(i)"
                                    nzTooltipContent="bottomCenter" nz-tooltip></span>
                            </div>
                            <p class="warning-text" *ngIf="duplicateVolumeName" style="margin-left: 10px">*数据卷名重复！</p>
                            <p class="choosable-add-text" *ngIf="!duplicateVolumeName" (click)=addVolume()>
                                <i class="fa fa-plus-circle"></i>
                                <span>添加数据卷</span>
                            </p>
                        </div>
                    </div>
                </div>
                <!-- 运行容器 -->
                <div class="choose-module-inp">
                    <div>
                        <label style="color: #232323;">运行容器*</label>
                        <div class="choosable-box choosable-box-bg" style="margin-left: 40px;padding-top: 0;">
                            <nz-collapse>
                                <nz-collapse-panel *ngFor="let item1 of containerList; index as i1"
                                    [nzHeader]="'容器' + (i1 + 1) + ' ' + item1.name" [nzExtra]="extraTpl"
                                    [nzActive]="item1.active">
                                    <div class="choosable-box-item">
                                        <div class="choosable-box-item-part">
                                            <label>名称*</label>
                                            <input class="margin-input" maxlength="50" [(ngModel)]="item1.name"
                                                type="text" placeholder="名称" style="width: 40%" />
                                            <p class="form-label-tips">*容器名称只能包含小写字母、数字及分隔符("-")，且不能以分隔符开头或结尾</p>
                                        </div>
                                        <div class="choosable-box-item-part">
                                            <label>镜像*</label>
                                            <input class="margin-input" maxlength="70" [(ngModel)]="item1.mirror"
                                                type="text" placeholder="镜像" style="width: 40%" />
                                        </div>
                                        <!-- 挂载点 -->
                                        <div class="choosable-box-item-part" *ngIf="ifShowMountPoint">
                                            <label>挂载点</label>
                                            <div class="choosable-box-item-part-item">
                                                <div *ngFor="let item2 of item1.mountPoint; index as i2">
                                                    <nz-select style="width: 170px; margin: 0 10px 10px 10px"
                                                        nzPlaceHolder="请选择"
                                                        (ngModelChange)="getVolumeName($event, item2)"
                                                        [(ngModel)]="item2.selectMountPointId">
                                                        <nz-option *ngFor="let item3 of item2.mountPointOption"
                                                            [nzValue]="item3.id" [nzLabel]="item3.name">
                                                        </nz-option>
                                                    </nz-select>
                                                    <input class="margin-input" [(ngModel)]="item2.targetPath"
                                                        type="text" placeholder="目标路径" />
                                                    <nz-select style="width: 100px; margin: 0 10px 10px 10px"
                                                        [(ngModel)]="item2.selectMountPointTypeId">
                                                        <nz-option *ngFor="let item3 of item2.mountPointTypeOption"
                                                            [nzValue]="item3.value" [nzLabel]="item3.name">
                                                        </nz-option>
                                                    </nz-select>
                                                    <span class="fa fa-times-circle" nzTitle="删除挂载点"
                                                        (click)="deleteMountPoint(i1, i2)" nzTooltipContent="bottomCenter"
                                                        nz-tooltip></span>
                                                </div>
                                                <p class="choosable-add-text" style="margin-left: 10px"
                                                    (click)=addMountPoint(i1)>
                                                    <i class="fa fa-plus-circle"></i>
                                                    <span>添加挂载点</span>
                                                </p>
                                            </div>
                                        </div>
                                        <!-- cpu/内存限制 -->
                                        <div class="choosable-box-item-part">
                                            <label>CPU/内存限制*</label>
                                            <div class="choosable-box-item-part-separate-area">
                                                <p class="separate-area-title">CPU限制</p>
                                                <div>
                                                    <nz-input-group nzCompact>
                                                        <input class="input-before" type="text" disabled=true nz-input
                                                            [ngModel]="'request'" />
                                                        <nz-input-number [nzPrecision]=2 [nzStep]="0.01" [nzMin]=0
                                                            [nzMax]=32 nz-input [(ngModel)]="item1.cpuRequest"
                                                            class="number-input">
                                                        </nz-input-number>
                                                        <span class="separate-icon"></span>
                                                        <input type="text" class="input-before" disabled=true nz-input
                                                            [ngModel]="'limit'" />
                                                        <nz-input-number type="number" [nzPrecision]=2 [nzStep]="0.01"
                                                            [nzMax]=32 [nzMin]="item1.cpuRequest" nz-input
                                                            [(ngModel)]="item1.cpuMax" class="number-input">
                                                        </nz-input-number>
                                                        <span class="separete-until">核</span>
                                                    </nz-input-group>
                                                    <p class="tips-text">*CPU最大32，且limit的值必须大于request的值</p>
                                                </div>
                                            </div>
                                            <div class="choosable-box-item-part-separate-area">
                                                <p class="separate-area-title">内存限制</p>
                                                <div>
                                                    <nz-input-group nzCompact>
                                                        <input type="text" disabled=true nz-input [ngModel]="'request'"
                                                            class="input-before" />
                                                        <nz-input-number type="number" [nzPrecision]=0 [nzStep]="1"
                                                            [nzMax]=64*1024 [nzMin]="0" nz-input
                                                            [(ngModel)]="item1.memRequest" class="number-input">
                                                        </nz-input-number>
                                                        <span class="separate-icon"></span>
                                                        <input type="text" disabled=true nz-input [ngModel]="'limit'"
                                                            class="input-before" />
                                                        <nz-input-number type="number" [nzPrecision]=0 [nzStep]="1"
                                                            [nzMax]=64*1024 [nzMin]="item1.memRequest" nz-input
                                                            [(ngModel)]="item1.memMax" class="number-input">
                                                        </nz-input-number>
                                                        <span class="separete-until">MB</span>
                                                    </nz-input-group>
                                                    <p class="tips-text">*内存最大64GB，且limit的值必须大于request的值</p>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 环境变量 -->
                                        <div class="choosable-box-item-part">
                                            <label>环境变量(可选)</label>
                                            <div class="choosable-box-item-part-item">
                                                <div *ngFor="let item2 of item1.environmentVariables; index as i2">
                                                    <input class="margin-input" maxlength="40" [(ngModel)]="item2.name"
                                                        type="text" placeholder="变量名" />
                                                    =
                                                    <input class="margin-input" maxlength="30" [(ngModel)]="item2.value"
                                                        type="text" placeholder="变量值" />
                                                    <span class="fa fa-times-circle" nzTitle="删除环境变量"
                                                        (click)="deleteEnvironmentVariable(i1, i2)"
                                                        nzTooltipContent="bottomCenter" nz-tooltip></span>
                                                </div>
                                                <p class="choosable-add-text" style="margin-left: 10px"
                                                    (click)=addEnvironmentVariable(i1)>
                                                    <i class="fa fa-plus-circle"></i>
                                                    <span>添加环境变量</span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="choosable-box-item-part">
                                            <label>运行命令(可选)</label>
                                            <input class="margin-input" maxlength="200" [(ngModel)]="item1.command"
                                                type="text" placeholder="运行命令" style="width: 40%" />
                                        </div>
                                        <div class="choosable-box-item-part">
                                            <label>运行参数(可选)</label>
                                            <textarea [(ngModel)]="item1.variables" maxlength="100"></textarea>
                                            <p class="input-tips">*多条参数请用换行隔开</p>
                                        </div>
                                    </div>
                                    <ng-template #extraTpl>
                                        <i class="fa fa-times-circle" nzTitle="删除容器" nzTooltipContent="bottomCenter"
                                            nz-tooltip (click)="deleteContainer(i1)">
                                        </i>
                                    </ng-template>
                                </nz-collapse-panel>
                            </nz-collapse>
                            <p class="choosable-add-text" (click)=addContainer()>
                                <i class="fa fa-plus-circle"></i>
                                <span>添加容器</span>
                            </p>
                        </div>
                    </div>
                </div>
                <!-- 实例数量 -->
                <div class="choose-module-inp">
                    <div class="inp-with-number-input">
                        <label>实例数量</label>
                        <div class="choosable-box">
                            <nz-input-number [(ngModel)]="serviceNumber" [nzMin]="1" [nzMax]="10" [nzStep]="1">
                            </nz-input-number> 个
                        </div>
                    </div>
                </div>
            </div>
            <!--访问设置-->
            <div class="choose-module">
                <div class="choose-module-txt">
                    <span class="line"></span>
                    <span class="txt">访问设置</span>
                    <div class="clear"></div>
                </div>
                <!-- 访问方式 -->
                <div class="choose-module-inp">
                    <div class="inp-with-number-input">
                        <label>服务访问方式</label>
                        <div class="choosable-box">
                            <nz-radio-group [(ngModel)]="accessMode" (ngModelChange)="chooseAccessMode()">
                                <label style="width: 120px" nz-radio [nzValue]="o.value"
                                    *ngFor="let o of accessModeOption">{{ o.name }}</label>
                            </nz-radio-group>
                        </div>
                    </div>
                </div>
                <!-- 端口映射 -->
                <div class="choose-module-inp">
                    <div class="inp">
                        <label>端口映射*</label>
                        <div class="choosable-box">
                            <div class="choosable-box-item choosable-box-item-border">
                                <nz-table #tableList [nzPageSize]=99999 [nzShowPagination]=false [nzData]="portList">
                                    <thead>
                                        <tr>
                                            <th>协议</th>
                                            <th>容器端口</th>
                                            <th>服务端口</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item1 of tableList.data; index as i">
                                            <td>
                                                <nz-select style="width: 200px; margin-right: 10px"
                                                    [(ngModel)]="item1.selectProtocolId">
                                                    <nz-option *ngFor="let item2 of item1.protocolOption"
                                                        [nzValue]="item2.value" [nzLabel]="item2.name">
                                                    </nz-option>
                                                </nz-select>
                                            </td>
                                            <td>
                                                <input style="width: 80px;padding-left: 5px;"
                                                    [(ngModel)]="item1.containerPort" type="text" (blur)="checkPort()"
                                                    placeholder="容器端口" />
                                            </td>
                                            <td>
                                                <input style="width: 80px;padding-left: 5px;"
                                                    [(ngModel)]="item1.servicePort" type="text" (blur)="checkPort()"
                                                    placeholder="服务端口" />
                                            </td>
                                            <td>
                                                <div class="table-btn-box">
                                                    <a href="javascript:void(0);" nzTitle="删除端口映射"
                                                        (click)="deletePort(i, item1.id)" nzTooltipContent="bottomCenter"
                                                        nz-tooltip>
                                                        <span class="fa fa-times-circle"
                                                            style="vertical-align: top"></span>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </nz-table>
                            </div>
                            <p class="choosable-add-text" (click)=addPort()>
                                <i class="fa fa-plus-circle"></i>
                                <span>添加端口映射</span>
                            </p>
                            <p class="warning-text" *ngIf="!portCheck">*端口输入不规范！</p>
                        </div>
                    </div>
                </div>
            </div>
            <section class="field-section action">
                <button type="submit" nz-button (click)="submitAdd()" nzSize="large" nzType="primary">创建</button>
            </section>
        </div>
        <div class="panel-aside pined">
            <section class="field-section">
                <div class="field-title">
                    配置概要
                </div>
                <table class="form-info">
                    <tbody>
                        <tr>
                            <td>名称</td>
                            <td>{{ name || '-'}}
                            </td>
                        </tr>
                        <tr>
                            <td>命名空间</td>
                            <td>{{ selectNamespace || '-'}}
                            </td>
                        </tr>
                        <tr *ngFor="let item of volumeList; index as i">
                            <td>数据卷{{ i + 1 }}</td>
                            <td>
                                {{ item.name || '-'}}
                            </td>
                        </tr>
                        <tr *ngFor="let item of containerList; index as i">
                            <td>容器{{ i + 1 }}</td>
                            <td>{{ item.name || '-' }}
                            </td>
                        </tr>
                        <tr>
                            <td>实例数量</td>
                            <td>{{ serviceNumber }}
                            </td>
                        </tr>
                        <tr>
                            <td>服务访问方式</td>
                            <td>{{ accessModeName }}
                            </td>
                        </tr>
                        <tr *ngFor="let item of portList; index as i">
                            <td>端口映射{{ i + 1 }}</td>
                            <td>容器端口-{{item.containerPort}}，服务端口-{{item.servicePort}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </div>
    </div>
</div>
