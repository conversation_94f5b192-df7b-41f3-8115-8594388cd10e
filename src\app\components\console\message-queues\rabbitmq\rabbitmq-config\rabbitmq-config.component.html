<nz-modal [(nzVisible)]="isVisible"
    nzTitle="创建RabbitMQ实例"
    nzOkText="创建"
    (nzAfterOpen)="mqModalOpened()"
    [nzMaskClosable]="false"
    [nzBodyStyle]="{padding: 0}"
    (nzOnCancel)="handleCancel()"
    [nzOkLoading]="isCreating"
    (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <form [formGroup]="rabbitMQ" class="config-content sm">
        <section class="field-section">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">实例名称</span>
                        <!-- <input type="text" formControlName="name" placeholder="请输入实例名称"> -->
                        <input nz-input placeholder="请输入实例名称" formControlName="name" />
                    </label>
                    <div *ngIf="isDirty(rabbitMQ.get('name'))" class="form-hint error">
                        <div *ngIf="rabbitMQ.get('name').hasError('required')">
                           实例名称不能为空
                        </div>
                        <div *ngIf="rabbitMQ.get('name').hasError('maxlength')">
                           实例名称长度不能超过{{ rabbitMQ.get('ruleName').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-group">
                <div class="field-item required" *ngIf="initData">
                    <label>
                        <span class="label-text">专有网络</span>
                        <nz-select formControlName="vpcId" nzPlaceHolder="请选择专有网络"
                            (ngModelChange)="selectVpc($event)">
                            <nz-option *ngFor="let item of initData.vpcs" [nzValue]="item" [nzLabel]="item.displayName">
                            </nz-option>
                        </nz-select>
                    </label>
                    <div class="form-hint error" *ngIf="isDirty(rabbitMQ.get('vpcId'))">
                        <div *ngIf="rabbitMQ.get('vpcId').hasError('required')">
                            请选择一个专有网络
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">子网</span>
                        <nz-select formControlName="ovdcNetworkId" nzPlaceHolder="请选择子网">
                            <nz-option *ngFor="let item of rabbitMQ.value.vpcId.networks" [nzValue]="item.id"
                                [nzLabel]="item.displayName">
                            </nz-option>
                        </nz-select>
                    </label>
                    <div class="form-hint error" *ngIf="isDirty(rabbitMQ.get('ovdcNetworkId'))">
                        <div *ngIf="rabbitMQ.get('ovdcNetworkId').hasError('required')">
                            请选择一个子网
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <p class="small tip ph15">
            如需使用其它专有网络，请选择已有专有网络，也可以到
                <a routerLink="/console/proprietary-network/index">VPC控制台</a>
            创建
        </p>
    </form>
    </ng-container>
</nz-modal>