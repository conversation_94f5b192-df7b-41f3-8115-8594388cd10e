const proxyConfig = require('./proxy.conf.json');
const { EventEmitter } = require('events');

// 设置 EventEmitter 的最大监听器数量
if (typeof process !== 'undefined' && process.setMaxListeners) {
  process.setMaxListeners(30); // 增加到30
}

// 设置 EventEmitter 默认最大监听器数量
EventEmitter.defaultMaxListeners = 30;

// 为每个代理配置添加额外的选项
Object.keys(proxyConfig).forEach(key => {
  const config = proxyConfig[key];

  // 添加默认的超时设置
  if (!config.proxyTimeout) {
    config.proxyTimeout = 60000; // 60秒
  }

  if (!config.timeout) {
    config.timeout = 60000; // 60秒
  }

  // 添加事件处理器
  config.onProxyRes = function(proxyRes, req, res) {
    // 处理代理响应
    proxyRes.headers['x-proxy-by'] = 'Angular CLI Proxy';
  };

  config.onError = function(err, req, res) {
    console.error('代理错误:', err);
    res.writeHead(500, {
      'Content-Type': 'text/plain'
    });
    res.end('代理请求错误: ' + err);
  };

  // 设置代理服务器创建时的回调
  config.onProxyReq = function(proxyReq, req, res, options) {
    // 处理代理请求
  };

  // 设置代理服务器实例的最大监听器数量
  config.createProxyServer = {
    agent: false,
    xfwd: true,
    secure: false,
    prependPath: false,
    ignorePath: false,
    changeOrigin: config.changeOrigin || false,
    proxyTimeout: config.proxyTimeout || 60000,
    timeout: config.timeout || 60000,
    setupListeners: function(proxy) {
      // 确保代理服务器实例设置了足够的监听器数量
      if (proxy && proxy.setMaxListeners) {
        proxy.setMaxListeners(30);
      }

      // 确保代理服务器的HTTP服务器实例也设置了足够的监听器数量
      if (proxy && proxy.server && proxy.server.setMaxListeners) {
        proxy.server.setMaxListeners(30);
      }
    }
  };
});

module.exports = proxyConfig;
