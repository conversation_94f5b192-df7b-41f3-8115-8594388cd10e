<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">审计日志</a></li>-->
<!--        <li><span>操作日志</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form [formGroup]="cloudAuditRule" nzLayout="cloudAuditRule" (ngSubmit)="search()" style="height: 32px;">
                <input type="text" autocomplete="off" formControlName="keyword"
                       nz-input placeholder="请输入事件描述、对象或对象ID" style="width: 220px; margin-right: 10px;" />
                <nz-range-picker [nzFormat]="dateFormat" formControlName="dateTime"  (ngModelChange)="onChange($event)"></nz-range-picker>
                <nz-select formControlName="type" nzShowSearch nzPlaceHolder="请选择类型" style="margin-left: 10px;width:150px">
                    <nz-option *ngFor="let item of initData.types_select" [nzValue]="item.key"
                               [nzLabel]="item.value">
                    </nz-option>
                </nz-select>
            </form>
            <div class="right-button-group">
                <div class="pull-right" >
                    <a nz-button>
                        <i nz-icon nzType="plus"
                           nzTheme="reload"></i>
                        重&nbsp;&nbsp;置
                    </a>
                </div>
                <div class="pull-right">
                    <a nz-button nzType="search">
                        查&nbsp;&nbsp;询
                    </a>
                </div>
            </div>
            <!--<p style="color:#999999">云审计服务为您提供云服务资源的操作记录，供您查询、审计和回溯使用。云审计服务不仅能够记录从页面控制台发起的资源操作请求还可以记录从开放API发起的请求，并且会记录每次请求资源操作的结果。</p>-->
        </div>
        <div class="on-panel-body">
            <div class="content-body-item">
                <div class="action-bar clearfix">
                    <span class="title">日志列表</span>
                </div>
            </div>
            <nz-table #monitors style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="cloudauditList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="500"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                            {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of monitors.data">
                        <td>{{ item.description }}</td>
                        <td>{{ item.type || '-'}}</td>
                        <td>{{ item.targetName || '-'}}</td>
                        <td>{{ item.ownerName}}</td>
                        <td>{{ item.createTm | date:'yyyy-MM-dd HH:mm:ss' || '-'}}</td>
                        <td>{{ item.status}}</td>
                        <!-- <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该自定义镜像规则吗？'"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteMonitor(item);">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                <a class="on-table-action-item"
                                    [routerLink]="['../detail', item.id]">
                                    <i nzTooltipTitle="共享"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        nz-icon
                                        nzType="cluster"
                                        nzTheme="outline"
                                        class="icon"></i>
                                </a>
                                </div>
                            </div>
                        </td> -->
                    </tr>
                    <tr [hidden]="monitors.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
