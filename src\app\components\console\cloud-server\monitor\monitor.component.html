<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">云服务器</a></li>-->
<!--        <li><span>监控</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入规则名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <button nz-button nzType="primary"
                            [ngClass]="{'disabled': isArchiveUser === 'true'}"
                            (click)="isArchiveUser === 'true' ? null : showCreateModal()">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建监控规则
                    </button>
                </div>
            </div>

        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">监控</span>
            </div>
            <nz-table #monitors style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="monitorList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="500"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                            {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of monitors.data; trackBy: trackById">
                        <td><a href="javascript:void(0);"
                            nzTooltipTitle="查看规则"
                            nzTooltipContent="bottom"
                            nz-tooltip
                            (click)="showMonitorRule(item);">{{ item.name }}</a></td>
                        <td>{{ item.vmId ? (vmExist(item.vmId) ? item.vmName : '云主机不存在或已删除') : '全部资源' }}</td>
                        <td>{{ item.alarmTm || '-'}}</td>
                        <td>{{ getNotificationType(item.notificationType) }}</td>
                        <td>
                            <div class="on-table-actions"  *ngIf="permission('update')"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                    (click)="isArchiveUser === 'true' ? null : showEditModal(item)">
                                    <i nzTooltipTitle="编辑"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item"  *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该监控规则吗？'"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteMonitor(item);">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="monitors.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 创建&编辑 -->
<nz-modal [nzWidth]="600" [(nzVisible)]="createModalVisible"
    [nzMaskClosable]="false"
    [nzTitle]="editMonitor.id ? '编辑监控规则' : '创建监控规则'"
    [nzOkText]="editMonitor.id ? '保存' : '创建'"
    [nzOkLoading]="isCreating"
    [nzBodyStyle]="{padding: '8px'}"
    (nzOnCancel)="hideCreateModal()"
    (nzOnOk)="editMonitor.id ? saveMonitorRule(editMonitor.id) : createMonitorRule()">
    <ng-container *nzModalContent>
    <form [formGroup]="monitorRule"
        class="config-content sm">
        <div class="field-group">
            <div class="field-item required select-group">
                <label>
                    <span class="label-text">监控规则</span>
                    <div>
                    <nz-select style="width: 120px"
                        nzShowSearch
                        (ngModelChange)="chooseMonitorRuleTypes($event)"
                        formControlName="ruleType"
                        nzPlaceHolder="请选择监控规则">
                        <nz-option
                            *ngFor="let item of initConfig.monitorRuleTypes"
                            [nzValue]="item.name"
                            [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                    <nz-select
                        style="width: 90px; margin-left: 10px;"
                        nzShowSearch
                        formControlName="monitorType"
                        nzPlaceHolder="请选择监控周期">
                        <nz-option
                            *ngFor="let item of initConfig.monitorTypes"
                            [nzValue]="item.name"
                            [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                    <nz-input-number formControlName="value1"
                        style="width: 70px; margin-left: 10px;"
                        *ngIf="monitorRule.value.condition === 'bt'"

                        [nzMin]="0" [nzMax]="monitorMaxSize"
                        [nzStep]="1"></nz-input-number>
                    <nz-select
                        style="width: 70px; margin-left: 10px;"
                        nzShowSearch
                        formControlName="condition"
                        nzPlaceHolder="请选择告警条件">
                        <nz-option
                            *ngFor="let item of initConfig.monitorConditionTypes"
                            [nzValue]="item.name"
                            [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                    <nz-input-number formControlName="value2"
                        style="width: 70px; margin-left: 10px;"
                        [nzMin]="0" [nzMax]="monitorMaxSize"
                        [nzStep]="1"></nz-input-number>
                    <span class="small tip" *ngIf="showUnit">%</span>
                    <span class="small tip" *ngIf="!showUnit">KB/S</span>
                    </div>
                    <div *ngIf="isInvalid(monitorRule.get('value1')) || isInvalid(monitorRule.get('value2'))"
                        class="form-hint error">
                        <div *ngIf="monitorRule.get('value1').hasError('required') || monitorRule.get('value2').hasError('required')">
                            请填写监控阈值
                        </div>
                        <div *ngIf="monitorRule.get('value1').hasError('pattern') || monitorRule.get('value2').hasError('pattern')">
                            请填写正确的监控阈值
                        </div>
                        <div *ngIf="monitorRule.get('value1').hasError('min') || monitorRule.get('value2').hasError('min')">
                            监控阈值不能低于0
                        </div>
                        <div *ngIf="monitorRule.get('value1').hasError('max') || monitorRule.get('value2').hasError('max')">
                            监控阈值不能高于100
                        </div>
                    </div>
                    <div class="form-hint error" *ngIf="(monitorRule.errors && monitorRule.errors.maxError)">
                        阈值上限必须大于阈值下限
                    </div>
                </label>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">产品</span>
                    <nz-select nzPlaceHolder="请选择产品类型"
                        nzShowSearch
                        [ngModelOptions]="{standalone: true}"
                        [(ngModel)]="productionType">
                        <nz-option
                            *ngFor="let item of productionTypeList"
                            [nzValue]="item.value"
                            [nzLabel]="item.label">
                        </nz-option>
                    </nz-select>
                </label>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">资源范围</span>
                    <nz-select [(ngModel)]="monitorTarget"
                        nzShowSearch
                        [ngModelOptions]="{standalone: true}"
                        (ngModelChange)="selectMonitorTarget($event)"
                        nzPlaceHolder="请选择资源范围">
                        <nz-option
                            *ngFor="let item of monitorRange"
                            [nzValue]="item.value"
                            [nzLabel]="item.label">
                        </nz-option>
                    </nz-select>
                </label>
            </div>
            <div class="field-item required" *ngIf="monitorTarget === 'vm'">
                <label>
                    <span class="label-text">实例</span>
                    <nz-select formControlName="vmId"
                        nzShowSearch
                        nzPlaceHolder="请选择云服务器实例">
                        <nz-option
                            *ngFor="let item of instanceList"
                            [nzValue]="item.id"
                            [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(monitorRule.get('vmId'))"
                    class="form-hint error">
                    <div
                        *ngIf="monitorRule.get('vmId').hasError('required')">
                        请选择监控实例
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">规则名称</span>
                    <input nz-input type="text"
                        formControlName="name"
                        maxlength="50" placeholder="请输入规则名称">
                </label>
                <div *ngIf="isInvalid(monitorRule.get('name'))"
                    class="form-hint error">
                    <div
                        *ngIf="monitorRule.get('name').hasError('required')">
                        规则名称不能为空
                    </div>
                    <div
                        *ngIf="monitorRule.get('name').hasError('maxlength')">
                        规则名称长度不能超过{{ monitorRule.get('name').errors.maxlength.requiredLength }}个字符
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">告警方式</span>
                    <nz-select formControlName="notificationType"
                        nzShowSearch
                        nzPlaceHolder="请选择告警方式">
                        <nz-option
                            *ngFor="let item of initConfig.notificationTypes"
                            [nzValue]="item.name"
                            [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                </label>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>

<!-- 详情 -->
<nz-modal
    [nzWidth]="600" [(nzVisible)]="detailModalVisible"
    nzTitle="监控规则详情" [nzCancelText]="null"
    (nzOnCancel)="detailModalVisible = false"
    (nzOnOk)="detailModalVisible = false">
    <ng-container *nzModalContent>
    <table class="monitor-info">
        <tbody>
            <tr>
                <td width="35%" class="th">监控规则</td>
                <td>{{ getMonitorRuleText(currentMonitorRule) }}</td>
            </tr>
            <tr>
                <td class="th">产品</td>
                <td>云服务器</td>
            </tr>
            <tr>
                <td class="th">资源范围</td>
                <td>{{ currentMonitorRule.vmId ? '实例' : '全部资源' }}</td>
            </tr>
            <tr *ngIf="currentMonitorRule.vmId">
                <td class="th">实例</td>
                <td>
                    {{ vmExist(currentMonitorRule.vmId) ? currentMonitorRule.vmName : '云主机不存在或已删除' }}
                </td>
            </tr>
            <tr>
                <td class="th">规则名称</td>
                <td>{{ currentMonitorRule.name }}</td>
            </tr>
            <tr>
                <td class="th">告警方式</td>
                <td>{{ initConfigMap.notificationTypes[currentMonitorRule.notificationType] }}</td>
            </tr>
        </tbody>
    </table>
    </ng-container>
</nz-modal>
