<div class="table-content">
  <ol class="on-breadcrumb">
    <li><a routerLink="/console/object-storage">对象存储</a></li>
    <li>{{ bucketItem.name ? bucketItem.name  : ''}}</li>
  </ol>
  <div class="on-panel">
    <div class="on-panel-header">

      <!-- 用量总览 -->
      <div class="on-desc" style="margin-top: 30px;">
        <div class="on-desc-body">
          <div nz-row class="on-desc-row" style="text-align: center;font-size: 13px;">
            <div nz-col nzSpan="12">
              <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">桶名称</div>
              <div class="on-desc-content">{{bucketItem.name}}</div>
            </div>
            <div nz-col nzSpan="4" style="position: absolute;right: 400px;">
              <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">使用量</div>
              <div class="on-desc-content">{{bucketItem.sizekbUtilized}}{{bucketItem.unit}}</div>
            </div>
            <div nz-col nzSpan="6" style="position: absolute;right: 40px;width: 240px">
              <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold;">创建时间</div>
              <div class="on-desc-content">
                {{bucketItem.createTime}}
              </div>
            </div>
          </div>
        </div>
      </div>



    </div>
    <div class="details-container">
      <!--内容菜单-->
      <div class="content-body-item" *ngIf="activeContentIndex === 0">
        <!--内容头部-->
        <div class="header">
          <p></p>
          <div class="back2" (click)="backFolder()" *ngIf="allFolderName.length > 0" style="position: absolute;margin-left: 20px;">
            <i nz-icon nzType="double-left" nzTheme="outline" style="margin-bottom: 2px"></i>
            返回上级目录 <span class="current-position">当前位置：{{ folderName }}</span>
          </div>
          <div class="pull-right">
            <button nz-button nzType="primary" (click)="showUploadFileWindow()">
              <i nz-icon nzType="fa-upload" nzTheme="outline"></i>
              上传文件
            </button>
            <button nz-button nzType="primary" (click)="showUloadFolderWindow()">
              <i nz-icon nzType="plus" nzTheme="outline"></i>
              新建文件夹
            </button>
          </div>

        </div>
      </div>

        <div class="operate">

        <nz-table #tableList [nzPageSize]=99999 #rowSelectionTable [nzShowPagination]=false [nzLoading]="tableLoading"
          [nzData]="fileList">
          <thead>
            <tr>
              <th>名称</th>
              <th>大小</th>
              <th>格式</th>
              <th>修改时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let files of tableList.data">
              <td (click)="clickFolder(files.id)" class="fixed-td" [ngClass]="{'folder-name': files.folder}">
                {{files.fileName}}
              </td>
              <td>{{files.size}}</td>
              <td>{{files.formats}}</td>
              <td>{{files.updateTime}}</td>
              <td>
                <div class="on-table-actions" [hidden]="busyStatus[files.fileName]">
                  <div class="on-table-action-item" [hidden]="files.folder"
                    (click)="download(files);">
                    <i nzTitle="下载" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-download"></i>
                  </div>
                  <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top" nzTitle="确定要删除该文件/文件夹吗？"
                    (nzOnConfirm)="deleteFile(files);">
                    <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o">
                    </i>
                  </div>
                </div>
                <div class="on-table-actions" [hidden]="!busyStatus[files.fileName]">
                  <div class="action-loading-placeholder">
                    <i class="icon" nz-icon [nzType]="'loading'"></i>
                    {{ getBusyText(files.fileName) }}
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </div>
  </div>
</div>
<!--上传文件弹出框-->
<nz-modal [(nzVisible)]="showUploadFile" nzTitle="上传文件" [nzOkLoading]="uploading" [nzCancelLoading]="uploading"
  (nzOnCancel)="showUploadFile = false" (nzOnOk)="handleUpload()">
  <ng-container *nzModalContent>
  <div class="tc-content">
    <div class="sele" style="height: 100%; padding-left: 30px">
      <div class="col-sele-2" style="margin-right: 5px">
        <label>上传文件</label>
      </div>
      <!--<nz-upload [nzDisabled]="uploading" [(nzFileList)]="uploadFileList" [nzRemove]="!uploading"
        [nzBeforeUpload]="beforeUpload">
        <button type="button" nz-button><i nz-icon nzType="upload"></i></button>
      </nz-upload>-->
      <button type="button" nz-button style="position: relative;"><i nz-icon nzType="upload"></i>
        <input   type="file" id="upload_file_id" class="upload-input" (change)="showFileName($event)" ng-model="fileName"/>
      </button>
      <div>{{fileName}}</div>
      <!--<nz-progress *ngIf="uploading" [nzStatus]="progressStatus" [nzPercent]="uploadProgress"></nz-progress>-->
      <p class="upload-tips" [ngClass]="{'upload-tips-error': uploadFileError}">
        *上传超过100MB的文件，请使用 S3 API 或 <a target="_blank" href='https://s3browser.com/'>S3 Browser</a></p>
    </div>
  </div>
  </ng-container>
</nz-modal>

<!-- 创建文件夹 -->
<nz-modal [(nzVisible)]="showUploadFolder" nzTitle="创建文件夹" [nzOkLoading]="uploading" [nzCancelLoading]="uploading"
  (nzOnCancel)="showUploadFolder = false" (nzOnOk)="createFolder()">
  <ng-container *nzModalContent>
  <div class="tc-content">
    <div class="sele">
      <div class="sele-label col-sele-2">
        <label>文件夹名：</label>
      </div>
      <div class="col-sele-11">
        <input nz-input placeholder="文件夹名" (blur)="checkUploadFolderName()" (keyup.enter)="createFolder()"
          [disabled]="uploading" [(ngModel)]="uploadFolderName">
      </div>
    </div>
    <ol class="folder-tips" [ngClass]="{'folder-tips-error': uploadFolderError}">
      <li class="upload-tips">只允许输入中文、大小写字母、. 、- </li>
      <li class="upload-tips">不能存在连续的 .</li>
      <li class="upload-tips">长度不能超过200个字符</li>
    </ol>
  </div>
  </ng-container>
</nz-modal>


