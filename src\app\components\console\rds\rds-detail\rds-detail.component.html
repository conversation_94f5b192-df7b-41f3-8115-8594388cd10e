<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">RDS</a></li>-->
<!--        <li><span>详情</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="details-container">
            <!--顶部菜单-->
            <div class="details-header">
                <div class="title-container">
                    <p>{{ basicInfor1[0].text }}</p>
                    <!-- <span>{{ netInfor[0].text }}</span>
                    <span class="title-des-separate">{{ netInfor[1].text }}</span> -->
                </div>
                <div>
                    <span class="operate-btn" (click)="openReboot()">
                        <i class="fa fa-play-circle-o"></i><span>重启</span>
                    </span>
                    <span class="operate-btn" (click)="deleteItem('rds', rdsId)">
                        <i class="fa fa-trash-o"></i><span>删除</span>
                    </span>
                </div>
            </div>
            <!--内容菜单-->
            <div class="details-menu">
                <nz-tabset [nzTabPosition]="'top'" [nzType]="'card'" (nzSelectChange)="selectMenu($event)"
                    [nzSelectedIndex]="activeContentIndex">
                    <nz-tab *ngFor="let tab of contentMenuOptions" [nzTitle]="tab.title">
                    </nz-tab>
                </nz-tabset>
            </div>
            <!--基本信息-->
            <div class="content-body-item" *ngIf="activeContentIndex === 0">
                <!--基本信息-->
                <section class="info-container">
                    <p>基本信息</p>
                    <div class="info-details-container">
                        <div class="info-details-item" *ngFor="let item of basicInfor1; index as i">
                            <p>{{ item.title }}</p>
                            <p class="info-text" (click)="item.change === true && showChangeBox('name')">
                                {{ item.text }}
                                <span class="fa fa-pencil" *ngIf="item.change === true"></span>
                            </p>
                        </div>
                    </div>
                    <!-- <div class="info-details-container">
                        <div class="info-details-item" *ngFor="let item of basicInfor2; index as i">
                            <p>{{ item.title }}</p>
                            <p class="info-text" (click)="item.change === true && showChangeBox('character')">
                                {{ item.text }}
                                <span class="fa fa-pencil" *ngIf="item.change === true"></span>
                            </p>
                        </div>
                    </div> -->
                </section>
                <!--配置信息-->
                <section class="info-container">
                    <p>配置信息</p>
                    <div class="info-details-container">
                        <span class="info-details-item" *ngFor="let item of configInfor; index as i">
                            <p>{{ item.title }}</p>
                            <p class="info-text">{{ item.text }}</p>
                        </span>
                    </div>
                </section>
                <!--网络信息-->
                <section class="info-container">
                    <p>网络信息</p>
                    <div class="info-details-container">
                        <span class="info-details-item" *ngFor="let item of netInfor; index as i">
                            <p>{{ item.title }}</p>
                            <p class="info-text">{{ item.text }}</p>
                        </span>
                    </div>
                </section>
            </div>
            <!--账号管理-->
            <div class="content-body-item" *ngIf="activeContentIndex === 2 && !ifShowAddAccount">
                <!--内容头部-->
                <div class="header">
                    <p>账号管理</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="showAddAccount()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            创建账号
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #accountList [nzBordered]=true [nzPageSize]=99999 [nzShowPagination]=false
                    [nzData]="accountListData">
                    <thead>
                        <tr>
                            <th>账号名称</th>
                            <th>账号类型</th>
                            <th>账号状态</th>
                            <th>所属数据库</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of accountList.data">
                            <td>{{ item.accountName }}</td>
                            <td>{{item.type === '0' ? '管理员账号' : '普通账号'}}</td>
                            <td>
                                <span
                                    class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status) }}</span>
                            </td>
                            <td>{{ item.dbNames ? item.dbNames : '-' }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item"
                                        (click)="showChangeBox('changeJurisdiction', item.id, item);"
                                        [ngClass]="{'disabled': !accountCanChange(item)}">
                                        <i nzTitle="修改权限" nzPlacement="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" (click)="showChangeBox('resetpass', item.id);">
                                        <i nzTitle="重置密码" nzPlacement="bottom" nz-tooltip class="icon fa fa-lock"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzPlacement="top"
                                        nzTitle="确定要删除该账号吗？" (nzOnConfirm)="deleteItem('account', item.id, item)">
                                        <i nzTitle="删除" nzPlacement="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!--添加账号-->
            <div class="content-body-item" *ngIf="ifShowAddAccount">
                <!--内容头部-->
                <div class="content-body-item-header">
                    <p class="content-title">创建账号</p>
                </div>
                <!--账号类型-->
                <section class="form-item">
                    <p class="form-label">账号类型</p>
                    <button type="button" *ngFor="let item of accountTypeOptions; index as i"
                        (click)="chooseAccountType(item.value)" nz-button class="choose-btn"
                        [ngClass]="{'action-btn': accountTypeValue == item.value}">{{ item.text }}
                    </button>
                </section>
                <!--账户名称-->
                <section class="form-item">
                    <p class="form-label">账户名称</p>
                    <p class="form-label-tips" [ngStyle]="{'color':nameCheck ? '#666': '#E01B2F'} ">
                        可包含数字、大小写字母、“.”、“_”或“-”，长度不超过16个字符</p>
                    <input
                        nz-input
                        style="width: 360px;"
                        type="text"
                        placeholder="请输入名称"
                        class="form-control"
                        [(ngModel)]="nameValue"
                        (blur)="checkName()">
                </section>
                <!--账户密码-->
                <section class="form-item">
                    <p class="form-label">帐户密码</p>
                    <p class="form-label-tips" [ngStyle]="{'color':passCheck ? '#666': '#E01B2F'} ">
                        由8-16位的字符组成，至少包含1个小写字母、大写字母、数字
                    </p>
                    <input nz-input
                        style="width: 360px;"
                        type="password"
                        placeholder="请输入密码"
                        class="form-control"
                        [(ngModel)]="passValue"
                        (blur)="checkPass()">
                </section>
                <!--授权数据库-->
                <section class="form-item" *ngIf="accountTypeValue == '1'">
                    <p class="form-label">授权数据库</p>
                    <div class="database-accredit-container">
                        <div class="larger-container">
                            <div class="database-accredit-header">
                                <span class="header-l1">已授权的数据库</span>
                                <select class="database-accredit-select" id="database-accredit-select"
                                    [(ngModel)]="selectedAddAccreditType" (change)="selectAccredit()">
                                    <option value='' disabled selected style='display:none;'>批量设置权限</option>
                                    <option value=''>不进行批量操作</option>
                                    <option value="readAndWrite" class="database-accredit-option">全部设为读写
                                    </option>
                                    <option value="readOnly" class="database-accredit-option">全部设为只读</option>
                                </select>
                            </div>
                            <div class="database-accredit-body">
                                <ul class="database-accredit-body-list">
                                    <li class="database-accredit-body-list-item"
                                        *ngFor="let item of selectDatabaseList; index as i">
                                        <span class="database-accredit-body-list-item-title">{{ item.dbName }}</span>
                                        <div class="database-accredit-body-list-item-operation-container">
                                            <label class="database-radio">
                                                <input [name]=nameBefore+item.id [checked]="readAndWriteSelected"
                                                    type="radio" value="0" />读写
                                            </label>
                                            <label class="database-radio">
                                                <input [name]=nameBefore+item.id [checked]="readOnlySelected"
                                                    type="radio" value="1" />只读
                                            </label>
                                            <span class="fa fa-times-circle-o" (click)="unselect(item, i)"></span>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="smaller-container">
                            <div class="database-accredit-header">
                                <span class="header-l1">未授权的数据库</span>
                            </div>
                            <div class="database-accredit-body">
                                <ul class="database-accredit-body-list">
                                    <li class="database-accredit-body-list-item database-accredit-body-list-item-hover"
                                        *ngFor="let item of unselectDatabaseList; index as i" (click)="select(item, i)">
                                        <span class="database-accredit-body-list-item-title">{{ item.dbName }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>
                <div class="action-bar">
                    <button nz-button nzType="primary" (click)="submitAddAccount()">确认</button>
                    <button nz-button (click)="ifShowAddAccount = false">取消</button>
                </div>
                <span class="warning-text" *ngIf="showNullWarning">*信息输入不完整！</span>
                <span class="warning-text" *ngIf="showNameWarning">*信息输入不合法！</span>
            </div>
            <!--数据库管理-->
            <div class="content-body-item" *ngIf="activeContentIndex === 3">
                <!--内容头部-->
                <div class="header">
                    <p>数据库管理</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="showAddDatabase()">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            创建数据库
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #databaseList [nzBordered]=true [nzPageSize]=99999 [nzShowPagination]=false
                    [nzData]="databaseListData">
                    <thead>
                        <tr>
                            <th>数据库名称</th>
                            <!-- <th>字符集</th> -->
                            <!-- <th>数据库状态</th> -->
                            <th>绑定账号</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of databaseList.data">
                            <td>{{ item.dbName }}</td>
                            <td>{{ item.characterSet }}</td>
                            <!-- <td>
                                <span
                                    class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status) }}</span>
                            </td> -->
                            <td>{{ item.accountNames ? item.accountNames : '-' }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" nz-popconfirm nzPlacement="top"
                                        nzTitle="确定要删除该数据库吗？" (nzOnConfirm)="deleteItem('database', item.id, item)">
                                        <i nzTitle="删除" nzPlacement="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!--数据备份-->
            <div class="content-body-item" *ngIf="activeContentIndex === 4">
                <div class="header">
                    <p>数据备份</p>
                </div>
                <!--内容-->
                <nz-table #backupList [nzBordered]=true [nzPageSize]=99999 [nzLoading]="backupLoading"
                    [nzShowPagination]=false [nzData]="backupListData">
                    <thead>
                        <tr>
                            <th>备份名称</th>
                            <th>类型</th>
                            <th>备份方式</th>
                            <th>备份大小</th>
                            <th>备份时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of backupList.data">
                            <td>{{ item.name }}</td>
                            <td>{{ item.backType }}</td>
                            <td>{{ item.type }}</td>
                            <td>{{ item.size }}</td>
                            <td>{{ item.createTime }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions">
                                    <a class="on-table-action-item"
                                        [href]="URL + '/rds/' + this.rdsId + '/backup/' + item.name + '/download/?token=' + token">
                                        <i nzTitle="下载" nzPlacement="bottom" nz-tooltip class="icon fa fa-download"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
            <!--日志-->
            <div class="content-body-item" *ngIf="activeContentIndex === 5">
                <div class="header">
                    <p>日志</p>
                </div>
                <!--内容-->
                <nz-table #logList [nzBordered]=true [nzLoading]="logLoading" [nzPageSize]=99999
                    [nzShowPagination]=false [nzData]="logListData">
                    <thead>
                        <tr>
                            <th>备份名称</th>
                            <th>类型</th>
                            <th>备份方式</th>
                            <th>备份大小</th>
                            <th>备份时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of logList.data">
                            <td>{{ item.name }}</td>
                            <td>{{ item.backType }}</td>
                            <td>{{ item.type }}</td>
                            <td>{{ item.size }}</td>
                            <td>{{ item.createTime }}</td>
                            <!--更多操作-->
                            <td>
                                <div class="on-table-actions">
                                    <a class="on-table-action-item"
                                        [href]="URL + '/rds/' + this.rdsId + '/log/' + item.name + '/download/?token=' + token">
                                        <i nzTitle="下载" nzPlacement="bottom" nz-tooltip class="icon fa fa-download"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </nz-table>
            </div>
        </div>
    </div>
</div>
<!--修改名称弹出层-->
<nz-modal [(nzVisible)]="showChangeNameWindow" nzTitle="修改RDS名称" (nzOnCancel)="showChangeNameWindow = false"
    (nzOnOk)="submitChangeName()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">RDS名称：</span>
        <input nz-input style="width: 350px;" (blur)="checkRDSName()" (keyup.enter)="submitChangeName()"
            [(ngModel)]="rdsNameValueChange" />
        <p class="warning-tips" *ngIf="!rdsNameCheck">*可包含数字、大小写字母、中文、“.”、“_”或“-”，长度不超过16个字符</p>
    </div>
    </ng-container>
</nz-modal>
<!--修改字符集弹出层-->
<nz-modal [(nzVisible)]="showChangeCharacterWindow" nzTitle="修改RDS字符集" (nzOnCancel)="showChangeCharacterWindow = false"
    (nzOnOk)="submitChangeCharacter()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">RDS字符集：</span>
        <nz-select style="width: 350px;" [(ngModel)]="selectCharacterValue">
            <nz-option *ngFor="let option of characterOptions" [nzValue]="option" [nzLabel]="option">
            </nz-option>
        </nz-select>
    </div>
    </ng-container>
</nz-modal>
<!--重置密码弹出层-->
<nz-modal [(nzVisible)]="showResetPassWindow" nzTitle="重置密码" (nzOnCancel)="showResetPassWindow = false"
    (nzOnOk)="submitResetPass()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">新密码：</span>
        <nz-input-group [nzSuffix]="suffixTemplate" style="width: 350px">
            <input [type]="resetPasswordVisible ? 'text' : 'password'" nz-input placeholder="请输入新密码"
                (input)="checkResetPass()" (keyup.enter)="submitResetPass()" [(ngModel)]="resetPassValue" />
        </nz-input-group>
        <ng-template #suffixTemplate>
            <i nz-icon style="cursor: pointer" [nzType]="resetPasswordVisible ? 'eye-invisible' : 'eye'"
                (click)="resetPasswordVisible = !resetPasswordVisible"></i>
        </ng-template>
        <p class="warning-tips" *ngIf="!resetPassCheck">*由8-16位的字符组成，至少包含1个小写字母、大写字母、数字</p>
    </div>
    </ng-container>
</nz-modal>
<!--修改权限弹出层-->
<div class="popup-window" id="changeJurisdictionWindow" (click)="closeChangeWindow($event)">
    <div class="popup-container-window">
        <div class="tc-title-box">
            <div class="tc-title">授权数据库</div>
            <img class="cd-popup-close-middle" (click)="closeChangeWindowIcon()" src="./assets/images/icon-close.png" />
        </div>
        <!--授权数据库-->
        <section class="form-item">
            <div class="database-accredit-container">
                <div class="larger-container">
                    <div class="database-accredit-header">
                        <span class="header-l1">已授权的数据库</span>
                        <select class="database-accredit-select" id="change-database-accredit-select"
                            [(ngModel)]="selectedChangeAccreditType" (change)="selectAccredit()">
                            <option value='' disabled selected style='display:none;'>批量设置权限</option>
                            <option value=''>不进行批量操作</option>
                            <option value="readAndWrite" class="database-accredit-option">全部设为读写</option>
                            <option value="readOnly" class="database-accredit-option">全部设为只读</option>
                        </select>
                    </div>
                    <div class="database-accredit-body">
                        <ul class="database-accredit-body-list">
                            <li class="database-accredit-body-list-item"
                                *ngFor="let item of selectDatabaseList; index as i">
                                <span class="database-accredit-body-list-item-title">{{ item.dbName }}</span>
                                <div class="database-accredit-body-list-item-operation-container">
                                    <label class="database-radio">
                                        <input [name]=item.id [checked]="item.authorityType === 0"
                                            (change)="changeAccreditType(item, 0)" type="radio" value="0" />读写
                                    </label>
                                    <label class="database-radio">
                                        <input [name]=item.id [checked]="item.authorityType === 1"
                                            (change)="changeAccreditType(item, 1)" type="radio" value="1" />只读
                                    </label>
                                    <span class="fa fa-times-circle-o" (click)="unselect(item, i)"></span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="smaller-container">
                    <div class="database-accredit-header">
                        <span class="header-l1">未授权的数据库</span>
                    </div>
                    <div class="database-accredit-body">
                        <ul class="database-accredit-body-list">
                            <li class="database-accredit-body-list-item database-accredit-body-list-item-hover"
                                *ngFor="let item of unselectDatabaseList; index as i" (click)="select(item, i)">
                                <span class="database-accredit-body-list-item-title">{{ item.dbName }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
        <div class="btn-container">
            <button class="btn btn-primary" (click)="submitChangeDatabase()">确认</button>
            <button class="btn btn-cancel" (click)="closeChangeWindowIcon()">取消</button>
        </div>
    </div>
</div>
<!-- 创建数据库 -->
<nz-modal [(nzVisible)]="ifShowAddDatabase" nzTitle="创建数据库" (nzOnCancel)="ifShowAddDatabase = false"
    [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading" (nzOnOk)="submitAddDatabase()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">数据库名称：</span>
        <input nz-input style="width: 300px;" (blur)="checkDatabaseName()" [(ngModel)]="databaseNameValue" />
        <p class="warning-tips" *ngIf="!databaseNameCheck">*可包含数字、大小写字母、“.”、“_”或“-”，长度不超过16个字符</p>
    </div>
    <div class="select-container">
        <span class="select-tips">字符集：</span>
        <nz-select style="width: 300px;" [(ngModel)]="addDatabaseCharacter">
            <nz-option *ngFor="let option of characterOptions" [nzValue]="option" [nzLabel]="option">
            </nz-option>
        </nz-select>
    </div>
    </ng-container>
</nz-modal>