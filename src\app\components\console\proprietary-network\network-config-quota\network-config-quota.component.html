<div class="rds-config config-content">
    <h4 class="title">
        创建专有网络
        <a routerLink=".." class="back"><i class="icon" nz-icon nzType="left" nzTheme="outline"></i>返回列表</a>
    </h4>

    <div class="panel">
        <div class="panel-body">
            <form [formGroup]="network" class="config-content md network-form">
                <section class="field-section">
                    <div class="field-title">
                        选择订单
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label for="">
                                <span class="label-text">合同名称</span>
                                <nz-select formControlName="Quota" nzShowSearch nzPlaceHolder="请选择合同名称"
                                    (ngModelChange)="selectQuota($event)">
                                    <nz-option *ngFor="let item of QuotaList" [nzValue]="item" [nzLabel]="item.name +'(' + item.code + ')'" nzCustomContent>
                                        <span [title]="item.name +'(' + item.code + ')'">{{item.name +'(' + item.code + ')'}}</span>
                                    </nz-option>
                                    <!-- <nz-option *ngIf="QuotaList" [nzValue]="QuotaList" [nzLabel]="QuotaList.name +'(' + QuotaList.code + ')'">
                                    </nz-option> -->
                                </nz-select>
                            </label>
                        </div>
                        <div class="field-item required" *ngIf="network.value.quotaId">
                            <label for="">
                                <span class="label-text">订单项ID</span>
                                <nz-select formControlName="QuotaDetail" nzShowSearch nzPlaceHolder="请选择订单项ID"
                                    (ngModelChange)="selectQuotaDetail($event)">
                                    <nz-option *ngFor="let item of QuotaDetailList" [nzValue]="item"
                                        [nzLabel]="item.name +'(' + item.subCode + ')'" nzCustomContent>
                                        <span [title]="item.name +'(' + item.subCode + ')'">{{item.name +'(' + item.subCode + ')'}}</span>
                                    </nz-option>
                                </nz-select>
                            </label>
                        </div>
                    </div>
                </section>
                <section class="field-section" [hidden]="!(type === 'vpc')">
                    <div class="field-title">
                        基本信息
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">专有网络名称</span>
                                <input nz-input type="text" formControlName="name" maxlength="50"
                                    placeholder="请输入专有网络名称" [disabled]="isInitDisable" [readonly]="isInitDisable">
                            </label>
                            <div *ngIf="isInvalid(network.get('name')) && !isInitDisable" class="form-hint error">
                                <div *ngIf="network.get('name').hasError('required') && !isInitDisable">
                                    专有网络名称不能为空
                                </div>
                                <div *ngIf="network.get('name').hasError('pattern')">
                                    专有网络名称必须以字母开头，只能输入英文、数字、中划线
                                </div>
                                <div *ngIf="network.get('name').hasError('maxlength')">
                                    专有网络名称长度不能超过{{ network.get('name').errors.maxlength.requiredLength }}个字符
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="field-section" formArrayName="ovdcNetworkList">
                    <div class="field-title">
                        子网配置
                    </div>
                    <div class="field-group" [formGroupName]="0">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">子网名称</span>
                                <input nz-input type="text" formControlName="name" maxlength="50" placeholder="请输入子网名称" [disabled]="isInitDisable" [readonly]="isInitDisable">
                            </label>
                            <div *ngIf="isInvalid(network.get('ovdcNetworkList').get('0').get('name')) && !isInitDisable"
                                class="form-hint error">
                                <div *ngIf="network.get('ovdcNetworkList').get('0').get('name').hasError('required') && !isInitDisable">
                                    子网名称不能为空
                                </div>
                                <div *ngIf="network.get('ovdcNetworkList').get('0').get('name').hasError('pattern')">
                                    子网名称必须以字母开头，只能输入英文、数字、中划线
                                </div>
                                <div *ngIf="network.get('ovdcNetworkList').get('0').get('name').hasError('maxlength')">
                                    子网名称长度不能超过{{ network.get('name').errors.maxlength.requiredLength }}个字符
                                </div>
                            </div>
                        </div>
                        <div formArrayName="ipScopeList">
                            <div>
                                <div class="field-item required field-item-filled" [formGroupName]="0">
                                    <label for="">
                                        <span class="label-text">子网网段</span>
                                        <div class="input-filled">
                                            <nz-radio-group name="radioName" (ngModelChange)="radioChange($event)"
                                                formControlName="gateway">
                                                <div *ngFor="let item of allItem; let id = index">
                                                    <label nz-radio nzValue="{{id}}" style="margin-right: 8px;"></label>
                                                    <input nz-input placeholder="10" class="ip-item" type="number"
                                                        [disabled]="!item.editable1 || isInitDisable" [(ngModel)]="item.value1"
                                                        [ngModelOptions]="{standalone: true}" />
                                                    <span class="divider">.</span>
                                                    <input nz-input placeholder="0" class="ip-item" type="number"
                                                        [nzTooltipTitle]="item.range2" nzTooltipContent="top"
                                                        nzTooltipTrigger="focus" nz-tooltip [disabled]="!item.editable2 || isInitDisable"
                                                        [(ngModel)]="item.value2"
                                                        (ngModelChange)="validateSubNetwork(id)"
                                                        [ngModelOptions]="{standalone: true}" />
                                                    <span class="divider">.</span>
                                                    <input nz-input placeholder="0" class="ip-item" type="number"
                                                        [nzTooltipTitle]="item.range3" nzTooltipContent="top"
                                                        nzTooltipTrigger="focus" nz-tooltip [disabled]="!item.editable3 || isInitDisable"
                                                        [(ngModel)]="item.value3"
                                                        (ngModelChange)="validateSubNetwork(id)"
                                                        [ngModelOptions]="{standalone: true}" />
                                                    <span class="divider">.</span>
                                                    <input nz-input placeholder="0" class="ip-item" type="number"
                                                        [disabled]="!item.editable4 || isInitDisable" [(ngModel)]="item.value4"
                                                        [ngModelOptions]="{standalone: true}" />
                                                    <span class="divider">/</span>
                                                    <nz-select style="width: 60px" [(ngModel)]="item.maskBit"
                                                        [ngModelOptions]="{standalone: true}"
                                                        (ngModelChange)="maskBitChange($event, id)"
                                                        nzPlaceHolder="请选择子网掩码位数" [nzDisabled]="isInitDisable">
                                                        <nz-option *ngFor="let item2 of item.maskBitList"
                                                            [nzValue]="item2" [nzLabel]="item2">
                                                        </nz-option>
                                                    </nz-select>
                                                    <div class="form-hint error"
                                                        *ngIf="subNetworkError && radioID == id.toLocaleString()"
                                                        style="padding-left: 0px;">
                                                        {{ subNetworkError }}
                                                    </div>
                                                </div>
                                            </nz-radio-group>
                                        </div>

                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="field-section action" >
                    <button
                        [nzLoading]="isCreating"
                        [disabled] = "isInitDisable"
                        type="submit"
                        nz-button
                        nzSize="large"
                        nzType="primary" 
                        (nzOnCancel)="handleCancel()"
                        (click)="createVpc()">创建</button>
                </section>
            </form>
        </div>
        <div class="panel-aside pined">
            <section class="field-section">
                <div class="field-title">
                    配置概要
                </div>
                <table class="form-info" *ngIf="network">
                    <tbody>

                        <tr>
                            <td width="25%">合同名称</td>
                            <td>{{ network.value.quotaName || '-'}}</td>
                        </tr>
                        <tr>
                            <td>订单项ID</td>
                            <td>{{ network.value.quotaDetailName || '-'}}</td>
                        </tr>
                        <tr>
                            <td>专有网络名称</td>
                            <td>{{ network.value.name || '-'}}
                            </td>
                        </tr>
                        <tr>
                            <td>子网名称</td>
                            <td>{{ network.value.ovdcNetworkList[0].name || '-'}}
                            </td>
                        </tr>
                        <tr>
                            <td>子网网段</td>
                            <td>{{ allItem[radioID].value1 + "." + allItem[radioID].value2 + "." + allItem[radioID].value3 + "." + allItem[radioID].value4 + "/" +  allItem[radioID].maskBit || '-'}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </div>
    </div>
</div>