<div class="ring-chart-container" [style.height.px]="height">
<!--  <div class="chart-title" *ngIf="title">{{ title }}</div>-->
  <div class="chart-content">
    <div #chartContainer class="chart-area" [style.height.px]="height"></div>
    <div class="legend-area">
      <!-- 当所有值都为0时显示禁用状态 -->
<!--      <div class="legend-item disabled-legend" *ngIf="totalValue === 0">-->
<!--        <div class="color-indicator" style="background-color: #CCCCCC"></div>-->
<!--        <div class="legend-text">-->
<!--          <span class="legend-key">禁用</span>-->
<!--        </div>-->
<!--      </div>-->
      <!-- 正常状态下显示数据图例 -->
      <div class="legend-item" *ngFor="let item of data" [class.disabled-text]="totalValue === 0">
        <div class="color-indicator" [style.background-color]="totalValue === 0 ? '#EEEEEE' : item.color"></div>
        <div class="legend-text">
          <span class="legend-key">{{ item.key }}：{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
