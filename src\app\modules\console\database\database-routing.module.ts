import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RedisComponent } from 'src/app/components/console/redis/index/redis.component';
import { RedisConfigComponent } from 'src/app/components/console/redis/redis-config/redis-config.component';
import { RdsComponent } from 'src/app/components/console/rds/index/rds.component';
import { RdsConfigComponent } from 'src/app/components/console/rds/rds-config/rds-config.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'redis',
        pathMatch: 'full',
    },
    {
        path: 'redis',
        component: RedisComponent
    },
    {
        path: 'redis-config',
        component: RedisConfigComponent
    },
    {
        path: 'rds',
        component: RdsComponent
    },
    {
        path: 'rds-config',
        component: RdsConfigComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class DatabaseRoutingModule {}
