import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { MessageService } from 'src/app/service/console/utils/message.service';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { CloudServerService } from 'src/app/service/console/cloud-server/cloud-server.service';

@Component({
    selector: 'app-vdi-config',
    templateUrl: './vdi-config.component.html',
    styleUrls: ['./vdi-config.component.less']
})
export class VdiConfigComponent implements OnInit {
    
    vdiForm: FormGroup;
    isCreating: boolean = false;
    formSubmitAttempt: boolean = false;
    
    // 初始化数据
    initData = {
        images: [],
        servicePlans: []
    };
    
    // 镜像列表（写死）
    imageList = [];
    
    // 服务计划列表（写死）
    servicePlanList = [
        { id: 1000, cpu: 4, memory: 8, name: "4核16G", imageIndex:[1,2,3,4]},
        { id: 1001, cpu: 4, memory: 16, name: "8核16G", imageIndex:[1,3,4,6,7,8]},
        { id: 1002, cpu: 8, memory: 16, name: "8核16G", imageIndex:[3,4,6]},
        { id: 1003, cpu: 8, memory: 32, name: "8核32G", imageIndex:[1,2,6,9]},
        { id: 1004, cpu: 8, memory: 64, name: "16核32G", imageIndex:[4,7,8,9,10]},
        { id: 1005, cpu: 16, memory: 32, name: "16核64G", imageIndex:[2,5,6,7,10]},
        { id: 1006, cpu: 16, memory: 64, name: "32核64G", imageIndex:[1,4,7,8]},
        { id: 1007, cpu: 32, memory: 64, name: "32核128G", imageIndex:[1,2,3,5,9]},
        { id: 1008, cpu: 32, memory: 128, name: "32核128G", imageIndex:[2,4,5,9,10]},
        { id: 1009, cpu: 64, memory: 128, name: "32核128G", imageIndex:[2,5,7,8,9]},
    ];
    
    // 当前选中的服务计划
    selectedServicePlan = null;
    
    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private fb: FormBuilder,
        private msg: MessageService,
        private http: HttpClient,
        private cloudServerService: CloudServerService
    ) {}
    
    ngOnInit() {
        this.loadImageList();
        this.initForm();
        this.getInitData();
    }

    loadImageList(): Promise<any> {
        // 如果已经在加载中或已加载，直接返回
        if (this.imageList.length > 0) {
            return Promise.resolve({ success: true });
        }

        const params = {
            bean: {
                description: 'vdi'
            }
        };
        return this.cloudServerService.getImage(params)
            .then(rs => {
                if (rs.success) {
                    const images = rs.data || [];
                    // 取前10个镜像并添加index
                    this.imageList = images.slice(0, 10).map((img, index) => ({
                        ...img,
                        index: index + 1
                    }));
                    console.log('镜像列表：', this.imageList);
                }
                return rs;
            })
            .catch(err => {
                console.error('加载镜像列表失败', err);
                return { success: false };
            });
    }
    
    initForm() {
        this.vdiForm = this.fb.group({
            name: ['', {
                validators: [
                    Validators.required,
                    Validators.maxLength(50),
                    Validators.pattern(/^[a-zA-Z][a-zA-Z\d-]*$/)
                ]
            }],
            imageId: [null, {
                validators: [
                    Validators.required
                ]
            }],
            servicePlanId: [null, {
                validators: [
                    Validators.required
                ]
            }],
            disk: [null, {
                validators: [
                    Validators.min(1),
                    Validators.max(1000)
                ]
            }],
        });

        // 监听镜像选择变化
        this.vdiForm.get('imageId')?.valueChanges.subscribe(imageId => {
            this.onImageChange(imageId);
        });
    }
    
    getInitData() {
        // 设置写死的数据
        this.initData.images = this.imageList;
        this.initData.servicePlans = this.servicePlanList;
    }
      // 镜像选择变化处理
    onImageChange(imageId: number) {
        if (!imageId) {
            // 如果没有选择镜像，清空服务计划选择
            this.vdiForm.patchValue({
                servicePlanId: null
            });
            this.selectedServicePlan = null;
            return;
        }

        // 检查当前选中的服务计划是否仍然可用
        const currentServicePlanId = this.vdiForm.get('servicePlanId')?.value;
        if (currentServicePlanId && !this.isServicePlanAvailable(currentServicePlanId, imageId)) {
            // 如果当前选中的服务计划不可用，清空选择
            this.vdiForm.patchValue({
                servicePlanId: null
            });
            this.selectedServicePlan = null;
        }
    }

    // 检查服务计划是否可用
    isServicePlanAvailable(servicePlanId: number, imageId?: number): boolean {
        if (!imageId) {
            imageId = this.vdiForm.get('imageId')?.value;
        }

        if (!imageId) {
            return false; // 没有选择镜像时，所有配置都不可用
        }

        const servicePlan = this.servicePlanList.find(plan => plan.id === servicePlanId);
        if (!servicePlan || !servicePlan.imageIndex) {
            return false;
        }

        const selectedImage = this.imageList.find(img => img.id === imageId);
        if (!selectedImage) {
            return false;
        }

        return servicePlan.imageIndex.includes(selectedImage.index);
    }

    // 选择服务计划
    selectServicePlan(servicePlanId) {
        // 检查是否可选
        if (!this.isServicePlanAvailable(servicePlanId)) {
            return; // 不可用的配置不能选择
        }

        this.selectedServicePlan = this.servicePlanList.find(plan => plan.id === servicePlanId);
        // 更新表单值
        this.vdiForm.patchValue({
            servicePlanId: servicePlanId
        });
    }
    
    // 获取选中的镜像名称
    getSelectedImageName(): string {
        const imageId = this.vdiForm.get('imageId')?.value;
        const image = this.imageList.find(img => img.id === imageId);
        return image ? image.name : '';
    }
    
    // 获取选中的服务计划名称
    getSelectedServicePlanName(): string {
        const servicePlanId = this.vdiForm.get('servicePlanId')?.value;
        const plan = this.servicePlanList.find(p => p.id === servicePlanId);
        return plan ? plan.name : '';
    }

    // 表单验证
    isInvalid(fc: AbstractControl): boolean {
        return (!fc.valid && fc.touched) || (fc.untouched && this.formSubmitAttempt);
    }
    
    // 创建桌面虚拟机
    createVdi() {
        if (this.vdiForm.invalid) {
            this.formSubmitAttempt = true;
            this.msg.error('请检查表单填写是否正确');
            return;
        }
        
        this.formSubmitAttempt = false;
        this.isCreating = true;
        
        // 构建提交数据
        const formValue = this.vdiForm.value;
        console.log('表单数据:', formValue)
        const submitData = {
            name: formValue.name,
            imageId: formValue.imageId,
            servicePlanId: formValue.servicePlanId,
            cpu: this.selectedServicePlan?.cpu || 0,
            memory: this.selectedServicePlan?.memory || 0,
            disk: formValue.disk || null
        };
        
        console.log('提交数据:', submitData);
        
        // 调用实际的API
        this.http.post('/cloud/api/aic/order/vdi/save', submitData)
        .toPromise()
        .then((rs: any) => {
            if (rs.success) {
                this.msg.success('桌面虚拟机创建成功');
                this.router.navigate(['/console/vdi']);
            } else {
                this.msg.error(`创建失败: ${rs.message || '未知错误'}`);
            }
            this.isCreating = false;
        })
        .catch(err => {
            console.error('创建VDI失败:', err);
            this.msg.error('创建失败，请稍后重试');
            this.isCreating = false;
        });
    }
    
    // 取消创建
    cancel() {
        this.router.navigate(['/console/vdi']);
    }
    
    // 返回列表
    goBack() {
        this.router.navigate(['/console/vdi']);
    }
}
