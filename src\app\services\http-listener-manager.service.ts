import { Injectable, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, Subscription } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HttpListenerManagerService implements OnDestroy {
  private subscriptions: Subscription[] = [];

  constructor(private http: HttpClient) {}

  /**
   * 执行 HTTP 请求并自动管理订阅
   * @param request Observable HTTP 请求
   * @returns 与输入相同的 Observable
   */
  public manageRequest<T>(request: Observable<T>): Observable<T> {
    return request;
  }

  /**
   * 添加订阅到管理列表
   * @param subscription 要管理的订阅
   */
  public addSubscription(subscription: Subscription): void {
    this.subscriptions.push(subscription);
  }

  /**
   * 取消特定订阅
   * @param subscription 要取消的订阅
   */
  public removeSubscription(subscription: Subscription): void {
    const index = this.subscriptions.indexOf(subscription);
    if (index > -1) {
      subscription.unsubscribe();
      this.subscriptions.splice(index, 1);
    }
  }

  /**
   * 取消所有订阅
   */
  public clearSubscriptions(): void {
    this.subscriptions.forEach(subscription => {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
  }

  ngOnDestroy(): void {
    this.clearSubscriptions();
  }
}
