<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">容器服务</a></li>-->
<!--        <li><span>集群管理</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入模型名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
<!--                <div *ngIf="permission('refresh')">-->
<!--                    <a nz-button *ngIf="permission('refresh')" disabled-->
<!--                       [ngClass]="{'disabled': resLimit}"-->
<!--                       (click)="refresh()">-->
<!--                        刷&nbsp;&nbsp;新-->
<!--                    </a>-->
<!--                </div>-->
                <div *ngIf="permission('create')">
                    <a nz-button disabled
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="resLimit ? null : '../k8s-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建模型
                    </a>
                </div>
            </div>

        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    模型管理
                </span>
            </div>
            <nz-table #tableList [nzLoading]="isLoading"  style="overflow:hidden;overflow-x: auto"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)" [nzScroll]="{ x: '1450px' }"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <th style="width: 25%">模型名称</th>
                        <th style="width: 5%">版本</th>
                        <th style="width: 10%">框架</th>
                        <th style="width: 10%">类型</th>
                        <th style="width: 10%">模型来源</th>
                        <th style="width: 10%">状态</th>
                        <th style="width: 5%">创建人</th>
                        <th style="width: 15%">更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.modelName || '-'}}</td>
                        <td>{{data.version }}</td>
                        <td>{{data.framework}}</td>
                        <td>{{data.type }}</td>
                        <td>
                             {{data.source }}
                        </td>
                        <td>
                            {{data.modelStatus }}
                        </td>
                        <td>{{data.ownerName}}</td>
                        <td>{{data.updateTm}}</td>
                        <td nzRight>

                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="downloadModalVisible" nzTitle="下载kubectl配置文件" (nzOnCancel)="handleCancel()"
          (nzOnOk)="download()" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading" [nzWidth]="400">
    <ng-container *nzModalContent>
    <form class="config-content md network-form modalForm" [formGroup]="downloadItem" (submit)="download()">
        <section class="field-section">
            <div class="field-group">
                <div class="field-item topSty">
                    <label>
                        <span class="label-text">有效期：</span>
                        <nz-select formControlName="duration" nzShowSearch nzPlaceHolder="请选择有效期" style="width:240px">
                            <nz-option *ngFor="let item of periodList" [nzValue]="item.key"
                                       [nzLabel]="item.value">
                            </nz-option>
                        </nz-select>
                    </label>
                </div>
                <div class="field-item topSty">
                    <label>
                        <span class="label-text">文件类型：</span>
                        <nz-select formControlName="type" nzShowSearch nzPlaceHolder="请选择类型" style="width:240px">
                            <nz-option *ngFor="let item of fileTypeList" [nzValue]="item.key"
                                       [nzLabel]="item.value">
                            </nz-option>
                        </nz-select>
                    </label>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>