# BPMN 流程编辑器实现

## 概述

本项目已成功集成了 bpmn.io 流程编辑器，实现了与 https://demo.bpmn.io/s/start 相同的**全屏流程编辑功能**。

🎯 **主要特色**: 提供了完整的全屏BPMN编辑器，包含左侧工具面板、完整的建模功能和专业的用户界面。

## 已完成的工作

### 1. 依赖安装
- 安装了 `bpmn-js` 核心库
- 安装了 `bpmn-js-properties-panel` 属性面板库（暂未启用）

### 2. 组件实现

#### BpmnEditorComponent (`src/app/components/console/system/bpmn/editor/`) ⭐ **主要功能**
- **功能**: 全屏BPMN流程编辑器（类似 demo.bpmn.io）
- **特性**:
  - 🎯 **全屏编辑界面** - 与 demo.bpmn.io 相同的用户体验
  - 🎨 **完整的BPMN建模功能** - 左侧工具面板，支持所有BPMN元素
  - ⌨️ **键盘快捷键支持** - 标准的建模快捷键
  - 🔧 **工具栏功能** - 缩放、适应视口、下载等
  - 💾 **文件导出** - BPMN XML 和 SVG 格式
  - 🔄 **自动保存** - 支持流程保存到后端
  - 📱 **响应式设计** - 适配不同屏幕尺寸

#### BpmnViewComponent (`src/app/components/console/system/bpmn/view/`) ⭐ **新增功能**
- **功能**: 只读BPMN流程查看器
- **特性**:
  - 👁️ **只读模式** - 去除所有编辑功能，纯查看模式
  - 🔍 **缩放控制** - 放大、缩小、适应视口、重置缩放
  - 📥 **下载功能** - 支持下载BPMN XML和SVG格式
  - 🎨 **优化界面** - 隐藏工具面板和上下文菜单
  - 📊 **状态显示** - 显示流程ID、版本等信息
  - 🔗 **API集成** - 调用 `/cloud/api/aic/bpmn/getTest/{id}` 接口获取数据

#### BpmnConfigComponent (`src/app/components/console/system/bpmn/config/`)
- **功能**: 流程信息编辑的弹窗组件
- **特性**:
  - 流程基本信息表单（名称、标识、分类、描述）
  - 简化的编辑界面
  - 与列表页面集成

#### BpmnComponent (`src/app/components/console/system/bpmn/`)
- **功能**: 流程管理列表页面
- **特性**:
  - 流程列表展示
  - 搜索和分页功能
  - 👁️ **查看流程按钮** - 跳转到只读查看器
  - 🎯 **设计流程按钮** - 跳转到全屏编辑器
  - 编辑、删除流程
  - 流程发布、暂停等状态管理

### 3. 样式配置
- 在 `angular.json` 中配置了 bpmn-js 相关样式文件
- 自定义了编辑器界面样式，符合项目整体设计风格
- 响应式设计支持

### 4. 服务集成
- 集成了 `BpmnService` 用于后端 API 调用
- 支持流程的 CRUD 操作
- 支持流程状态管理（发布、暂停、归档等）

## 核心功能

### BPMN 编辑器特性
1. **可视化建模**: 拖拽式流程设计
2. **元素支持**: 支持开始事件、任务、网关、结束事件等 BPMN 元素
3. **连接线**: 支持流程节点间的连接
4. **键盘快捷键**: 支持标准的键盘操作
5. **画布操作**: 缩放、平移、适应视口等

### BPMN 查看器特性 ⭐ **新增**
1. **只读模式**: 完全禁用编辑功能，纯查看模式
2. **缩放控制**: 提供放大、缩小、适应视口、重置缩放功能
3. **下载支持**: 支持下载BPMN XML和SVG格式文件
4. **界面优化**: 隐藏编辑工具，突出查看体验
5. **API集成**: 通过专用API获取流程数据

### 表单功能
- 流程名称（必填）
- 流程标识（必填）
- 流程分类
- 流程描述

### 导出功能
- 导出 BPMN XML 格式文件
- 导出 SVG 图像文件

### 路由配置 ⭐ **新增**
- `/console/system/bpmn` - 流程列表页面
- `/console/system/bpmn-editor` - 新建流程编辑器
- `/console/system/bpmn-editor/:id` - 编辑现有流程
- `/console/system/bpmn-view/:id` - 查看流程（只读模式）

## 技术实现

### 前端技术栈
- Angular 18
- ng-zorro-antd UI 组件库
- bpmn-js 流程建模库
- TypeScript
- Less CSS

### 关键代码结构
```
src/app/components/console/system/bpmn/
├── bpmn.component.ts              # 流程管理主页面
├── bpmn.component.html
├── bpmn.component.less
└── config/
    ├── bpmn-config.component.ts   # 流程编辑器弹窗
    ├── bpmn-config.component.html
    └── bpmn-config.component.less
```

### 服务层
```
src/app/service/console/system/
└── bpmn.service.ts                # 流程管理服务
```

## 使用方法

### 方式一：直接访问嵌入式编辑器
1. 启动项目: `npm start`
2. 直接访问: `http://localhost:4200/console/system/bpmn-editor`
3. 开始设计流程图（保留左侧菜单和顶部导航）

### 方式二：通过流程管理页面
1. 访问: `http://localhost:4200`
2. 导航到系统管理 -> 流程管理
3. 点击"新增流程"按钮跳转到嵌入式编辑器
4. 或点击现有流程的"设计流程"按钮进行编辑

### 布局特点
- 🏗️ **嵌入式设计**: 保留系统的左侧菜单和顶部导航
- 📱 **响应式布局**: 自适应不同屏幕尺寸
- 🎯 **专注编辑**: 右侧内容区域专门用于流程设计
- 🔄 **无缝集成**: 与现有系统完美融合

### 编辑器功能
- 🎨 **左侧工具面板**: 拖拽BPMN元素到画布
- ⌨️ **键盘快捷键**: 支持标准建模快捷键
- 🔧 **顶部工具栏**: 缩放、适应视口、保存、下载
- 💾 **文件导出**: 支持BPMN XML和SVG格式下载

## 与 demo.bpmn.io 的对比

### 相同功能
- ✅ 基本的 BPMN 建模功能
- ✅ 拖拽式元素创建
- ✅ 流程连接
- ✅ 键盘快捷键支持
- ✅ 画布缩放和平移
- ✅ BPMN XML 导出

### 项目特有功能
- ✅ 流程信息管理（名称、描述等）
- ✅ 流程列表管理
- ✅ 流程状态管理
- ✅ 与后端 API 集成
- ✅ 符合项目 UI 风格

### 暂未实现功能
- ❌ 属性面板（可后续添加）
- ❌ 高级 BPMN 元素配置
- ❌ 流程验证功能

## 后续扩展建议

1. **属性面板**: 启用 bpmn-js-properties-panel 以支持元素属性编辑
2. **流程验证**: 添加 BPMN 流程语法验证
3. **模板功能**: 提供常用流程模板
4. **版本管理**: 支持流程版本控制
5. **权限控制**: 添加流程编辑权限管理

## 注意事项

- 确保后端 API 接口已正确配置
- 建议在生产环境中启用流程验证功能
- 大型流程图可能需要性能优化

---

## 🆕 最新更新 (2025-07-18)

### 新增 BPMN 查看器组件

基于现有的 `bpmn-editor.component.ts`，创建了全新的只读查看器组件：

#### 1. 创建的文件
- `src/app/components/console/system/bpmn/view/bpmn-view.component.ts` - 查看器组件逻辑
- `src/app/components/console/system/bpmn/view/bpmn-view.component.html` - 查看器模板
- `src/app/components/console/system/bpmn/view/bpmn-view.component.less` - 查看器样式

#### 2. 核心特性
- **只读模式**: 使用 `BpmnViewer` 替代 `BpmnModeler`，完全禁用编辑功能
- **隐藏编辑工具**: 通过CSS隐藏工具面板和上下文菜单
- **专用API**: 调用 `/cloud/api/aic/bpmn/getTest/{id}` 接口获取流程数据
- **缩放控制**: 提供完整的缩放控制功能
- **下载功能**: 支持BPMN XML和SVG格式下载
- **状态显示**: 显示流程ID、版本等元信息

#### 3. 集成方式
- 在 `bpmn.component.html` 中已存在查看按钮，点击跳转到 `bpmn-view/:id` 路由
- 在 `system-routing.module.ts` 中已配置路由
- 在 `system.module.ts` 中已声明组件
- 在 `bpmn.service.ts` 中已添加 `getTestById()` 方法

#### 4. 使用方法
1. 在流程列表页面点击"查看流程"按钮（绿色眼睛图标）
2. 系统会跳转到 `/console/system/bpmn-view/{id}` 页面
3. 自动调用API获取流程数据并在只读模式下展示
4. 用户可以进行缩放操作和下载文件，但无法编辑流程

#### 5. 技术实现
- 使用 `bpmn-js/lib/Viewer` 实现只读查看
- 保持与编辑器相同的中文翻译支持
- 响应式设计，适配不同屏幕尺寸
- 完整的错误处理和加载状态管理

### 构建状态
✅ **构建成功** - 项目可以正常编译和运行

### 文件结构更新
```
src/app/components/console/system/bpmn/
├── bpmn.component.ts              # 流程列表页面
├── bpmn.component.html
├── bpmn.component.less
├── editor/                        # 编辑器组件
│   ├── bpmn-editor.component.ts
│   ├── bpmn-editor.component.html
│   └── bpmn-editor.component.less
├── view/                          # 🆕 查看器组件
│   ├── bpmn-view.component.ts     # 只读查看器
│   ├── bpmn-view.component.html
│   └── bpmn-view.component.less
└── config/                        # 配置弹窗
    ├── bpmn-config.component.ts
    ├── bpmn-config.component.html
    └── bpmn-config.component.less
```
