import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';

import { UsageRecordComponent } from './usage-record.component';

describe('IndexComponent', () => {
    let component: UsageRecordComponent;
    let fixture: ComponentFixture<UsageRecordComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            declarations: [UsageRecordComponent]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(UsageRecordComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
