import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UsageRecordService } from 'src/app/service/console/cost/usage-record.service';
import { NzMessageService } from 'ng-zorro-antd/message'
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { environment } from 'src/environments/environment';
import {FormBuilder, FormGroup} from "@angular/forms";
import {NzResizeEvent} from "ng-zorro-antd/resizable";

@Component({
    selector: 'app-usage-record',
    templateUrl: './usage-record.component.html',
    styleUrls: ['./usage-record.component.less']
})
export class UsageRecordComponent implements OnInit {
    servicePlanTypeList = [{key:"ecs", value:"云服务器"},{key:"evs",value:"云盘"},{key:"k8s",value:"容器"},{key:"redis",value:"Redis"},
        {key:"rds",value:"RDS"},{key:"msg",value:"消息队列"}];

    cols = [
        {
            title: '实例名称',
            ColumnKey: "instanceName",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: ''
        },
        {
            title: '实例类型',
            ColumnKey: "servicePlanTypeText",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '10%'
        },
        {
            title: '启用时间',
            ColumnKey: "servicePlanEndTime",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '15%'
        },
        {
            title: '停用时间',
            ColumnKey: "servicePlanStartTime",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '15%'
        },
        {
            title: '所有者',
            ColumnKey: "ownerDisplayName",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '10%'
        },
        {
            title: '金额（RMB）',
            ColumnKey: "actualCost",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '10%'
        },
        {
            title: '操作',
            ColumnKey: "",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '5%'
        },
    ];

    detailCols = [
        {
            title: '服务时长（月）',
            ColumnKey: "billingCycle",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '15%'
        },
        {
            title: '计费周期',
            ColumnKey: "",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: ''
        },
        {
            title: '原始价格（RMB）',
            ColumnKey: "originalCost",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '15%'
        },
        {
            title: '折扣',
            ColumnKey: "discount",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '10%'
        },
        {
            title: '实际价格（RMB）',
            ColumnKey: "actualCost",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '15%'
        },
    ];

    tableList: any[] = [];
    tableData: any[] = [];
    // 分页
    pager = {
        page: 1,
        pageSize: environment.pageSize,
        total: 0,
    };
    isLoading: boolean = false;
    // 操作状态
    busyStatus = {};
    resLimit: boolean = false;
    userList = [];
    userTemplateList = [];
    filters = {
        pageNum: 0 ,
        pageSize: 15,
        orderBy1: false,
        orderName1: '',
        orderBy2: false,
        orderName2: '',
    };

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private msg: NzMessageService,
        private fb: FormBuilder,
        private usageRecordService: UsageRecordService,
    ) {}
    sortName = '';
    sortValue = false;
    openFlag:boolean = true;
    fristQuery:boolean = false;
    oldSortName;
    sort;
    index;
    keyword: string = '';

    detailModel = false;

    searchForm: FormGroup;
    servicePlanType = 'ecs';
    detailData = [];

    ngOnInit() {
        this.searchForm = this.fb.group({
            keyword: ['', {}],
            servicePlanType: ['', {}],
        });
        this.getDataList();
    }

    getDataList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);
        if (this.searchForm.value.keyword) {
            params.bean = {
                name: this.searchForm.value.keyword
            }
        }
        if(this.searchForm.value.servicePlanType){
            if(!params.bean){
                params.bean = {
                    servicePlanType: this.searchForm.value.servicePlanType
                }
            }else{
                params.bean.servicePlanType = this.searchForm.value.servicePlanType;
            }
        }

        this.isLoading = true;
        this.usageRecordService.getDataList(params)
            .then(rs => {
                if (rs.success) {
                    this.tableData = rs.data.dataList || [];
                    this.tableData.forEach(item => {
                        if (!item.servicePlanEndTime) {
                            item.servicePlanEndTime = "运行中";
                        }
                    });
                    // this.tableData.forEach(item => item.resourceTypeText = item.resourceType ? RESOURCE_TYPE_MAP[item.resourceType] : '-');
                } else {
                    this.msg.error(`获取配额列表失败${ rs.msg ? ': ' + rs.msg : '' }`);
                }
                this.pager = {
                    page: rs.data.pageNum + 1,
                    pageSize: rs.data.pageSize,
                    total: rs.data.recordCount,
                };
                this.isLoading = false;
            })
            .catch(msg => {
                this.msg.error('获取配额列表失败');
                this.isLoading = false;
            });
    }
    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getDataList();
    }

    onParamsChange(params: NzTableQueryParams) {
        if (this.fristQuery) {
            if (this.openFlag) {
                var checkData = false;
                var getIndex
                var index = -1;
                params.sort.forEach(sortDate => {
                    index ++;
                    if(sortDate.value) {
                        this.sortName = sortDate.key;
                        if (sortDate.value === 'ascend') {
                            this.sort = sortDate.value;
                            this.sortValue = false;
                        } else {
                            this.sortValue = true;
                        }
                        checkData = true;
                        getIndex = index;
                    }
                })
                this.index = getIndex;
                if (checkData) {
                    var names = this.sortName.split(',');
                    if (names.length == 2) {
                        this.filters.orderBy2 = this.sortValue;
                        this.filters.orderName2 = names[1];
                    } else {
                        this.filters.orderBy2 = this.sortValue;
                        this.filters.orderName2 = '';
                    }
                    this.filters.orderBy1 = this.sortValue;
                    this.filters.orderName1 = names[0];
                    this.getDataList()
                } else {
                    this.filters.orderBy1 = false;
                    this.filters.orderName1 = '';
                    this.filters.orderBy2 = false;
                    this.filters.orderName2 = '';
                    this.getDataList();
                }
            } else {
                this.openFlag = true;
            }
        } else {
            this.fristQuery = true;
        }
    }

    search() {
        this.filters.pageNum = 0;
        this.getDataList();
    }


    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
        if (this.index !== undefined && this.index !== '') {
            this.cols[this.index].allowSort = this.sort;
        }
        this.openFlag = false;
    }

    viewData = {
        instanceName: "",
        ownerDisplayName: "",
        servicePlanTypeText: "",
        actualCost: ""
    };
    viewDetail(data: any) {
        this.viewData = data;
        if(data.enabledStatus){
            return;
        }
        this.userTemplateList = [];
        this.usageRecordService.getChildList(data.id)
            .then(rs => {
                if (rs.success) {
                    this.detailData = rs.data;
                    this.detailModel = true;
                } else {
                    this.msg.error(`操作失败${ rs.msg ? ': ' + rs.msg : '' }`);
                }
                this.isLoading = false;
            })
            .catch(msg => {
                this.msg.error('操作失败');
                this.isLoading = false;
            });
    }

    onDetailDataChange($event: NzTableQueryParams) {
        const sortType = $event.sort[0].value;
        if (sortType === 'ascend') {
            this.detailData.sort((a, b) => a.billingCycle - b.billingCycle);
        } else if (sortType === 'descend') {
            this.detailData.sort((a, b) => b.billingCycle - a.billingCycle);
        } else{
            this.detailData.sort((a, b) => a.billingCycle - b.billingCycle);
        }
    }
    reset(){
        this.searchForm.reset();
    }
}
