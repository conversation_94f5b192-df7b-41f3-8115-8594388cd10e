<div class="table-content">
<!--  <ol class="on-breadcrumb">-->
<!--      <li><a routerLink="../">云监控</a></li>-->
<!--      <li><span>监控大盘</span></li>-->
<!--  </ol>-->
  <div class="on-panel">
      <div class="on-panel-header">
          <h3 class="title">监控大盘</h3>
      </div>
      <nz-spin [nzSpinning]="chartContainerLoading" [nzDelay]="300">
        <div class="on-panel-body">
          <!-- 第一级操作台 -->
          <div class="action-bar clearfix">
            <!-- 选择监控大盘 -->
            <nz-tabset [nzType]="'card'" [nzTabBarExtraContent]="extraTemplate">
              <nz-tab *ngFor="let tab of selectedMonotorNameOptions" 
                      [nzTitle]="titleTemplate" 
                      (nzClick)="chooseTab(tab)">
                <ng-template #titleTemplate>
                  <div>
                    <span>{{ tab.chartContainerName }}</span>
                    <i nz-icon 
                      nzType="close"
                      (click)="deleteMonitor(tab)"
                      class="ant-tabs-close-x"></i>
                  </div>
                </ng-template>
              </nz-tab>
            </nz-tabset>
            <ng-template #extraTemplate>
              <i class="ant-tabs-new-tab"
                  nz-icon nzType="plus" 
                  nzTitle="新增大盘"
                  nzTooltipContent="top"
                  nz-tooltip
                  (click)="addOrUpdateMonitor('create')"></i>
            </ng-template>
          </div>
          <!-- 第二级操作台 -->
          <div class="action-bar clearfix" *ngIf="selectedMonotorNameOptions && selectedMonotorNameOptions.length">
            <button nz-button nz-dropdown [nzDropdownMenu]="menu"><span>更多操作</span>
                <i nz-icon type="down"></i>
            </button>
            <nz-dropdown-menu #menu="nzDropdownMenu" class="action-bar-btn">
              <ul nz-menu>
                <li nz-menu-item (click)="addOrUpdateMonitor('update')">
                  <a>修改名称</a>
                </li>
                <li nz-menu-item (click)="addOrUpdateCharts('add')">
                  <a>添加图表</a>
                </li>
              </ul>
            </nz-dropdown-menu>
            <!-- 选择时间 -->
            <nz-radio-group class="action-bar-btn"
                          [(ngModel)]="timeValue" 
                          [nzButtonStyle]="'solid'"
                          (ngModelChange)="selectTime($event)">
              <label nz-radio-button [nzValue]="data.value" *ngFor="let data of timeOptions">{{ data.name }}</label>
            </nz-radio-group>
            <!-- 同比 -->
            <nz-checkbox-group class="action-bar-btn" [(ngModel)]="compareData" (ngModelChange)="compare($event)"></nz-checkbox-group>
            <button nz-button nzType="primary" 
                    class="pull-right" 
                    (click)="refresh()">
              <i nz-icon nzType="reload"></i>手动刷新
            </button>
          </div>
          <!-- 图表 -->
          <div>
            <section class="echarts-container" *ngFor="let data of echartsDataList; index as i">
              <!-- 工具栏 -->
              <div class="tool-box">
                <span class="echarts-title">{{data.chartName}}</span>
                <div>
                    <i class="fa fa-info-circle"
                      nz-tooltip [nzTitle]="titleTemplate"
                      nzTheme="outline"></i>
                    <ng-template #titleTemplate>
                      <p class="break-word-title">{{data.resourceName}}</p>
                      <p>{{'监控指标：' + data.metricName}}</p>
                      <p>采集周期：5分钟</p>
                      <p>{{'时间范围：' + data.option.xAxis.data[0] + '~' + data.option.xAxis.data[data.option.xAxis.data.length - 1]}}</p>
                      <p>{{'描述：' + data.metricDes}}</p>
                      <p>{{'单位：' + data.unit}}</p>
                    </ng-template>
                  <i class="fa fa-search"
                    nzTitle="查看大图"
                    (click)="showChartsDetail(data)"
                    nzTooltipContent="topCenter"
                    nz-tooltip></i>
                  <i class="fa fa-pencil-square-o"
                    nzTitle="修改"
                    (click)="addOrUpdateCharts('update', data)"
                    nzTooltipContent="topCenter"
                    nz-tooltip></i>
                  <i class="fa fa-trash"
                    nzTitle="删除"
                    (click)="deleteCharts(data)"
                    nzTooltipContent="topCenter"
                    nz-tooltip></i>
                </div>
              </div>
              <!-- 图表 -->
              <nz-spin [nzSpinning]="data.loading" [nzDelay]="300">
                  <div class="echarts" echarts [options]="data.option"></div>
              </nz-spin>
              <!-- 无数据提示 -->
              <div class="empty-container" *ngIf="data.empty">
                <p>暂无数据</p>
              </div>
            </section>
            <nz-empty *ngIf="echartsDataListEmpty"></nz-empty>
          </div>
          <nz-empty *ngIf="!selectedMonotorNameOptions"></nz-empty>
        </div>
      </nz-spin>
  </div>
</div>
<!--新建监控大盘弹框-->
<nz-modal [(nzVisible)]="showCreateWindow" [nzTitle]="showCreateWindowTitle"
          (nzOnCancel)="showCreateWindow = false"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="confirmCreateOrUpdateWindow()">
  <ng-container *nzModalContent>
  <div class="tc-content">
    <div class="sele taller-sele">
      <div class="sele-label col-sele-2">
        <label>名称</label>
      </div>
      <div class="col-sele-11">
        <nz-input-group [nzSuffix]="suffixTemplate">
          <input type="text" 
                 (input)="nameCheckFun(name)"
                 nz-input
                 [(ngModel)]="name" placeholder="请输入名称" />
        </nz-input-group>
        <ng-template #suffixTemplate>
          <i
            nz-icon
            nz-tooltip
            class="ant-input-clear-icon"
            nzTheme="fill"
            nzType="close-circle"
            *ngIf="name"
            (click)="name = null">
          </i></ng-template>
        <ol class="order-list" [ngClass]="{'warning-text': !nameCheck}">
          <li>名称仅允许包含数字、大小写字母、中文、“.”、“_”或“-”</li>
          <li>长度不超过64个字符</li>
        </ol>
      </div>
    </div>
  </div>
  </ng-container>
</nz-modal>
<!--新建图表弹框-->
<nz-modal [(nzVisible)]="showCreateChartsWindow" 
          [nzTitle]="showCreateChartsWindowTitle"
          (nzOnCancel)="showCreateChartsWindow = false"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="confirmCreateCharts()">
  <ng-container *nzModalContent>
  <div class="tc-content">
    <div class="sele" style="height: 90px">
      <div class="sele-label col-sele-2">
        <label>图表名称</label>
      </div>
      <div class="col-sele-11">
          <nz-input-group [nzSuffix]="chartNameTemplate">
            <input nz-input
                  type="text" 
                  (input)="chartNameCheckFun(createChartsName)"
                  placeholder="请输入图表名称" 
                  [disabled]="isLoading"
                  [(ngModel)]="createChartsName">
          </nz-input-group>
          <ng-template #chartNameTemplate>
            <i
              nz-icon
              nz-tooltip
              class="ant-input-clear-icon"
              nzTheme="fill"
              nzType="close-circle"
              *ngIf="createChartsName"
              (click)="createChartsName = null">
            </i></ng-template>
          <ol class="order-list" [ngClass]="{'warning-text2': !chartNameCheck}">
            <li>名称仅允许包含数字、大小写字母、中文、“.”、“_”或“-”</li>
            <li>长度不超过15个字符</li>
          </ol>
        </div>
    </div>
    <!-- 资源类型 -->
    <div class="sele">
      <div class="sele-label col-sele-2">
        <label>资源类型</label>
      </div>
      <div class="col-sele-11">
        <nz-select
          style="width: 100%;"
          nzShowSearch
          (ngModelChange)="selectResourceType($event)"
          [(ngModel)]="resourceTypeValue">
          <nz-option *ngFor="let option of resourceTypeOptions" 
                      [nzValue]="option.name" 
                      [nzLabel]="option.des">
          </nz-option>
        </nz-select>
      </div>
    </div>
    <!-- 云资源 -->
    <div class="sele">
      <div class="sele-label col-sele-2">
        <label>云资源</label>
      </div>
      <div class="col-sele-11">
        <nz-select
            style="width: 100%;"
            nzShowSearch
            [(ngModel)]="cloudResourceValue">
            <nz-option *ngFor="let option of cloudResourceOptions" 
                        [nzValue]="option.resourceId" 
                        [nzLabel]="option.resourceName">
            </nz-option>
          </nz-select>
      </div>
    </div>
    <!-- 监控指标 -->
    <div class="sele">
      <div class="sele-label col-sele-2">
        <label>监控指标</label>
      </div>
      <div class="col-sele-11">
          <nz-select style="width: 100%"
              nzPlaceHolder="请选择"
              [(ngModel)]="monitorIndexValue">
              <nz-option  *ngFor="let option of monitorIndexOptions"
                            nzCustomContent [nzLabel]="option.metricName" 
                            [nzValue]="option.metricCode">
                    <span [title]="desTemplate"
                          nzTooltipContent="bottom"
                          nz-tooltip>{{option.metricName}}</span>
                      <ng-template #desTemplate>
                        <p>{{option.metricName}}</p>
                        <p>{{'描述：' + option.description}}</p>
                      </ng-template>
              </nz-option>
          </nz-select>
      </div>
    </div>
    <!-- 时间 -->
    <!-- <div class="sele">
      <div class="sele-label col-sele-2">
        <label>时间</label>
      </div>
      <div class="col-sele-11">
        <nz-select
            style="width: 100%;"
            nzShowSearch
            [(ngModel)]="createChartsTime">
            <nz-option *ngFor="let option of timeOptions" 
                        [nzValue]="option.value" 
                        [nzLabel]="option.name">
            </nz-option>
          </nz-select>
      </div>
    </div> -->
    <!-- 阈值 -->
    <div class="sele">
      <div class="sele-label col-sele-2">
        <label>阈值</label>
      </div>
      <div class="col-sele-11">
        <div class="threshold-container">
          <span>警告：</span>
          <nz-input-number class="threshold-input" [(ngModel)]="thresholdNumObj.warn" [nzStep]="1"></nz-input-number>
        </div>
        <div class="threshold-container">
          <span>严重：</span>
          <nz-input-number class="threshold-input" [(ngModel)]="thresholdNumObj.seriousness" [nzStep]="1"></nz-input-number>
        </div>
        <div class="threshold-container">
          <span>紧急：</span>
          <nz-input-number class="threshold-input" [(ngModel)]="thresholdNumObj.urgency" [nzStep]="1"></nz-input-number>
        </div>
      </div>
    </div>
    <div class="sele">
      <span class="warning-tips" [ngClass]="{'red': !thresholdNumWarning}">*请确保您输入的阈值符合 警告 &lt; 严重 &lt; 紧急 ！</span>
    </div>
  </div>
  </ng-container>
</nz-modal>
<!--图表大图弹框-->
<nz-modal [(nzVisible)]="showChartsDetailWindow" [nzTitle]="showChartsDetailTitle"
          [nzWidth]="1000"
          (nzOnOk)="showChartsDetailWindow = false"
          (nzOnCancel)="showChartsDetailWindow = false"
          [nzCancelText]=null
          nzOkText="关闭">
  <ng-container *nzModalContent>
  <div class="tc-content">
    <!-- 工具栏 -->
    <div class="action-bar clearfix">
      <!-- 选择时间 -->
      <nz-radio-group class="action-bar-btn"
                      [(ngModel)]="timeValueDetail" 
                      [nzButtonStyle]="'solid'"
                      (ngModelChange)="selectTimeDetail($event)">
        <label nz-radio-button [nzValue]="data.value" *ngFor="let data of timeOptions">{{ data.name }}</label>
      </nz-radio-group>
      <!-- 同比 -->
      <nz-checkbox-group class="action-bar-btn"
                        style="width: 20%;vertical-align: middle" 
                        [(ngModel)]="compareDetailData"
                        (ngModelChange)="compareDetail($event)">
      </nz-checkbox-group>
      <div class="aggregation-container">
        <span>聚合</span>
        <nz-select
          style="width: 32%;margin-right: 5px"
          nzShowSearch
          [nzDisabled]="compare1Detail || compare7Detail"
          (ngModelChange)="selectAggregation($event)"
          [(ngModel)]="selectAggregationValue">
          <nz-option *ngFor="let option of aggregationOption" 
                      [nzValue]="option.value" 
                      [nzLabel]="option.name">
          </nz-option>
        </nz-select>
        <span *ngIf="selectAggregationValue !== 'null'">聚合周期</span>
        <nz-select
          *ngIf="selectAggregationValue !== 'null'"
          style="width: 32%"
          nzShowSearch
          [nzDisabled]="compare1Detail || compare7Detail"
          (ngModelChange)="selectAggregationPriod($event)"
          [(ngModel)]="selectAggregationPriodValue">
          <nz-option *ngFor="let option of aggregationPriodOption" 
                      [nzValue]="option.value" 
                      [nzLabel]="option.name">
          </nz-option>
        </nz-select>
      </div>
      <button nz-button nzType="primary" 
              class="pull-right" 
              (click)="refreshChartsDetail()">
        <i nz-icon nzType="reload"></i>手动刷新
      </button>
      <p class="warning-text">*环比和聚合为互斥项，请合理选择！</p>
    </div>
    <!-- 图表 -->
    <nz-spin [nzSpinning]="chartLoading" [nzDelay]="300">
        <div class="echarts" *ngIf="showChartsDetailObj" echarts [options]="showChartsDetailObj.option"></div>
    </nz-spin>
    <!-- 无数据提示 -->
    <div class="empty-container" *ngIf="showChartsDetailEmpty">
      <p>暂无数据</p>
    </div>
  </div>
  </ng-container>
</nz-modal>

