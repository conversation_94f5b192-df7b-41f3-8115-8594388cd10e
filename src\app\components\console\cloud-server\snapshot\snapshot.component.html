<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">云服务器</a></li>-->
<!--        <li><span>快照</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入快照名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right" *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary"
                            (click)="createModalVisible = true">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建快照
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">实例</span>

                <div class="danger">
                    <i class="fa fa-exclamation-circle"></i>
                    目前系统盘和每个数据盘只能创建一个快照，新的快照会覆盖旧的快照，请谨慎操作。
                </div>
            </div>
            <nz-table #snapshots style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                [nzData]="snapshotList">
                <thead>
                    <tr>
                        <th width="20%">快照名称</th>
                        <th width="25%">云服务器</th>
                        <th>创建时间</th>
                        <th>付费方式</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of snapshots.data; trackBy: trackById">
                        <td>{{item.name}}</td>
                        <td>{{item.vmName}}</td>
                        <td>{{item.createTm}}</td>
                        <td>后付费</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item" *ngIf="permission('restore')"
                                    (click)="showRestoreConfirm(item)">
                                    <i nzTooltipTitle="还原"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-history"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该快照吗？"
                                    (nzOnConfirm)="deleteSnapshot(item);">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="snapshots.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="createModalVisible" nzTitle="创建快照"
    nzOkText="创建" [nzOkLoading]="isCreating"
    [nzBodyStyle]="{padding: '8px'}"
    (nzOnCancel)="hideCreateModal()"
    [nzWidth]="450"
    (nzOnOk)="createSnapshot()">
    <ng-container *nzModalContent>
    <form [formGroup]="snapshot" class="config-content sm">
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">云服务器</span>
                    <nz-select formControlName="vmId"
                        nzShowSearch
                        nzPlaceHolder="请选择要创建快照的云服务器">
                        <nz-option
                            *ngFor="let item of instanceList"
                            [nzValue]="item.spUuid"
                            [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                    <div *ngIf="isDirty(snapshot.get('vmId'))"
                        class="form-hint error">
                        <div
                            *ngIf="snapshot.get('vmId').hasError('required')">
                            请选择一个云服务器
                        </div>
                    </div>
                </label>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">快照名称</span>
                    <input nz-input type="text"
                        formControlName="name"
                        maxlength="50" placeholder="快照名称">
                    <div class="small tip label-padding">
                        快照名称必须以字母开头，只能输入英文、数字、中划线
                    </div>
                </label>
                <div *ngIf="isDirty(snapshot.get('name'))"
                    class="form-hint error">
                    <div
                        *ngIf="snapshot.get('name').hasError('required')">
                        快照名称不能为空
                    </div>
                    <div
                        *ngIf="snapshot.get('name').hasError('pattern')">
                        快照名称不符合规定格式
                    </div>
                    <div
                        *ngIf="snapshot.get('name').hasError('maxlength')">
                        快照名称长度不能超过{{ snapshot.get('name').errors.maxlength.requiredLength }}个字符
                    </div>
                </div>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>

<app-restore-confirm [isVisible]="restoreModalVisible"
    type="snapshot" [cloudServer]="restoreCloudServer"
    [restoreItem]="currentRestoreItem"
    (close)="restoreModalVisible = false"
    (submit)="restoreSnapshot(currentRestoreItem)">
</app-restore-confirm>
