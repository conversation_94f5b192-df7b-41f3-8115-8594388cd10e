.flex-col{
  font-size: 14px;
}
div {
  box-sizing: border-box;
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #212332;
  background: #F5F7FC;
  width: 100%;
  padding-top: 58px;
}
nav {
  padding: 30px;
}
nav a {
  font-weight: bold;
  color: #2c3e50;
}
nav a.router-link-exact-active {
  color: #42b983;
}

a {
  padding-left: 40px;
  color: #212332;
  text-decoration: none;
}
a.document {
  padding-left: 0 !important;
}
a:first-child {
  /*padding-left: 50px;*/
}
a:hover,
a:active {
  font-weight: bold;
  color: #155EEF;
}
a.primary:hover {
  color: #fff;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-row-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  //gap:0;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-center {
  align-items: center;
  justify-content: center;
}
.items-stretch {
  align-items: stretch;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: start;
}
.items-end {
  align-items: end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-start {
  justify-content: start;
}
.p-r {
  position: relative;
  z-index: 3;
}
.p-absolute {
  position: absolute;
}
.w-140 {
  width: 140px !important;
}
.w-160 {
  width: 160px;
}
.w-200 {
  width: 200px;
}
.w-420 {
  width: 420px;
}
.w-450 {
  width: 450px;
}
.w-480 {
  width: 480px;
}
.w-500 {
  width: 500px !important;
}
.w-540 {
  width: 540px;
}
.w-full {
  width: 100%;
}
.h-full-1800 {
  height: 10000px;
}
.h-40 {
  height: 40px !important;
}
.h-55 {
  height: 55px !important;
}
.h-60 {
  height: 60px !important;
}
.h-120 {
  height: 120px;
}
.h-600 {
  height: 600px !important;
}
.h-800 {
  height: 800px !important;
}
.w-percent-50 {
  width: 50%;
}
.w-percent-30 {
  width: 30%;
}
.h-full {
  height: 100vm !important;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.line-h-30 {
  line-height: 30px !important;
}
.line-h-60 {
  line-height: 60px;
}
.ml-0 {
  margin-left: 0 !important;
}
.ml-10 {
  margin-left: 10px;
}
.ml-15 {
  margin-left: 15px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-30 {
  margin-left: 30px;
}
.ml-35 {
  margin-left: 35px;
}
.ml-40 {
  margin-left: 40px;
}
.ml-50 {
  margin-left: 50px;
}
.ml-60 {
  margin-left: 60px;
}
.ml-70 {
  margin-left: 70px;
}
.ml-80 {
  margin-left: 80px;
}
.ml-100 {
  margin-left: 100px;
}
.ml-116 {
  margin-left: 116px;
}
.-ml-110 {
  margin-left: -110px;
}
.-ml-12 {
  margin-left: -12px;
}
.-ml-25 {
  margin-left: -25px;
}
.mlr-6 {
  margin-left: 6px;
  margin-right: 6px;
}
.mlr-10 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}
.ml-165 {
  margin-left: 165px;
}
.mr-165 {
  margin-right: 165px;
}
.mt-4 {
  margin-top: 4px;
}
.mt-5 {
  margin-top: 5px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-15 {
  margin-top: 15px !important;
}
.mt-10 {
  margin-top: 10px;
}
.mt-12 {
  margin-top: 12px;
}
.mt-16 {
  margin-top: 16px !important;
}
.mt-18 {
  margin-top: 18px;
}
.mt-20 {
  margin-top: 20px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-38 {
  margin-top: 38px;
}
.mt-40 {
  margin-top: 40px !important;
}
.mt-50 {
  margin-top: 50px !important;
}
.mt-60 {
  margin-top: 60px !important;
}
.mt-65 {
  margin-top: 65px !important;
}
.mt-70 {
  margin-top: 70px !important;
}
.mt-80 {
  margin-top: 80px !important;
}
.mt-85 {
  margin-top: 85px !important;
}
.mt-100 {
  margin-top: 100px;
}
.mt-116 {
  margin-top: 116px;
}
.mt-130 {
  margin-top: 130px;
}
.mt-120 {
  margin-top: 120px;
}
.mt-140 {
  margin-top: 140px;
}
.mt-160 {
  margin-top: 160px;
}
.mt-200 {
  margin-top: 200px !important;
}
.mt-300 {
  margin-top: 300px;
}
.-mt-18 {
  margin-top: -18px;
}
.-mt-28 {
  margin-top: -28px;
}
.-mt-38 {
  margin-top: -38px;
}
.-mt-73 {
  margin-top: -63px;
}
.mr-10 {
  margin-right: 10px;
}
.mr-15 {
  margin-right: 15px;
}
.mr-20 {
  margin-right: 20px;
}
.mr-25 {
  margin-right: 25px;
}
.mr-50 {
  margin-right: 50px;
}
.mr-60 {
  margin-right: 60px;
}
.mb-50 {
  margin-bottom: 50px;
}
.mb-90 {
  margin-bottom: 90px;
}
.mb-120 {
  margin-bottom: 120px;
}
.pl-0 {
  padding-left: 0 !important;
}
.pl-5 {
  padding-left: 5px;
}
.pl-10 {
  padding-left: 10px;
}
.pl-120 {
  padding-left: 120px;
}
.plr-12 {
  padding-left: 12px;
  padding-right: 12px;
}
.plr-16 {
  padding-left: 16px;
  padding-right: 16px;
}
.plr-20 {
  padding-left: 20px;
  padding-right: 20px;
}
.plr-25 {
  padding-left: 25px;
  padding-right: 25px;
}
.plr-100 {
  padding-left: 100px;
  padding-right: 100px;
}
.pt-12 {
  padding-top: 12px;
}
.ptb-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.pb-130 {
  padding-bottom: 130px;
}
.p-40 {
  padding: 40px;
}
.p-50 {
  padding: 50px;
}
.fs-14 {
  font-size: 14px !important;
}
.fs-14-500 {
  font-size: 14px;
  font-weight: 500;
}
.fs-14-fat {
  font-size: 14px;
  font-weight: 600;
}
.fs-16 {
  font-size: 16px;
  font-weight: 500;
}
.fs-16-fat {
  font-size: 16px;
  font-weight: 600;
}
.fs-16-thin {
  font-size: 16px;
  font-weight: 400;
}
.fs-18-thin {
  font-size: 18px;
  font-weight: 300;
}
.fs-18 {
  font-size: 18px;
  font-weight: 400;
}
.fs-18-fat-0 {
  font-size: 18px;
  font-weight: 500;
}
.fs-18-fat {
  font-size: 18px;
  font-weight: 600;
}
.fs-20-thin {
  font-size: 20px;
  font-weight: 300;
}
.fs-20 {
  font-size: 20px;
  font-weight: 500;
}
.fs-20-fat {
  font-size: 20px;
  font-weight: 600;
}
.fs-24 {
  font-size: 24px;
  font-weight: 400;
}
.fs-24-fat {
  font-size: 24px;
  font-weight: 600;
}
.fs-26-fat {
  font-size: 26px;
  font-weight: 600;
}
.fs-28 {
  font-size: 28px;
  font-weight: 600;
}
.fs-30 {
  font-size: 30px;
  font-weight: 600;
}
.fs-36 {
  font-size: 36px;
  font-weight: 600;
}
.fs-44 {
  font-size: 44px;
  font-weight: 600;
}
.fs-48 {
  font-size: 48px;
  font-weight: 600;
}
.fs-64 {
  font-size: 64px;
  font-weight: 600;
}
.fc-white {
  color: #fff;
}
.fc-main {
  color: #212332;
}
.fc-sub {
  color: #5B6167;
}
.fc-sub-1 {
  color: #535666;
}
.fc-gray-100 {
  color: #A8A9AA;
}
.icon-24 {
  width: 24px;
  height: 24px;
}
.icon-28 {
  width: 28px;
  height: 28px;
}
.icon-36 {
  width: 36px;
  height: 36px;
}
.icon-40 {
  width: 40px;
  height: 40px;
}
.icon-44 {
  width: 44px;
  height: 44px;
}
.icon-48 {
  width: 48px;
  height: 48px;
}
.size-216 {
  width: 216px;
  height: 216px;
}
.top-0 {
  top: 0;
}
.bottom-0 {
  bottom: 0;
}
.bottom-73 {
  bottom: 73px;
}
.left-0 {
  left: 0;
}
.left-40 {
  left: 40px;
}
.right-0 {
  right: 0;
}
.text-main {
  color: #155EEF;
}
.text-sec {
  color: #5B6167;
}
.bg-white {
  background: #fff;
}
.rounded-12 {
  border-radius: 12px;
}
.rounded-16 {
  border-radius: 16px;
}
.primary {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background: #155EEF;
  border-radius: 6px;
  color: #fff;
  border: none;
  box-shadow: none;
  padding: 10px 20px;
}
.primary.shadow {
  box-shadow: 0px 4px 6px 0px rgba(10, 34, 66, 0.15);
}
.primary.large {
  box-sizing: border-box;
  text-align: center;
  width: 180px;
  height: 58px;
  line-height: 38px;
}
.primary-font-color,
.fc-primary {
  color: #155EEF;
}
.home {
  margin-top: -58px;
}
.home h1 {
  font-size: 48px;
}

.container {
  position: relative;
  box-sizing: border-box;
  width: 1200px;
  padding: 0;
  margin: 0 auto;
}
.container1 {
  position: relative;
  box-sizing: border-box;
  max-width: 1200px;
  margin: 0 auto;
}
.container-full {
  position: relative;
  width: 100%;
  height: auto;
}
.tips {
  width: 358px;
  padding: 15px 24px;
  color: #155EEF;
  background: #EFF4FF;
  border-radius: 28px;
  border: 1px solid #EBEBEB;
  margin-top: 165px;
  font-size: 18px;
}
.item-box {
  position: relative;
  width: 390px;
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #EBEBEB;
  padding: 30px 28px;
  text-align: left;
  line-height: 20px;
}
.item-box .font-main {
  font-size: 18px;
  font-weight: 500;
}
.item-box .font-desc {
  font-size: 14px;
  font-weight: 400;
  color: #5B6167;
}
.item-box .font-price {
  font-size: 28px;
  font-weight: 500;
}
.item-box.active {
  border: 2px solid #155EEF;
}
.item-box.active .font-main {
  color: #155EEF;
}
.span-box span {
  margin-top: 20px;
}
.span-box span:first-child {
  margin-top: 0;
}
.rb-button {
  display: block;
  text-align: center;
  padding: 15px;
  font-size: 16px;
  color: #155EEF;
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #155EEF;
}
.rb-button.active {
  background: #155EEF;
  color: #fff;
}
.privatized-box {
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #EBEBEB;
  padding: 60px 24px 0 60px;
  text-align: left;
}
.privatized-box h2 {
  font-size: 28px;
}
.icon-18 {
  width: 18px;
  height: 18px;
}
.black-button {
  background: #212332;
  border-radius: 12px;
  padding: 15px 80px;
  color: #fff;
  font-size: 16px;
  border: none;
  box-shadow: none;
}
.logo-shading-small {
  width: 820px;
  height: 120px;
}
.icon-56-36 {
  width: 56px;
  height: 36px;
}
.question-item-box {
  position: relative;
  padding: 40px 30px 0 30px;
  width: 294px;
  height: 400px;
  background: radial-gradient(131% 90% at 90% 100%, #065BFF 0%, #BDD3FF 100%, #BDD3FF 100%), radial-gradient(69% 64% at -3% -18%, #F2F4FF 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 10px;
}
.h-400 {
  height: 400px;
  padding: 60px;
}
.h-400-box {
  width: 886px;
  height: 400px;
  padding: 60px;
  background: #FFFFFF;
  border-radius: 12px;
}
.item-box-q {
  cursor: pointer;
}
.item-box-q .font-main {
  font-size: 18px;
  color: #212332;
  font-weight: 500;
}
.item-box-q .font-desc {
  display: none;
  color: #5B6167;
}
.item-box-q .font-sec {
  font-size: 18px;
  color: #212332;
  font-weight: 500;
}
.item-box-q:hover .font-main {
  font-size: 18px;
  color: #155EEF !important;
  font-weight: 500;
}
.item-box-q:hover .font-desc {
  display: block;
  color: #5B6167;
}
.icon-question {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200px;
  height: 200px;
}
.icon-28 {
  width: 28px;
  height: 28px;
}
.regbox-full {
  position: relative;
  width: 100%;
  height: 236px;
  background: url("/assets/images/index/form-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top left;
}
.border-code {
  width: 240px;
  height: 240px;
  border: 1px solid #FFFFFF;
  padding: 12px;
}
.reg-btn-footer {
  display: flex;
  width: 180px;
  height: 58px;
  padding-left: 0;
  background: #FFFFFF;
  box-shadow: 0px 4px 6px 0px rgba(10, 34, 66, 0.15);
  border-radius: 12px;
  color: #155EEF;
}

.product-illustration-2 {
  flex-shrink: 0;
  padding: 40px;
  width: 600px !important;
  height: 800px;
  border-radius: 10px;
  border: 1px solid #EDEDED;
}
.product-illustration-2 img.img-full {
  width: 100%;
  height: auto;
}
.text-6 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 36px;
  color: #212332;
  text-shadow: 0px 3px 0px rgba(0, 0, 0, 0.1);
}
.text-6 .font-main {
  color: #155EEF;
}
.function-box {
  box-sizing: border-box;
  padding: 30px;
  width: 280px;
  height: 180px;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  color: #5B6167;
  font-weight: 400;
  line-height: 20px;
  z-index: 99;
}
.function-box.api {
  background: radial-gradient(66% 90% at 90% 100%, #065BFF 0%, #BDD3FF 100%, #BDD3FF 100%), radial-gradient(0% 64% at 10% -27%, #F2F4FF 0%, rgba(255, 255, 255, 0) 100%);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
}
.text-white {
  color: #fff;
}
.product-2 {
  background: #fff;
  margin-top: -100px;
  padding-top: 170px;
  min-height: 1036px;
}
.w-180 {
  width: 180px;
}
.w-250 {
  width: 250px;
}
.w-200 {
  width: 200px;
}
.w-320 {
  width: 320px;
}
.w-380 {
  width: 380px;
}
.w-468 {
  width: 468px;
}
.w-880 {
  width: 880px;
}
.w-600 {
  width: 600px;
}
.w-612 {
  width: 612px;
}
.w-800 {
  width: 800px;
}
.h-370 {
  height: 370px;
}
.product-2-1-2 {
  position: absolute;
  bottom: 0;
  right: 50%;
  margin-right: -600px;
  flex-shrink: 0;
  width: 672px;
  height: 376px;
  background: #FFFFFF;
  box-shadow: -4px 2px 10px 0px rgba(0, 0, 0, 0.05);
  border-radius: 12px 0px 0px 0px;
}
.product-2-1-2 img {
  width: 100%;
  height: 100%;
  border-radius: 12px 0px 0px 0px;
}
.a-b-r {
  position: absolute;
  right: 0;
  bottom: 0;
}
.v-enter-from {
  transform: translateX(100%);
  opacity: 0;
}
.v-enter-active,
.v-leave-active {
  transition: transform 600ms ease-in-out, opacity 600ms ease-in-out;
}
.v-enter-to {
  transform: translateX(0);
  opacity: 1;
}
.v-leave-from {
  transform: translateX(0);
  opacity: 1;
}
.v-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
.recommend {
  position: absolute;
  width: 128px;
  height: 50px;
  top: 12px;
  right: -6px;
}
.case-1 {
  background: #E4E9F4;
}
.case-1-img {
  width: 683px;
  height: 542px;
}
.case-applet-bg {
  position: absolute;
  width: 680px;
  height: 430px;
  top: 0;
  left: 0;
  z-index: 0;
}
.case-tab-title {
  cursor: pointer;
  padding: 0 30px;
  font-size: 16px;
  color: #212332;
  font-weight: 600;
}
.case-tab-title.active {
  position: relative;
  color: #155EEF;
}
.case-tab-title.active::after {
  content: "";
  position: absolute;
  bottom: -22px;
  left: 50%;
  margin-left: -10px;
  width: 20px;
  height: 4px;
  border-radius: 2px;
  background: #155EEF;
}
.solution-bg {
  padding: 60px 48px 48px 60px;
  background: #fff;
  border-radius: 12px;
  height: 500px;
}
.solution-bg.solution-bg-1 {
  background: linear-gradient(166deg, #FFFFFF 50%, #DBE7F9 100%);
}
.bg-f5f7fc {
  background-color: #F5F7FC;
}
.solution-img-1 {
  position: absolute;
  bottom: -60px;
  right: 0;
  width: 585px;
  height: 377px;
}
.s-img-2 {
  position: absolute;
  bottom: -100px;
  right: 0;
  width: 340px;
  height: 275px;
}
.s-bg {
  background: linear-gradient(-90deg, #F5F7FC 0%, #FFFFFF 50%);
  border-radius: 12px;
}
.s-bg-1 {
  width: 540px;
  height: 120px;
  border-radius: 8px;
  background: #FFFFFF;
  border: 1px solid #EBEBEB;
  padding: 25px 30px;
}
.s-img-3 {
  width: 456px;
  height: 220px;
}
.border-r-1 {
  border-right: 1px solid #EBEBEB !important;
}
.border-b-1 {
  border-bottom: 1px solid #EBEBEB;
}
.s-img-4 {
  width: 777px;
  height: 216px;
}
.s-img-box-1 {
  width: 190px;
  height: 230px;
  background: #F1F5FD;
  border-radius: 8px;
  padding: 50px 0;
}
.s-img-box-2 {
  width: 190px;
  height: 230px;
  background: #F9F4FF;
  border-radius: 8px;
  padding: 50px 0;
}
.s-img-box-3 {
  width: 190px;
  height: 230px;
  background: #F1FDF5;
  border-radius: 8px;
  padding: 50px 0;
}
.s-img-box-4 {
  width: 190px;
  height: 230px;
  background: #FDF2F1;
  border-radius: 8px;
  padding: 50px 0;
}
.s-img-box {
  width: 190px;
  height: 230px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #EBEBEB;
  padding: 10px 10px 10px 20px;
}
.s-img-box-11 {
  height: 99px !important;
}
.s-img-car {
  z-index: 0;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 400px;
  height: 300px;
}
.z-index-99 {
  position: relative;
  z-index: 99;
}
.s-bg-6 {
  background: linear-gradient(342deg, #DFE5F6 36%, #FFFFFF 50%);
}
.img-person {
  position: absolute;
  bottom: 0;
  right: 30px;
  width: 203px;
  height: 333px;
}
.rounded-8 {
  border-radius: 8px;
}
.bg-c-primary {
  background: #155EEF !important;
}
.bg-primary {
  width: 100px;
  height: 32px;
  background: #155EEF;
  border-radius: 16px;
  color: #FFFFFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.applet-left-box {
  position: relative;
  width: 240px;
  height: 600px;
  background: #E8ECF6;
  border-radius: 8px;
}
.applet-left-box .left-menu-items {
  position: relative;
  cursor: pointer;
  margin: 20px 0 0 0 ;
  padding: 0 30px;
  height: 60px;
  color: #212332;
  font-size: 16px;
  z-index: 1;
}
.applet-left-box .left-menu-items.active {
  position: relative;
  background: linear-gradient(270deg, rgba(246, 248, 253, 0) 0%, #FFFFFF 100%);
  color: #155EEF;
}
.applet-left-box .left-menu-items.active::after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -12px;
  width: 6px;
  height: 24px;
  border-radius: 0px 4px 4px 0px;
  background: #155EEF;
}
.applet-left-box .left-img {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 0;
  width: 240px;
  height: 240px;
}
.applet-box {
  position: relative;
  z-index: 1;
  width: 940px;
  height: 600px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 50px 44px 44px 44px;
}
.applet-c-box {
  width: 180px;
  height: 372px;
  background: linear-gradient(180deg, #FFFFFF 0%, #E8ECF6 100%);
  border-radius: 8px;
  padding: 70px 20px 60px 20px;
}
.img-250 {
  width: 250px;
  height: 250px;
}
.img-gy {
  width: 260px;
  height: 171px;
}
.img-xhs {
  width: 296px;
  height: 373px;
}
.ai-box {
  position: absolute;
  right: 26px;
  width: 710px;
  height: 110px;
  background: linear-gradient(90deg, #FFFFFF 0%, #E8ECF6 100%);
  padding: 24px 48px;
  margin-right: -70px;
}
.ai-box-1 {
  top: 100px;
}
.ai-box-2 {
  top: 230px;
}
.-mr-70 {
  margin-right: -70px;
}
.bg-e5e9f3 {
  background: #e5e9f3;
}
.edu-1 {
  background: #e5e9f3;
}
.edu-2-box {
  width: 380px;
  height: 463px;
  background: linear-gradient(180deg, #D5E5FF 0%, #FFFFFF 50%);
  border-radius: 12px;
  padding: 40px 20px;
}
.edu-2-box .edu-title {
  color: #155EEF;
}
.edu-2-box-1 {
  background: linear-gradient(180deg, #CCEDF6 0%, #FFFFFF 50%);
}
.edu-2-box-1 .edu-title {
  color: #138AA7;
}
.edu-2-box-2 {
  background: linear-gradient(180deg, #E6E4FA 0%, #FFFFFF 50%);
}
.edu-2-box-2 .edu-title {
  color: #4338B3;
}
.icon-66 {
  width: 66px;
  height: 66px;
}
.icon-90 {
  width: 86px;
  height: 90px;
}
.border-t-1 {
  border-top: 1px solid #EBEBEB;
}
.border-l-1 {
  border-left: 1px solid #FFFFFF;
}
.line-h-16 {
  line-height: 16px !important;
}
.line-h-20 {
  line-height: 20px !important;
}
.line-h-24 {
  line-height: 24px;
}
.line-h-32 {
  line-height: 32px;
}
.edu-3-1 {
  background: #155EEF;
  color: #FFFFFF;
  height: 260px;
}
.edu-3-item {
  width: 260px;
  height: 60px;
}
.border-r-1 {
  border-right: 1px solid #FFFFFF;
}
.edu-3-2-item {
  width: 260px;
}
.edu-4 {
  width: 1200px;
  height: 550px;
  border-radius: 12px;
}
.edu-4-p-a {
  position: absolute;
  top: -50px;
  left: 50%;
  margin-left: -600px;
  width: 1200px;
  height: 600px;
}
.img-550 {
  position: absolute;
  bottom: -95px;
  right: 10px;
  width: 550px;
  height: 470px;
}
.img-735 {
  position: absolute;
  right: 44px;
  bottom: -68px;
  width: 735px;
  height: 378px;
}
.edu-5-item {
  position: relative;
  box-sizing: border-box;
  width: 240px;
  height: 300px;
  padding: 40px 0 0 36px;
  color: #212332;
  z-index: 1;
}
.edu-5-icon {
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
  width: 180px;
  height: 180px;
}
.edu-6 {
  position: absolute;
  z-index: 0;
  width: 1440px;
  height: 566px;
  bottom: -466px;
  left: 50%;
  margin-left: -720px;
}
.transform-90 {
  transform: rotateX(90deg);
}
.transform-180 {
  transform: scaleX(-1);
}
.transform-180-v {
  transform: scaleY(-1);
}
/*TODO nav*/
.header{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #212332;
  background: rgba(255,255,255,0.3);
  padding-left: 24px;
  padding-right: 24px;
}
.headerScroll{
  background: #fff;
}
.logo{
  min-width: 70px;
  min-height: 26px;
  img{
    width: 100%;
    height: 100%;
  }
}
.navList{
  a{
    padding-left: 40px;
    color: #212332;
    text-decoration: none;
  }
  a:first-child{
    padding-left: 50px;
  }
  a:hover,a.active,.router-link-active{
    font-weight: bold;
    color: #155EEF;
  }
}

/*TODO content*/
.bg-1{
  height: 600px;
  background: #E4E9F4;
  background-image: url("/assets/images/index/AiH-bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom right;
}
.bg-2{
  height: 800px;
  background-image: url("/assets/images/index/hybrid-bg-2.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top left;
}
.bg-3{
  width: 100%;
  height: 1600px;
  background-image: url("/assets/images/index/aiH-bg-3.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top left;
}
.itd-2-box{
  width: 230px;
  height: 424px;
  background: #FFFFFF;
  border-radius: 8px;
  position: relative;
  padding: 38px 30px 0 30px;
  img{
    position: absolute;
    bottom: 0;
    right: 0;
  }
}

.size-180{
  width: 180px;
  height: 180px;
}
.fn-1-box{
  width: 384px;
  height: 224px;
  padding: 30px 42px 0 42px;
}
.fn-1-bg{
  background: url('/assets/images/index/aiH-fn-1.png');
  background-size: 100% 100%;
}
.fn-2-bg{
  background: url('/assets/images/index/aiH-fn-2.png');
  background-size: 100% 100%;
}
.fn-3-bg{
  background: url('/assets/images/index/aiH-fn-3.png');

  background-size: 100% 100%;
}
.text-v{
  writing-mode: vertical-rl;
  text-orientation: upright;
  letter-spacing: 0.3em;
}
.fn-box{
  background: #F5F7FC;
  border-radius: 8px;
  border: 1px solid #EBEBEB;
  padding: 10px 20px;
}
.fn-3-title{
  width: 100%;
  text-align: center;
  line-height: 40px;
  height: 40px;
  background: #155EEF;
  border-radius: 20px 0px 20px 0px;
  font-size: 16px;
  font-weight: 400;
  color: #FFFFFF;
  margin-top: -28px;
}
.fn-c-box{
  box-sizing: border-box;
  height: 212px;
  border-radius: 8px;
  border: 1px dashed #155EEF;
  padding: 18px 10px 20px 20px;
}
.h-198{
  height: 198px !important;
}
.h-150{
  height: 150px !important;
}
.h-194{
  height: 194px;
}
.h-217{
  height: 217px;
}
.h-244{
  height: 244px !important;
}
.h-400{
  height: 401px !important;
}
.h-450{
  height: 450px;
}
.w-604{
  width: 604px;
}
.w-572{
  width: 572px;
}
.w-360{
  width: 360px;
}
.w-328{
  width: 328px !important;
}
.w-284{
  width: 284px !important;
}
.w-260{
  width: 260px;
}
.w-250{
  width: 250px;
}
.w-226{
  width: 226px;
}
.w-206{
  width: 198px;
}
.w-178{
  width: 178px;
}
.w-155{
  width: 153px;
}
.w-128{
  width: 128px;
}
.w-106{
  width: 106px;
}
.w-100{
  width: 98px;
}
.w-96{
  width: 94px;
}
.w-88{
  width: 86px;
}
.w-64{
  width: 64px;
}
.text-box{
  box-sizing: border-box;
  height: 40px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #EBEBEB;
}
.text-box-p{
  height: 30px;
  color: #FFFFFF;
  background: #155EEF;
  border-radius: 8px;
}
.p-12{
  padding: 12px;
}
.fn-3-x{
  font-size: 16px;
  font-weight: 600;
  color: #155EEF;
  background: #EFF3FF;
  border-radius: 8px;
  border: 1px solid #155EEF;
}


.value-box{
  width: 100%;
  height: 570px;
  background-image: url("/assets/images/index/value-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top left;
}
.value-bg{
  height: 400px;
  background-image: url("/assets/images/index/bg-v-pro.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top left;
}
.btn-white{
  width: 104px;
  height: 44px;
  text-align: center;
  line-height: 44px;
  background: #FFFFFF;
  border-radius: 8px;
}
.w-440{
  width: 440px;
}
.p-40 {
  padding: 40px;
}
.p-50 {
  padding: 50px;
}


.line{
  width: 235px;
  height: 1px;
  border: 1px solid #E2E2E2;
  margin-right: 50px;
  margin-left: 50px;
}
.slider-container {
  width: 1440px;
  margin: 0 auto;
  white-space: nowrap;
  position: relative;
}

.slider {
  overflow-x: auto;
  -ms-overflow-style:none;
  scrollbar-width: none;
}
.slider::-webkit-scrollbar{
  display: none;
}
.slider img {
  /*width: 228px;*/
  height: 90px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #EBEBEB;
  margin-right: 15px; /* 图片之间的间距 */
  margin-top: 15px;
}
.leftMark{
  position: absolute;
  left: 0;
  top: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient( 270deg, rgba(245,247,252,0) 0%, #F5F7FC 100%);
}
.rightMark{
  width: 100px;
  height: 100%;
  background: linear-gradient( 270deg, #F5F7FC  0%, rgba(245,247,252,0) 100%);
  position: absolute;
  right: 0;
  top: 0;
}

.foot-content{
  display: flex;
  .foot-left{
    display: flex;
    flex-direction: column;
    .foot-text{
      font-size: 34px;
      font-weight: 600;
    }
  }
  .foot-right{
    .footer-item{
      padding-left: 120px;
      .font-main{
        font-weight: 600;
        font-size: 16px;

      }
    }
  }
}


.dropdown-toggle {
  display: inline-block;
  /*line-height: @nav-height;*/
  &:after {
    display: none;
  }
}

.login-info{
  display: flex; flex-direction: row;align-items: flex-end;align-content: center;
  a{
    display: flex;align-content: center;align-items: center;
    i{
      color: #155EEF;font-weight: bold;margin-right: 10px;height:24px;line-height: 24px;
    }
  }
  .dropdown{
    max-width:200px;display:flex;
    .menu-item{
      display:flex;
      align-content: flex-end;
    }
  }
}