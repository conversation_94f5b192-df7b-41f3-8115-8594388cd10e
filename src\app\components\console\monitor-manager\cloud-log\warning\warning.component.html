<div class="table-content">
    <ol class="on-breadcrumb">
        <li><a routerLink="../">云日志</a></li>
        <li><span>警告日志</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">警告日志</h3>
        </div>
        <div class="details-container">
            <!--内容菜单-->
            
                <nz-tabset [nzTabPosition]="'top'" [nzType]="'line'" (nzSelectChange)="changMenu($event)"
                    [nzSelectedIndex]="activeContentIndex">
                    <nz-tab *ngFor="let tab of contentMenuOptions" [nzTitle]="tab.title">
                    </nz-tab>
                </nz-tabset>
            
            <!-- 告警策略 -->
            <div class="content-body-item"  *ngIf="activeContentIndex === 0">
                <div class="action-bar clearfix">
                    <button nz-button nzType="primary"
                        (click)="make()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新建
                    </button>
                    <span>&nbsp;&nbsp;&nbsp;</span>
                    <button nz-button nzType="primary">
<!--                        (click)="keyPairModalVisible = true">-->
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                         暂停
                     </button>
                     <span>&nbsp;&nbsp;&nbsp;</span>
                    <button nz-button nzType="primary">
<!--                        (click)="keyPairModalVisible = true">-->
                        <i nz-icon nzType="plus"  nzTheme="outline"></i>
                        恢复
                    </button>
                    <span>&nbsp;&nbsp;&nbsp;</span>
                    <button nz-button nzType="primary">
<!--                        (click)="keyPairModalVisible = true">-->
                        <i nz-icon nzType="delete"  nzTheme="outline"></i>
                        删除
                    </button>
                </div>
                 <nz-table #symptom [nzPageSize]=99999
                    [nzShowPagination]=false [nzData]="">
                    <thead>
                        <tr>
                            <th
                                nzShowCheckbox
                                [(nzChecked)]="isAllDisplayDataChecked"
                                [nzIndeterminate]="isIndeterminate"
                                (nzCheckedChange)="checkAll($event)">
                            </th>
                            <ng-container *ngFor="let col of cols">
                                <th
                                  *ngIf="col.width"
                                  nz-resizable
                                  nzBounds="window"
                                  nzPreview
                                  nzColumnKey="col.ColumnKey"
                                  [nzWidth]="col.width"
                                  [nzMaxWidth]="400"
                                  [nzMinWidth]="60"
                                  [nzSortFn]="true"
                                  (nzResizeEnd)="onResize($event, col.title)"
                                >
                                  {{ col.title }}
                                  <nz-resize-handle nzDirection="right">
                                    <div class="resize-trigger"></div>
                                  </nz-resize-handle>
                                </th>
                                <th *ngIf="!col.width" style="min-width: 120px;">
                                  {{ col.title }}
                                </th>
                              </ng-container>
                            <!-- <th>告警名称</th>
                            <th>监控资源类型</th>
                            <th>策略类型</th>
                            <th>当前状态</th>
                            <th>统计周期</th>
                            <th>创建时间</th>
                            <th>操作</th> -->
                        </tr>
                    </thead>
                    <tbody>
                    <!--    <tr *ngFor="let item of symptom.data">
                         <td
                            nzShowCheckbox
                            [(nzChecked)]="mapOfCheckedId[data.id]"
                            [nzDisabled]="data.disabled"
                            (nzCheckedChange)="refreshStatus()">
                        </td>
                            <td>{{ item.symptomDefName || '-' }}</td>
                            <td>
                                <i class="fa fa-warning {{getStatusClass(item)}}"></i>
                                <span
                                    class="margin-span">{{ item.symptomDefinition ? getDegreeText(item) : '-' }}</span>
                            </td>
                            <td>
                                <span class="margin-span">{{item.metricName || '-'}}</span>
                                <span
                                    class="margin-span">{{ item.symptomDefinition ? getOperatorText(item) : '-' }}</span>
                                <span class="margin-span">{{ item.symptomDefinition ? getNumber(item) : '-' }}</span>
                            </td> -->
                            <!--更多操作-->
                            <!-- <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createSymptom(item)">
                                        <i nzTitle="修改" nzPlacement="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzPlacement="top"
                                        nzTitle="确定要删除该告警症状吗？" (nzOnConfirm)="deleteSymptom(item)">
                                        <i nzTitle="删除" nzPlacement="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr> -->
                    </tbody>
                </nz-table>
            </div>
            
            <!-- 历史警告 -->
            <div class="content-body-item" *ngIf="activeContentIndex === 1">
                <div class="action-bar clearfix">
                    <form nz-form nzLayout="inline" (ngSubmit)="search()" style="height: 32px;">
                        <input type="text" name="ipAddres" autocomplete="off" [ngModelOptions]="{standalone: true}"
                            [(ngModel)]="ipAddres" nz-input placeholder="请输入实例名称" style="width: 200px; margin-right: 10px;" />
                        <nz-range-picker [(ngModel)]="dateTime" [nzFormat]="dateFormat" name="dateTime"></nz-range-picker>
                        <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;">
                            <i nz-icon nzType="search"></i>
                            查询
                        </button>
                    </form>
                </div>
                <!--内容-->
                <nz-table #alertDef [nzPageSize]=99999 [nzLoading]=tableLoading
                [nzShowPagination]=false [nzData]="">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols1">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzColumnKey="col.ColumnKey"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="400"
                              [nzMinWidth]="60"
                              [nzSortFn]="true"
                              (nzResizeEnd)="onResize1($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                        <!-- <th>实例名称</th>
                        <th>资源类型</th>
                        <th>告警策略</th>
                        <th>监控指标值</th>
                        <th>告警规则</th>
                        <th>告警类型</th>
                        <th>生产时间</th>
                        <th>删除时间</th> -->
                    </tr>
                </thead>
             <!--      <tbody>
                        <tr *ngFor="let item of symptom.data">
                            <td>{{ item.name || '-' }}</td>
                            <td>{{ item.alertDefinitionNames.join('，') || '-' }}</td>
                            <td>{{ item.groupName || '-' }}</td>
                            <td>{{ item.remark || '-' }}</td> -->

                            <!--更多操作-->
                            <!-- <td>
                                <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item" (click)="createNotification(item)">
                                        <i nzTitle="修改" nzPlacement="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                                    </div>
                                    <div class="on-table-action-item" nz-popconfirm nzPlacement="top"
                                        nzTitle="确定要删除该告警规则吗？" (nzOnConfirm)="deleteNotification(item)">
                                        <i nzTitle="删除" nzPlacement="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                    </div>
                                </div>
                                <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                    <div class="action-loading-placeholder">
                                        <i class="icon" nz-icon [nzType]="'loading'"></i>
                                        {{ getBusyText(item.id) }}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>-->
                </nz-table> 
            </div>
        </div>
    </div>
</div>

<!-- 创建&编辑 -->
<nz-modal [nzWidth]="600" [(nzVisible)]="creatWaring"
    [nzMaskClosable]="false"
    [nzTitle]="'新建告警策略'"
    [nzCancelText]='clear'
    [nzOkText]="ok" small
    (nzOnCancel)="creatWaring = false"
    [nzBodyStyle]="{padding: '8px'}">
    <ng-container *nzModalContent>
    <nz-steps nzType="navigation" [nzCurrent]="current" nzSize="small">
        <nz-step nzTitle="参数设置"></nz-step>
        <nz-step nzTitle="告警规则"></nz-step>
    </nz-steps>

    <form [formGroup]="warning"
        class="config-content sm">
        <div class="field-group" *ngIf="!expression">
            <div class="field-item required">
                <label>
                    <span class="label-text">规则名称</span>
                    <input nz-input type="text"
                        maxlength="50" placeholder="请输入规则名称">
                </label>
                <!-- <div *ngIf="isInvalid(monitorRule.get('name'))"
                    class="form-hint error">
                    <div
                        *ngIf="monitorRule.get('name').hasError('required')">
                        规则名称不能为空
                    </div>
                    <div
                        *ngIf="monitorRule.get('name').hasError('maxlength')">
                        规则名称长度不能超过{{ monitorRule.get('name').errors.maxlength.requiredLength }}个字符
                    </div>
                </div> -->
            </div>
            <div class="field-item">
                <label>
                    <span class="label-text">资源类型 :</span>
                    <nz-select nzPlaceHolder="请选择资源类型">
                        <nz-option>云日志</nz-option>
                    </nz-select>
                </label>
            </div>
            <div class="field-item">
                <label>
                    <span class="label-text">统计周期 :</span>
                    <nz-select nzPlaceHolder="请选择统计周期">
                        <nz-option>30分钟</nz-option>
                    </nz-select>
                </label>
            </div>
        </div>
        <div class="field-group" *ngIf="expression" style="padding-left: 0px;">
            <nz-table #basicTable [nzData]="listOfData" [nzShowPagination]="false">
                <thead>
                  <tr>
                    <th>监控指标</th>
                    <th>条件</th>
                    <th>关键字</th>
                    <th>重试次数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of basicTable.data">
                    <td><label>
                        <nz-select nzPlaceHolder="日志关键字" style="width: 133.88px;">
                            <nz-option></nz-option>
                        </nz-select>
                    </label></td>
                    <td><label>
                        <nz-select nzPlaceHolder="等于" style="width: 95.61px;">
                            <nz-option></nz-option>
                        </nz-select>
                    </label></td>
                    <td><label>
                        <input nz-input type="text"
                            maxlength="50" placeholder="请输入关键字" style="width: 133.88px;">
                    </label></td>
                    <td><label>
                        <nz-select nzPlaceHolder="1" style="width: 95.61px;">
                            <nz-option></nz-option>
                        </nz-select>
                    </label></td>
                  </tr>
                </tbody>
              </nz-table>
            <!-- <span>*新增关键字不能包含特殊字符，<>《》@+ - && || ! ( ) { } [ ] ^ " ~ * ? :，;</span> -->
        </div>
    </form>
    </ng-container>

    <div *nzModalFooter>
        <button *ngIf="expression" nz-button nzType="default" (click)="cancel()">{{ clear }}</button>
        <button nz-button nzType="primary" (click)="nextStep()">{{ ok }}</button>
    </div>
</nz-modal>