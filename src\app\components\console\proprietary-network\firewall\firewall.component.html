<div class="table-content">
    <ol class="on-breadcrumb">
        <li><span>专有网络</span></li>
        <li><span>防火墙</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">防火墙</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="keyword" nz-input
                            placeholder="请输入网络名称" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <button nz-button nzType="primary"
                        (click)="isArchiveUser === 'true' ? null : editFirewall(null)"
                        [ngClass]="{'disabled': isArchiveUser === 'true'}">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建防火墙规则
                    </button>
                </div>
            </div>
            <nz-table #vpcs style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                [nzData]="firewallList">
            <thead>
                    <tr>
                        <th width="10%">名称</th>
                        <th width="20%">源地址</th>
                        <th width="10%">源端口</th>
                        <th width="10%">协议</th>
                        <th width="20%">目标地址</th>
                        <th width="10%">目标端口</th>
                        <th width="7%">方向</th>
                        <th width="13%">操作</th>
                    </tr>
                </thead>
                <tbody>

                    <tr *ngFor="let item of firewallList; let i = index; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td >
                            <span *ngFor="let date of item.source.split(','); let i = index">{{item.source.split(',')[i]}}<br/></span>
                        </td>
                        <td>{{ item.sourcePort }}</td>
                        <td>{{ policyTypeMap[item.policyType] }}</td>
                        <td>
                            <span *ngFor="let date of item.destination.split(','); let i = index">{{item.destination.split(',')[i]}}<br/></span>
                        </td>
                        <td>{{ item.destinationPort }}</td>
                        <td>{{ item.direction }}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                     (click)="editFirewall(item)">
                                    <i nzTitle="编辑"
                                       nzPlacement="bottom"
                                       nz-tooltip
                                       class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzPlacement="top"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                    [title]="isArchiveUser === 'true' ? null : '确定要删除该防火墙规则吗？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteFirewall(item);">
                                    <i nzTitle="删除"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                                <div class="on-table-action-item" [ngClass]="{'disabled': moveStatus[item.id] ||i ==0}"
                                     (click)="uprecord(item)">
                                    <i nzTitle="向上移动"
                                       nzPlacement="bottom"
                                       nz-tooltip
                                       class="icon fa fa-arrow-up"></i>
                                </div>
                                <div class="on-table-action-item" [ngClass]="{'disabled': moveStatus[item.id]||i ==pager.pageSize||i== pager.total%pager.pageSize-1}"
                                     (click)="downrecord(item)">
                                    <i nzTitle="向下移动"
                                       nzPlacement="bottom"
                                       nz-tooltip
                                       class="icon fa fa-arrow-down"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="firewallList.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!--新建监控大盘弹框-->
<nz-modal [(nzVisible)]="showCreateWindow" [nzTitle]="showCreateWindowTitle"
          (nzOnCancel)="handleCancel()"
          [nzOkLoading]="isMdLoading"
          (nzOnOk)="createOrUpdateFirewall()" [nzWidth]="580">
    <ng-container *nzModalContent>
    <form [formGroup]="firewallItem" class="config-content md network-form">
        <section class="field-section">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">规则名称</span>
                        <input nz-input type="text" formControlName="name" maxlength="50" placeholder="请输入规则名称">
                    </label>
                    <div *ngIf="isInvalid(firewallItem.get('name'))"
                         class="form-hint error">
                        <div
                                *ngIf="firewallItem.get('name').hasError('required')">
                            规则名称不能为空
                        </div>
                        <div
                                *ngIf="firewallItem.get('name').hasError('pattern')">
                            规则名称必须以字母开头，只能输入英文、数字、中划线
                        </div>
                        <div
                                *ngIf="firewallItem.get('name').hasError('maxlength')">
                            规则名称长度不能超过{{ firewallItem.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">协议</span>
                        <nz-select formControlName="policyType" nzPlaceHolder="请选择查询防火墙协议">
                            <nz-option *ngFor="let item of policyTypeList"
                                       [nzValue]="item.name"
                                       [nzLabel]="item.title"></nz-option>
                        </nz-select>
                    </label>
                    <div *ngIf="isInvalid(firewallItem.get('policyType'))"
                         class="form-hint error">
                        <div *ngIf="firewallItem.get('policyType').hasError('required')">
                            请选择协议
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">源IP</span>
                        <input nz-input type="text" [readOnly]="isSourceDisabled" [disabled]="isSourceDisabled" formControlName="source" maxlength="150" placeholder="多个IP请用逗号分隔">
                    </label>
                    <label nz-checkbox [ngModel]="isSourceAny" style="margin-left: 10px;"  [ngModelOptions]="{standalone: true}" (ngModelChange)="checkSourceButton()">任意来源</label>
                    <div *ngIf="isInvalid(firewallItem.get('source'))"
                         class="form-hint error">
                        <div *ngIf="firewallItem.get('source').hasError('required') && !isSourceAny">
                            源IP不能为空
                        </div>
                        <div *ngIf="firewallItem.get('source').hasError('pattern')  && !isSourceAny">
                            请输入正确的IP地址，多个IP用逗号分隔
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">源端口</span>
                        <input nz-input type="text" formControlName="sourcePort" maxlength="150" placeholder="多个端口请用逗号分隔">
                    </label>
                    <div *ngIf="isInvalid(firewallItem.get('sourcePort'))"
                         class="form-hint error">
                        <div *ngIf="firewallItem.get('sourcePort').hasError('pattern')">
                            源端口的取值范围是:0-65535，多个源端口请用逗号分隔
                        </div>
                        <div *ngIf="firewallItem.get('sourcePort').hasError('maxlength')">
                            源端口长度不能超过{{ firewallItem.get('sourcePort').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">目标IP</span>
                        <input nz-input type="text" formControlName="destination" [disabled]="isDestinationDisabled" [readOnly]="isDestinationDisabled" maxlength="150" placeholder="多个IP请用逗号分隔">
                    </label>
                    <label nz-checkbox [ngModel]="isDestinationAny" style="margin-left: 10px;" [ngModelOptions]="{standalone: true}" (ngModelChange)="checkDestinationButton()">任意目标</label>
                    <div *ngIf="isInvalid(firewallItem.get('destination'))"
                         class="form-hint error">
                        <div *ngIf="firewallItem.get('destination').hasError('required')  && !isDestinationAny">
                            目标IP不能为空
                        </div>
                        <div *ngIf="firewallItem.get('destination').hasError('pattern')  && !isDestinationAny">
                            请输入正确的IP地址，多个IP用逗号分隔
                        </div>
                        <div *ngIf="firewallItem.get('destination').hasError('maxlength')">
                            目标IP长度不能超过{{ firewallItem.get('destination').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">目标端口</span>
                        <input nz-input type="text" formControlName="destinationPort" maxlength="150" placeholder="多个端口请用逗号分隔">
                    </label>
                    <div *ngIf="isInvalid(firewallItem.get('destinationPort'))"
                         class="form-hint error">
                        <div *ngIf="firewallItem.get('destinationPort').hasError('pattern')">
                            目标端口的取值范围是:0-65535，多个目标端口请用逗号分隔
                        </div>
                        <div *ngIf="firewallItem.get('destinationPort').hasError('maxlength')">
                            目标端口长度不能超过{{ firewallItem.get('destinationPort').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">方向</span>
                        <nz-select [ngModelOptions]="{standalone: true}" [(ngModel)]="direction" nzPlaceHolder="请选择防火墙方向">
                            <nz-option nzValue="IN" nzLabel="入方向"></nz-option>
                            <nz-option nzValue="OUT" nzLabel="出方向"></nz-option>
                            <nz-option nzValue="IN_OUT" nzLabel="双向"></nz-option>
                        </nz-select>
                    </label>
                    <div *ngIf="isInvalid(firewallItem.get('direction'))"
                         class="form-hint error">
                        <div *ngIf="firewallItem.get('direction').hasError('required')">
                            请选择方向
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>
