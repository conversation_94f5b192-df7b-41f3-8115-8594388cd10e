<div class="table-content">
    <ol class="on-breadcrumb">
        <li><a routerLink="../">对象存储</a></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">
              账户信息
            </h3>
            <div class="on-desc" style="margin-top: 30px;">
                <div class="on-desc-body" *ngIf="accountFlag ==true">
                    <div nz-row class="on-desc-row" style="text-align: center;font-size: 13px;">
                        <div  style="width: 35%;">
                            <div style="font-weight: bold;font-size: 20px;padding: 10px 0 0 10px; text-align: left">
                                <p>配额: <span style="font-size: 28px;margin-left: 29px;">{{this.userInfo.totalSizeKb?(userInfo.totalSizeKb):''}}</span> GB</p>
                                <p>已使用：<span style="font-size: 28px;">{{this.userInfo.totalUsagedSizeBytes}}</span> GB</p>
                            </div>
                        </div>
                        <div  style="width: 65%;font-size: 18px; text-align: left;">
                            <table>
                                <tr>
                                    <td>公网访问：</td>
                                    <td>https://{{userInfo.os_public_access_url}}</td>
                                </tr>
                                <tr>
                                    <td>内网访问：</td>
                                    <td>https://{{userInfo.os_private_access_url}}</td>
                                </tr>
                                <tr>
                                    <td>Access Key：</td>
                                    <td>{{userInfo.accessKeyFlag ?userInfo.accessKey : "***" }}
                                        <i class="suffix" [title]="userInfo.accessKeyFlag ? '隐藏' : '查看'" nz-icon
                                           [nzType]="userInfo.accessKeyFlag ? 'eye-invisible' : 'eye'"
                                           (click)="userInfo.accessKeyFlag = !userInfo.accessKeyFlag" style="margin-bottom: 5px;"></i></td>
                                </tr>
                                <tr>
                                    <td>Secret Key：</td>
                                    <td>{{userInfo.secretKeyFlag ?userInfo.secretKey : "***" }}
                                        <i class="suffix" [title]="userInfo.secretKeyFlag ? '隐藏' : '查看'" nz-icon
                                           [nzType]="userInfo.secretKeyFlag ? 'eye-invisible' : 'eye'"
                                           (click)="userInfo.secretKeyFlag = !userInfo.secretKeyFlag" style="margin-bottom: 5px;"></i></td>
                                </tr>
                            </table>
                            <!--<div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">公网访问</div>
                            <div class="on-desc-content">{{userInfo.os_public_access_url}}</div>
                            <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">内网访问</div>
                            <div class="on-desc-content">{{userInfo.os_private_access_url}}</div>
                            <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">Access Key</div>
                            <div class="on-desc-content">{{userInfo.accessKeyFlag ?userInfo.accessKey : "***" }}
                                <i class="suffix" [title]="userInfo.accessKeyFlag ? '隐藏' : '查看'" nz-icon
                                   [nzType]="userInfo.accessKeyFlag ? 'eye-invisible' : 'eye'"
                                   (click)="userInfo.accessKeyFlag = !userInfo.accessKeyFlag" style="margin-bottom: 5px;"></i>
                            </div>
                            <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">Secret Key</div>
                            <div class="on-desc-content">{{userInfo.secretKeyFlag ?userInfo.secretKey : "***" }}
                                <i class="suffix" [title]="userInfo.secretKeyFlag ? '隐藏' : '查看'" nz-icon
                                   [nzType]="userInfo.secretKeyFlag ? 'eye-invisible' : 'eye'"
                                   (click)="userInfo.secretKeyFlag = !userInfo.secretKeyFlag" style="margin-bottom: 5px;"></i></div>-->
                        </div>
                    </div>
                   <!-- <div nz-row class="on-desc-row" style="text-align: center;font-size: 13px;">
                        <div nz-col nzSpan="8">
                            <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">Access Key</div>
                            <div class="on-desc-content">{{userInfo.accessKeyFlag ?userInfo.accessKey : "***" }}
                                <i class="suffix" [title]="userInfo.accessKeyFlag ? '隐藏' : '查看'" nz-icon
                                   [nzType]="userInfo.accessKeyFlag ? 'eye-invisible' : 'eye'"
                                   (click)="userInfo.accessKeyFlag = !userInfo.accessKeyFlag" style="margin-bottom: 5px;"></i>
                            </div>
                        </div>
                        <div nz-col nzSpan="12">
                            <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold">Secret Key</div>
                            <div class="on-desc-content">{{userInfo.secretKeyFlag ?userInfo.secretKey : "***" }}
                                <i class="suffix" [title]="userInfo.secretKeyFlag ? '隐藏' : '查看'" nz-icon
                                   [nzType]="userInfo.secretKeyFlag ? 'eye-invisible' : 'eye'"
                                   (click)="userInfo.secretKeyFlag = !userInfo.secretKeyFlag" style="margin-bottom: 5px;"></i></div>
                        </div>
                        <div nz-col nzSpan="2" style="position: absolute;right: 40px;width: 240px">
                            <div class="on-desc-title" style="margin-bottom: 10px;font-weight: bold;">配额</div>
                            <div class="on-desc-content">
                                {{this.userInfo.totalSizeKb?userInfo.totalSizeKb+'GB':''}}
                            </div>
                        </div>
                    </div>-->
                </div>
                <div class="on-desc-body" *ngIf="accountFlag ==false">
                    请先开通对象存储服务
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
              <!--<div class="pull-right" *ngIf="isAdmin === 'true'">
                <a nz-button nzType="primary"
                   [routerLink]="'../object-storage-config'">
                  <i nz-icon nzType="plus"
                     nzTheme="outline"></i>
                  创建桶
                </a>
              </div>-->
              <div class="pull-right" *ngIf="isAdmin === 'false' && accountFlag ==true">
                <button nz-button nzType="primary"
                   [ngClass]="{'disabled': isArchiveUser === 'true' && keyFlag}"
                   (click)="createModalVisible = true">
                  <i nz-icon nzType="plus"
                     nzTheme="outline"></i>
                  创建桶
                </button>
              </div>
               <!-- <div class="pull-right" [routerLink]="['../object-storage-detail',1]">
                    <i nzTooltipTitle="查看"
                       nzTooltipContent="bottom"
                       nz-tooltip
                       class="icon fa fa-search"></i>
                </div>-->
            </div>
            <nz-table #buckets style="overflow-x: auto"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzData]="bucketList">
            <thead>
                <tr>
                  <ng-container *ngFor="let col of cols">
                    <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            [nzWidth]="col.width"
                            [nzMaxWidth]="300"
                            [nzMinWidth]="60"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                    >
                      {{ col.title }}
                      <nz-resize-handle nzDirection="right">
                        <div class="resize-trigger"></div>
                      </nz-resize-handle>
                    </th>
                    <th *ngIf="!col.width" style="min-width: 120px;">
                      {{ col.title }}
                    </th>
                  </ng-container>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of buckets.data; trackBy: trackById">
                    <td>{{item.name}}</td>
                    <td>
                        <div class="on-table-actions">
                            <div class="on-table-action-item" [routerLink]="['../object-storage-detail',item.id]">
                              <i nzTooltipTitle="查看"
                                 nzTooltipContent="bottom"
                                 nz-tooltip
                                 class="icon fa fa-search"></i>
                            </div>
                            <div class="on-table-action-item"
                                 nz-popconfirm
                                 nzTooltipContent="top"
                                 nzTitle="确定要删除该桶吗？"
                                 (nzOnConfirm)="deleteBucket(item);">
                              <i nzTooltipTitle="删除"
                                 nzTooltipContent="bottom"
                                 nz-tooltip
                                 class="icon fa fa-trash-o">
                              </i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                             [hidden]="!busyStatus[item.id]">
                            <div
                                    class="action-loading-placeholder">
                              <i class="icon" nz-icon
                                 [nzType]="'loading'"></i>
                              {{ getBusyText(item) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="buckets.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
            </tbody>
            </nz-table>
      </div>
</div>
</div>
    <nz-modal [(nzVisible)]="createModalVisible" nzTitle="创建桶"
              nzOkText="创建" [nzOkLoading]="isCreating"
              [nzBodyStyle]="{padding: '8px'}"
              (nzOnCancel)="hideCreateModal()"
              [nzWidth]="450"
              (nzOnOk)="createBucket()">
        <ng-container *nzModalContent>
        <form [formGroup]="storage" class="config-content sm">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">名称</span>
                        <input maxlength="63" type="text" nz-input formControlName="name" placeholder="请输入桶名称"/>
                        <p class="small tip label-padding tip-p">名称长度为3~63个字符，仅允许包含小写字母, 数字和-，</p>
                        <p class="small tip label-padding tip-p">需要以小写字母或数字开头和结尾。</p>
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(storage.get('name'))">
                        <div *ngIf="storage.get('name').hasError('required')">
                            名称不能为空
                        </div>
                        <div *ngIf="storage.get('name').hasError('maxlength') || storage.get('name').hasError('minlength')">
                            名称长度为3~63个字符
                        </div>
                        <div *ngIf="storage.get('name').hasError('pattern')">
                            名称不符合规范
                        </div>
                    </div>
                </div>
            </div>
        </form>
        </ng-container>
    </nz-modal>
