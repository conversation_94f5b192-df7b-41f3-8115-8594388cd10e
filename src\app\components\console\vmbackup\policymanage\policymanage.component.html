<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">备份</a></li>-->
<!--        <li><span>备份策略</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">备份策略</h3>
         </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="keyword" nz-input
                            placeholder="按照名称或ID搜索" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <button nz-button nzType="primary"
                        [ngClass]="{'disabled': isArchiveUser === 'true'}"
                        (click)="isArchiveUser === 'true' ? null : showCreateModal()">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建备份规则
                    </button>
                </div>

            </div>
            <nz-table #policy style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="policyList">
            <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>

                    <tr *ngFor="let item of policy.data; trackBy: trackById">
                        <td><span style="word-break: break-all; " >{{item.name}}</span></td>
<!--                        <td>{{ item.backupTypeTitle }}</td>-->
                        <td>{{ item.schedFreqTitle }}</td>
                        <td>{{ item.schedRetTitle }}</td>
                        <td>{{ item.schedDateTitle }}</td>
                        <td>{{ item.vmName }}</td>
                        <td>{{ item.policyName }}</td>
                        <td>
                            <span>{{ getDeployStatusText(item) }}</span>
                        </td>
                        <td>
                            <div class="on-table-actions" [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item" (click)="getBackUpList(item);">
                                    <i nzTitle="查看详情" nzPlacement="bottom" nz-tooltip class="icon fa fa-search"></i>
                                </div>
                               <!-- <div class="on-table-action-item" (click)="getDetail(item);">
                                    <i nzTitle="查看详情" nzPlacement="bottom" nz-tooltip class="icon fa fa-search"></i>
                                </div>-->
                                <div class="on-table-action-item" (click)="isArchiveUser === 'true' ? null : showModal(item)"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTitle="编辑" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="item.backupStatus === 'active'" class="on-table-action-item" (click)="deactive(item)">
                                    <i nzTitle="暂停" nzTooltipContent="bottom" nz-tooltip class="icon iconfont icon-guanji"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="item.backupStatus === 'deactive'" class="on-table-action-item" (click)="active(item)">
                                    <i nzTitle="激活" nzTooltipContent="bottom" nz-tooltip class="icon iconfont icon-qidong"></i>
                                </div>
                                <div class="on-table-action-item" nz-popconfirm nzPlacement="top"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                    [nzTitle]="isArchiveUser === 'true' ? null : '删除备份策略会同步删除策略下所有备份项，确定删除？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : showDeletePolicy(item);">
                                    <i nzTitle="删除" nzPlacement="bottom" nz-tooltip class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions" [hidden]="!busyStatus[item.id]">
                                <div class="action-loading-placeholder">
                                    <i class="icon" nz-icon [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="policy.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 编辑备份策略 -->
<nz-modal [(nzVisible)]="isVisible" nzTitle="修改备份策略" (nzOnCancel)="handleCancel()"
(nzOnOk)="updatePolicy()" [nzOkLoading]="isOkLoading" [nzCancelLoading]="isLoading" [nzWidth]="620">
<ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="policyItem" (submit)="updatePolicy()">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">名称</span>
                        <input class="background" required maxlength="50" type="text" formControlName="name" placeholder="请输入名称" readonly
                            [(ngModel)]="policyItem.value.name" (keydown.enter)="keyEnter()" />
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(policyItem.get('name'))">
                        <div *ngIf="policyItem.get('name').hasError('required')">
                            名称不能为空
                        </div>
                        <div *ngIf="policyItem.get('name').hasError('maxlength')">
                            名称长度不能超过{{ policyItem.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>

                <div class="field-item required">
                    <label>
                        <span class="label-text">备份类型</span>
                        <nz-select formControlName="vTBackupTypeVm" nzPlaceHolder="请选择类型" nzDisabled>
                            <nz-option *ngFor="let item of (policyItem.value.vTBackupTypes | keyvalue)"
                                [nzValue]="item.key">{{item.value}}
                            </nz-option>
                        </nz-select>
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(policyItem.get('vTBackupTypeVm'))">
                        <div *ngIf="policyItem.get('vTBackupTypeVm').hasError('required')">
                            请选择类型
                        </div>
                    </div>
                </div>

                <div class="field-item required">
                    <label>
                        <span class="label-text">备份频率</span>
                        <!-- <input nz-input style="width: 280px;" disabled="true" maxlength="50" type="text"
                            value="{{ policyItem.value.vTBackupSchedFreqTypes }}" /> -->

                        <nz-select formControlName="vTBackupSchedFreqType" nzPlaceHolder="请选择备份频率" (ngModelChange)="editTypeChange($event)" nzDisabled>
                            <nz-option *ngFor="let item of (policyItem.value.vTBackupSchedFreqTypes | keyvalue)"
                                [nzValue]="item.key" [nzLabel]="item.value.title">
                            </nz-option>
                        </nz-select>
                    </label>

                    <div class="form-hint error" *ngIf="isInvalid(policyItem.get('vTBackupSchedFreqType'))">
                        <div *ngIf="policyItem.get('vTBackupSchedFreqType').hasError('required')">
                            请选择备份频率
                        </div>
                    </div>
                </div>

                <div class="field-item required">
                    <label>
                        <span class="label-text">保留时间</span>
                        <nz-select formControlName="vTBackupSchedRetTypename" nzPlaceHolder="请选择保留时间" nzDisabled>
                            <nz-option *ngFor="let item of (policyItem.value.vTBackupSchedRetTypes | keyvalue)" [nzValue]="item.key">
                                {{item.value}}
                            </nz-option>
                        </nz-select>
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(policyItem.get('vTBackupSchedRetTypename'))">
                        <div *ngIf="policyItem.get('vTBackupSchedRetTypename').hasError('required')">
                            请选择保留时间
                        </div>
                    </div>
                </div>

                <div class="field-item required" *ngIf="vTBackupSchedFreqTypeName.schedDates?.length != 0">
                    <label>
                        <span class="label-text">备份日期</span>
                        <nz-select formControlName="schedDate" nzPlaceHolder="请选择日期">
                            <nz-option *ngFor="let item of (schedDateList || [])"
                                [nzValue]="item" [nzLabel]="item.title">
                            </nz-option>
                        </nz-select>
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(policyItem.get('schedDate'))">
                        <div *ngIf="policyItem.get('schedDate').hasError('required')">
                            请选择日期
                        </div>
                    </div>
                </div>

                <div class="field-item required">
                    <!-- <label for="">
                        <span class="label-text">备份云主机</span>
                        <nz-select nzShowSearch nzAllowClear formControlName="cloudServerName"
                            nzPlaceHolder="请选择主机">
                            <nz-option *ngFor="let item of (policyItem.value.cloudServers || [])"
                                [nzValue]="item.id" [nzLabel]="item.name">
                            </nz-option>
                        </nz-select>
                    </label> -->
                    <label>
                        <span class="label-text">备份云主机</span>
                        <input class="background" required maxlength="50" type="text" formControlName="cloudServerName" placeholder="请选择主机"
                            [(ngModel)]="policyItem.value.cloudServerName" (keydown.enter)="keyEnter()" readonly/>
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(policyItem.get('cloudServerName'))">
                        <div *ngIf="policyItem.get('cloudServerName').hasError('required')">
                            请选择主机
                        </div>
                    </div>
                </div>
            </div>
    </form>
</ng-container>
</nz-modal>

<!--备份列表-->
<nz-modal [(nzVisible)]="showBackupListWindow" [nzWidth]="'60%'"
          [nzCancelText]=null
          nzOkText="关闭"
          (nzOnCancel)="showBackupListWindow = false"
          (nzOnOk)="colseBackupListWindow()">
	<ng-container *nzModalContent>
    <ng-template #backupListTitleTemplate>
        <span class="modal-title">备份列表</span>
    </ng-template>
    <div>
        <nz-table #backup
                  [nzLoading]="isLoading2"
                  [nzLoadingDelay]="300"
                  [nzFrontPagination]="false"
                  [nzData]="backupList">
            <thead>
            <tr>
                <ng-container *ngFor="let col of backupcols">
                    <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                    >
                        {{ col.title }}
                        <nz-resize-handle nzDirection="right">
                            <div class="resize-trigger"></div>
                        </nz-resize-handle>
                    </th>
                    <th *ngIf="!col.width" style="min-width: 120px;">
                        {{ col.title }}
                    </th>
                </ng-container>
            </tr>
            </thead>
            <tbody>
            <tr
                    *ngFor="let item of backup.data; trackBy: trackById">
                <td>{{item.restoretime || "-"}}</td>
                <td><span>{{item.backup_start_time| date: 'yyyy-MM-dd HH:mm:ss' || "-"}}</span></td>
                <td >{{item.expiration_time| date: 'yyyy-MM-dd HH:mm:ss'  || "-"}}</td>
                <td>{{ item.schedule_type_title || "-"}}</td>
                <td>{{ item.policy_type_title || "-"}}</td>
                <td>{{ item.backup_size || "-"}}</td>
                <td>{{ item.files_number || "-"}}</td>
                <td>
                    <div class="on-table-actions">
                        <div class="on-table-action-item" nz-popconfirm  nzPlacement="top" style="cursor: pointer"
                             [ngClass]="{'disabled': isArchiveUser === 'true'}"
                             [title]="isArchiveUser === 'true' ? null : '本次操作将会覆盖当前虚机，确认将虚机恢复至版本'+item.restoretime+'吗？'"
                             (nzOnConfirm)="isArchiveUser === 'true' ? null : recoverBackup(item);">
                            <i nzTitle="恢复" nzPlacement="bottom" nz-tooltip class="icon iconfont icon-huifu"></i>
                        </div>
                    </div>
                </td>
            </tr>
            <tr [hidden]="backup.data.length || !isLoading2"
                class="loading-placeholder">
                <td colspan="100%"></td>
            </tr>
            </tbody>
        </nz-table>
    </div>
    </ng-container>
</nz-modal>




<!--查看详情-->
<nz-modal [(nzVisible)]="showDetailWindow"
          [nzCancelText]=null
          nzOkText="关闭"
          (nzOnCancel)="showDetailWindow = false"
          (nzOnOk)="showDetailWindow = false">
	<ng-container *nzModalContent>
  <ng-template #detailTitleTemplate>
    <span class="modal-title">备份策略</span>
  </ng-template>
  <div>
    <div class="select-container">
      <span class="select-tips">名称：</span>
      <span class="select-value">{{ policyDetal.name || '-'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">备份类型：</span>
      <span class="select-value">{{ policyDetal.backupTypeTitle || '-'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">备份频率：</span>
      <span class="select-value">{{ policyDetal.schedFreqTitle || '-'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">保留时间：</span>
      <span class="select-value">{{ policyDetal.schedRetTitle || '-'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">备份日期：</span>
      <span class="select-value">{{ policyDetal.schedDateTitle || '-'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">备份云主机：</span>
      <span class="select-value">{{ policyDetal.vmName || '-'}}</span>
    </div>
  </div>
  </ng-container>
</nz-modal>
<!-- 删除提示框 -->
<nz-modal [(nzVisible)]="showDelete"
    nzCancelText="取消"
    nzOkText="确认"
    (nzOnCancel)="showDelete = false"
    [nzOkLoading]="isOkLoading"
    (nzOnOk)="deletePolicy()">
    <ng-container *nzModalContent>
    <span>删除备份策略及备份项不可逆，确定删除？</span>
    </ng-container>
</nz-modal>
<!-- 创建备份规则 -->

<nz-modal [(nzVisible)]="configModalVisible" nzTitle="创建备份策略" (nzOnCancel)="configModalVisible = false"
(nzOnOk)="createPolicymanage()" [nzOkLoading]="isOkLoading" [nzCancelLoading]="isLoading" [nzWidth]="620">
	<ng-container *nzModalContent>
    <form class="config-content md network-form modalForm" [formGroup]="policyItem" (submit)="createPolicymanage()">
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">名称</span>
                    <input required maxlength="50" type="text" formControlName="name" placeholder="请输入名称"
                        [(ngModel)]="policyItem.value.name" (keydown.enter)="keyEnter()" />
                </label>
                <div class="form-hint error" *ngIf="isInvalid(policyItem.get('name'))">
                    <div *ngIf="policyItem.get('name').hasError('required')">
                        名称不能为空
                    </div>
                    <div *ngIf="policyItem.get('name').hasError('maxlength')">
                        名称长度不能超过{{ policyItem.get('name').errors.maxlength.requiredLength }}个字符
                    </div>
                </div>
            </div>

            <div class="field-item required">
                <label>
                    <span class="label-text">备份类型</span>
                    <nz-select formControlName="vTBackupTypeVm" nzPlaceHolder="请选择类型">
                        <nz-option *ngFor="let item of (policyItem.value.vTBackupTypes | keyvalue)"
                            [nzValue]="item.key">{{item.value}}
                        </nz-option>
                    </nz-select>
                </label>
                <div class="form-hint error" *ngIf="isInvalid(policyItem.get('vTBackupTypeVm'))">
                    <div *ngIf="policyItem.get('vTBackupTypeVm').hasError('required')">
                        请选择类型
                    </div>
                </div>
            </div>

            <div class="field-item required">
                <label>
                    <span class="label-text">备份频率</span>
                    <!-- <input nz-input style="width: 280px;" readonly  maxlength="50" type="text"
                        value="{{ vTBackupSchedFreqTypeName.title }}" /> -->
                    <nz-select formControlName="vTBackupSchedFreqType" nzPlaceHolder="请选择备份频率" (ngModelChange)="retTypeChange($event)">
                        <nz-option *ngFor="let item of (policyItem.value.vTBackupSchedFreqTypes | keyvalue)"
                            [nzValue]="item.key" [nzLabel]="item.value.title">
                        </nz-option>
                    </nz-select>
                </label>

                <div class="form-hint error" *ngIf="isInvalid(policyItem.get('vTBackupSchedFreqType'))">
                    <div *ngIf="policyItem.get('vTBackupSchedFreqType').hasError('required')">
                        请选择备份频率
                    </div>
                </div>
            </div>

            <div class="field-item required">
                <label>
                    <span class="label-text">保留时间</span>
                    <!-- <input nz-input style="width: 280px;" disabled="true" maxlength="50" type="text"
                        value="{{ vTBackupSchedRetTypeName }}" /> -->

                    <nz-select formControlName="vTBackupSchedRetTypename" nzPlaceHolder="请选择保留时间" >
                        <nz-option *ngFor="let item of (policyItem.value.vTBackupSchedRetTypes | keyvalue)"
                            [nzValue]="item.key">
                            {{item.value}}
                        </nz-option>
                    </nz-select>
                </label>
                <div class="form-hint error" *ngIf="isInvalid(policyItem.get('vTBackupSchedRetTypename'))">
                    <div *ngIf="policyItem.get('vTBackupSchedRetTypename').hasError('required')">
                        请选择保留时间
                    </div>
                </div>
            </div>

            <div class="field-item required" *ngIf="policyItem.value.vTBackupSchedFreqType !='Gold'">
                <label>
                    <span class="label-text">备份日期</span>
                    <nz-select formControlName="schedDate" nzPlaceHolder="请选择日期">
                        <nz-option *ngFor="let item of (schedDateList || [])"
                            [nzValue]="item" [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                </label>
                <div class="form-hint error" *ngIf="isInvalid(policyItem.get('schedDate'))">
                    <div *ngIf="policyItem.get('schedDate').hasError('required')">
                        请选择日期
                    </div>
                </div>
            </div>

            <div class="field-item required">
                <label>
                    <span class="label-text">备份云主机</span>
                    <nz-select nzShowSearch nzAllowClear formControlName="cloudServerName"
                        nzPlaceHolder="请选择主机">
                        <nz-option *ngFor="let item of (policyItem.value.cloudServers || [])"
                            [nzValue]="item" [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <div class="form-hint error" *ngIf="isInvalid(policyItem.get('cloudServerName'))">
                    <div *ngIf="policyItem.get('cloudServerName').hasError('required')">
                        请选择主机
                    </div>
                </div>
            </div>
        </div>
</form>
</ng-container>
</nz-modal>
