<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword" autocomplete="off" [(ngModel)]="keyword" nz-input placeholder="请输入节点名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch><i nz-icon nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <!-- 节点不支持新增操作，所以这里不需要添加按钮 -->
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">节点</span>
            </div>
            <nz-table #nodeList
                      style="overflow-x: auto"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="nodePageChange($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="nodeListData">
                <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)">
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width">
                            {{ col.title }}
                        </th>
                    </ng-container>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of nodeList.data; trackBy: trackById">
                    <td align="left" style="padding-left:35px;"><span style="color:#1890ff;">{{ item.name || '-' }}</span><br>{{item.uid }}</td>
                    <td>{{item.flavor}}</td>
                    <td>{{item.rootVolumeSize}}</td>
                    <td>{{item.dataVolumeSize}}</td>
                    <td>{{item.creationTimestamp}}</td>
                    <td>
                        <div class="on-table-actions" *ngIf="permission('view')"
                             [hidden]="busyStatus[item.id]">
                            <div class="on-table-action-item"
                                 (click)="visit(item);"
                                 [ngClass]="{'disabled': !nodeCanVisit(item)}">
                                <i nzTooltipTitle="访问"
                                   nzTooltipContent="bottom"
                                   nz-tooltip
                                   class="icon fa fa-eye"></i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                             [hidden]="!busyStatus[item.id]">
                            <div class="action-loading-placeholder">
                                <i class="icon" nz-icon [nzType]="'loading'"></i>
                                {{ getBusyText(item.id) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="nodeList.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
