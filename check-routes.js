const fs = require('fs');
const path = require('path');

// 递归获取所有路由模块文件
function getAllRoutingModules(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllRoutingModules(filePath, fileList);
    } else if (file.endsWith('-routing.module.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 检查路由模块文件中是否有缺少 pathMatch 的重定向路由
function checkRoutingModule(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let issues = [];
  
  // 使用正则表达式查找包含 redirectTo 的行
  const redirectToRegex = /redirectTo:/g;
  let match;
  let redirectToCount = 0;
  
  while ((match = redirectToRegex.exec(content)) !== null) {
    redirectToCount++;
  }
  
  // 使用正则表达式查找包含 pathMatch 的行
  const pathMatchRegex = /pathMatch:/g;
  let pathMatchCount = 0;
  
  while ((match = pathMatchRegex.exec(content)) !== null) {
    pathMatchCount++;
  }
  
  // 如果 redirectTo 的数量大于 pathMatch 的数量，可能存在问题
  if (redirectToCount > pathMatchCount) {
    issues.push({
      file: filePath,
      redirectToCount,
      pathMatchCount,
      diff: redirectToCount - pathMatchCount
    });
  }
  
  return issues;
}

// 主函数
function main() {
  const appDir = path.join('d:', 'workspace', 'aic', 'webui', 'src', 'app');
  const routingModules = getAllRoutingModules(appDir);
  
  let allIssues = [];
  
  routingModules.forEach(filePath => {
    const issues = checkRoutingModule(filePath);
    if (issues.length > 0) {
      allIssues = allIssues.concat(issues);
    }
  });
  
  if (allIssues.length > 0) {
    console.log('发现以下可能存在问题的文件：');
    allIssues.forEach(issue => {
      console.log(`文件: ${issue.file}`);
      console.log(`  redirectTo 数量: ${issue.redirectToCount}`);
      console.log(`  pathMatch 数量: ${issue.pathMatchCount}`);
      console.log(`  差异: ${issue.diff}`);
      console.log('---');
    });
  } else {
    console.log('没有发现问题，所有路由模块文件都已正确配置！');
  }
}

main();
