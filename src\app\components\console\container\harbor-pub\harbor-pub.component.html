<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../harbor_pub">容器服务</a></li>-->
<!--        <li><span>Harbor(公有)</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">Harbor(公有)</h3>
        </div>
        <div class="on-panel-body" *ngIf="isChecked">
            <div class="action-bar clearfix">
                <div class="pull-right">
                    <button nz-button nzType="primary" (click)="changePassword()">
                        <i nz-icon nzType="edit" nzTheme="outline"></i>
                        修改密码
                    </button>
                    <button nz-button nzType="primary" (click)="addProj()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新建项目
                    </button>
                </div>
            </div>
            <nz-table #tableList [nzItemRender]="renderItemTemplate" [nzTotal]="page.total" [nzPageIndex]="page.current"
                [nzPageSize]="page.size" (nzPageIndexChange)="pageChange($event)" [nzLoading]="tableLoading"
                [nzData]="tableListData">
                <thead>
                    <tr>
                        <th width="20%">项目名称</th>
                        <th>类型</th>
                        <th>仓库数量</th>
                        <th>当前角色</th>
                        <th>创建时间</th>
                        <th>更多操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data; index as i">
                        <td>{{data.projName || "-"}}</td>
                        <td>{{repoType(data.ownerType)}}</td>
                        <td>{{data.repoCount}}</td>
                        <td>{{roleType(data.roleType)}}</td>
                        <td>{{getdate(data.updateTime) || "-"}}</td>
                        <td>
                            <div class="on-table-actions" [hidden]="busyStatus[data.id]">
                                <div class="on-table-action-item" (click)="toDetail(data);"
                                    [ngClass]="{'disabled': !canGoToDetail(data)}">
                                    <i nzTitle="查看详情" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-search"></i>
                                </div>
                                <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top" nzTitle="确定要删除该项目吗？"
                                    (nzOnConfirm)="delete(data);">
                                    <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions" [hidden]="!busyStatus[data.id]">
                                <div class="action-loading-placeholder">
                                    <i class="icon" nz-icon [nzType]="'loading'"></i>
                                    {{ getBusyText(data) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'pre'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
        <div class="on-panel-body" *ngIf="!isChecked" style="height: 600px;">
            <div class="center-panel">
                <div class="center-header">
                    <p>创建用户</p>
                </div>
                <div class="center-body">
                    <div class="select-container">
                        <span class="select-tips">用户名：</span>
                        <input nz-input style="width: 280px" [disabled]="isLoading" maxlength="63" placeholder="请输入用户名"
                            [(ngModel)]="username" disabled>
                    </div>
                    <div class="select-container">
                        <span class="select-tips">密码：</span>
                        <input nz-input style="width: 280px" [disabled]="isLoading" (blur)="checkPassword(password)"
                            type="password" maxlength="63" placeholder="请输入密码" [(ngModel)]="password">
                        <p class="{{passwordCheck ? 'password-tips' : 'warning-text'}}">*密码长度在8到20之间，且必须包含大写字母、小写字母和数字
                        </p>
                    </div>
                    <button nz-button nzType="primary" [disabled]="isLoading" (click)="createUser()">确认</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--创建文件存储弹出框-->
<nz-modal [(nzVisible)]="showAddProj" nzTitle="创建文件系统" (nzOnCancel)="showAddProj = false" [nzOkLoading]="isLoading"
    [nzCancelLoading]="isLoading" (nzOnOk)="confirmCreateWindow()">
    <ng-container *nzModalContent>
    <div class="tc-content">
        <div class="select-container">
            <span class="select-tips">名称：</span>
            <input nz-input style="width: 300px" maxlength="63" placeholder="请填写项目名称" [disabled]="isLoading"
                (blur)="checkName()" [(ngModel)]="addProjName">
            <ol class="folder-tips" [ngClass]="{'folder-tips-error': !nameCheck}">
                <li>名称仅允许包含小写字母, 数字</li>
                <li>长度为2-30个字符</li>
            </ol>
        </div>
    </div>
    </ng-container>
</nz-modal>
<!--修改密码弹出框-->
<nz-modal [(nzVisible)]="showChangePass" nzTitle="修改密码" (nzOnCancel)="showChangePass = false" [nzOkLoading]="isLoading"
    [nzCancelLoading]="isLoading" (nzOnOk)="submitChangePassword()">
    <ng-container *nzModalContent>
    <div class="tc-content">
        <div class="select-container">
            <span class="select-tips">密码：</span>
            <input nz-input style="width: 300px" [disabled]="isLoading" (blur)="checkPassword(newPassword)" type="password"
                maxlength="63" placeholder="请输入密码" [(ngModel)]="newPassword">
            <p class="{{passwordCheck ? 'password-tips' : 'warning-text'}}">*密码长度在8到20之间，且必须包含大写字母、小写字母和数字
            </p>
        </div>
    </div>
    </ng-container>
</nz-modal>