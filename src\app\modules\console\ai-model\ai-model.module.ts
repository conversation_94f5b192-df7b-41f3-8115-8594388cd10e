import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AiModelRoutingModule } from './ai-model-routing.module';
import { ModelManagementComponent } from 'src/app/components/console/ai-model/management/model-management.component';
import { ModelMarketComponent } from 'src/app/components/console/ai-model/market/model-market.component';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';

@NgModule({
    declarations: [ModelManagementComponent,ModelMarketComponent],
    imports: [CommonModule, SharedModule, AiModelRoutingModule]
})
export class AiModelModule {
    constructor(moduleRef: NgModuleRef<AiModelModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
