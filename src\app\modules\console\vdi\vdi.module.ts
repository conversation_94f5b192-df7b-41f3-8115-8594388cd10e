import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VdiRoutingModule } from './vdi-routing.module';
import { VdiComponent } from '../../../components/console/vdi/vdi/vdi.component';
import { VdiConfigComponent } from '../../../components/console/vdi/vdi/config/vdi-config.component';
import { VdiPoolComponent } from '../../../components/console/vdi/vdi-pool/vdi-pool.component';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';

@NgModule({
    declarations: [
        VdiComponent,
        VdiConfigComponent,
        VdiPoolComponent
    ],
    imports: [
        CommonModule,
        VdiRoutingModule,
        SharedModule
    ]
})
export class VdiModule {
    constructor(moduleRef: NgModuleRef<VdiModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
