import { OnDestroy, Injectable } from '@angular/core';
import { Subscription } from 'rxjs';
import { HttpListenerManagerService } from '../../services/http-listener-manager.service';

@Injectable()
export class BaseComponent implements OnDestroy {
  protected subscriptions: Subscription[] = [];

  constructor(protected httpListenerManager?: HttpListenerManagerService) {}

  /**
   * 添加订阅到管理列表
   * @param subscription 要管理的订阅
   */
  protected addSubscription(subscription: Subscription): void {
    if (this.httpListenerManager) {
      this.httpListenerManager.addSubscription(subscription);
    } else {
      this.subscriptions.push(subscription);
    }
  }

  /**
   * 取消所有订阅
   */
  protected clearSubscriptions(): void {
    if (this.httpListenerManager) {
      this.httpListenerManager.clearSubscriptions();
    } else {
      this.subscriptions.forEach(subscription => {
        if (subscription && !subscription.closed) {
          subscription.unsubscribe();
        }
      });
      this.subscriptions = [];
    }
  }

  ngOnDestroy(): void {
    this.clearSubscriptions();
  }
}
