.map-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .content-wrapper {
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    gap: 20px; // Space between map and list
    min-height: 0; // Prevent flex blowout
  }

  .chart-area {
    flex: 3; // Map takes more space
    min-width: 0; // Prevent flex blowout
    height: 100%;

    .chart-container {
      width: 100%;
      height: 100%;
      // height is set by [style.height.px]="chartHeight"
    }
  }

  .details-list {
    flex: 1; // List takes less space
    min-width: 150px; // Ensure a minimum width
    max-width: 250px; // Prevent list from becoming too wide
    border-left: 1px solid #e0e0e0; // Lighter border
    padding-left: 15px;
    margin-left: 15px; // Add margin for separation
    overflow-y: auto; // Allow list to scroll if needed
    font-size: 12px;
    // background-color: #f9f9f9; // Remove subtle background color
    border-radius: 4px;
    padding: 15px;

    // Remove h4 styles as it's removed from HTML
    /*
    h4 {
      font-size: 14px; // Slightly larger title
      font-weight: 600; // Semi-bold
      color: #333;
      margin-top: 0;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    */

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 12px; // More space between items
        padding-bottom: 12px;
        border-bottom: 1px dashed #eee; // Dashed separator
        word-break: break-word; // Use break-word for better wrapping

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        strong {
            display: block;
            font-size: 13px;
            color: #155EEF; // Match point color
            margin-bottom: 4px;
            font-weight: 500;
        }

        .details-content {
            color: #555;
            font-size: 11px;
            line-height: 1.4;
            white-space: pre-wrap;
            background-color: #fff; // White background for details
            padding: 6px 8px;
            border-radius: 3px;
            border: 1px solid #eee;

            // Styles for key-value pairs from keyvalue pipe
            .detail-key {
                font-weight: 500; // Slightly bolder key
                color: #333;
                margin-right: 4px;
            }
            .detail-value {
                color: #555;
            }
        }
      }
    }
  }
} 