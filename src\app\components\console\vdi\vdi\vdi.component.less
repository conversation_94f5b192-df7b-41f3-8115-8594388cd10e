.table-content {
    .on-panel {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .on-panel-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .right-button-group {
                display: flex;
                gap: 8px;

                .disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
            }
        }

        .on-panel-body {
            padding: 0;

            .action-bar {
                padding: 16px 24px;
                border-bottom: 1px solid #f0f0f0;

                .title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #262626;
                }
            }
        }
    }

    .on-table-actions {
        gap: 8px;

        .on-table-action-item {
            cursor: pointer;
            //padding: 4px;
            border-radius: 4px;
            transition: background-color 0.3s;

            &:hover {
                background-color: #f5f5f5;
            }

            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    background-color: transparent;
                }
            }

            .icon {
                //font-size: 16px;
                color: #666;

                &:hover {
                    color: #1890ff;
                }
            }

            // 更多操作按钮样式
            &.more-actions-trigger {
                position: relative;

                .icon {
                    font-size: 14px;
                    color: #666;

                    &:hover {
                        color: #1890ff;
                    }
                }
            }
        }

        .action-loading-placeholder {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 12px;

            .icon {
                font-size: 14px;
            }
        }
    }

    .dot {
        display: inline-flex;
        align-items: center;

        &::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            background-color: #d9d9d9;
        }

        &.dot-green::before {
            background-color: #52c41a;
        }

        &.dot-red::before {
            background-color: #ff4d4f;
        }

        &.dot-blue::before {
            background-color: #1890ff;
        }

        &.dot-gray::before {
            background-color: #d9d9d9;
        }
    }

    .loading-placeholder {
        td {
            height: 200px;
            text-align: center;
            color: #999;
        }
    }

    .resize-trigger {
        width: 1px;
        height: 100%;
        background-color: #f0f0f0;
        cursor: col-resize;

        &:hover {
            background-color: #1890ff;
        }
    }

    // 更多操作下拉菜单样式
    .more-actions-menu {
        min-width: 140px;
        padding: 4px 0;

        .ant-dropdown-menu-item {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            line-height: 1.5;
            transition: all 0.3s;

            .icon {
                font-size: 14px;
                color: #666;
                width: 16px;
                text-align: center;
            }

            span {
                flex: 1;
                color: #262626;
            }

            &:hover {
                background-color: #f5f5f5;

                .icon {
                    color: #1890ff;
                }

                span {
                    color: #1890ff;
                }
            }

            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    background-color: transparent;

                    .icon {
                        color: #666;
                    }

                    span {
                        color: #262626;
                    }
                }
            }
        }
    }
}

// 全局下拉菜单样式覆盖
:host ::ng-deep {
    .more-actions-menu {
        .ant-dropdown-menu-item {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            line-height: 1.5;
            transition: all 0.3s;

            .icon {
                font-size: 14px;
                color: #666;
                width: 16px;
                text-align: center;
            }

            span {
                flex: 1;
                color: #262626;
            }

            &:hover {
                background-color: #f5f5f5;

                .icon {
                    color: #1890ff;
                }

                span {
                    color: #1890ff;
                }
            }

            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    background-color: transparent;

                    .icon {
                        color: #666;
                    }

                    span {
                        color: #262626;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .table-content {
        .on-panel-header {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;

            .right-button-group {
                justify-content: flex-end;
            }
        }
    }
}

.dot[class*=dot-]:before{
    top:35%;
}