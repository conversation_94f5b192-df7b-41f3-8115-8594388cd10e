<nz-modal [(nzVisible)]="isVisible" nzTitle="绑定弹性公网IP" nzOkText="绑定" [nzMaskClosable]="okLoading ? false : true"
    [nzOkLoading]="okLoading" (nzOnCancel)="handleCancel()" (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <form [formGroup]="bind" class="config-content sm">
        <div class="field-group">
            <div class="field-item required">
                <label for="">
                    <span class="label-text">IP地址</span>
                    <span class="lable-span">{{ network.ipAddress }}</span>
                </label>
            </div>
            <div class="field-item required">
                <label for="">
                    <span class="label-text">实例类型</span>
                    <nz-select [(ngModel)]="targetType" [ngModelOptions]="{standalone: true}" nzPlaceHolder="请选择实例类型">
                        <nz-option [nzValue]="1" nzLabel="云服务器">
                        </nz-option>
                    </nz-select>
                </label>
            </div>
            <div class="field-item required">
                <label for="">
                    <span class="label-text">实例</span>
                    <nz-select nzShowSearch formControlName="vmId" nzPlaceHolder="请选择云服务器实例">
                        <nz-option *ngFor="let item of instanceList" [nzValue]="item.id" [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(bind.get('vmId'))" class="form-hint error">
                    <div *ngIf="bind.get('vmId').hasError('required')">
                        请选择要绑定的云服务器
                    </div>
                </div>
            </div>
<!--            <div class="field-item required">-->
<!--                <label for="">-->
<!--                    <span class="label-text">协议</span>-->
<!--                    <nz-select formControlName="protocol" nzPlaceHolder="请选择协议" (ngModelChange)="protocolChange($event)">-->
<!--                        <nz-option *ngFor="let item of protocol" [nzValue]="item.name" [nzLabel]="item.title">-->
<!--                        </nz-option>-->
<!--                    </nz-select>-->
<!--                </label>-->
<!--                <div *ngIf="isInvalid(bind.get('protocol'))" class="form-hint error">-->
<!--                    <div *ngIf="bind.get('protocol').hasError('required')">-->
<!--                        请选择要绑定协议-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="field-item"-->
<!--            [ngClass]="{'required': protocolShow}">-->
<!--                <label for="">-->
<!--                    <span class="label-text">云主机端口</span>-->
<!--                    &lt;!&ndash; <input nz-input type="text" formControlName="vm_port" maxlength="5" placeholder="请输入云主机端口"> &ndash;&gt;-->
<!--                    <nz-input-number [nzPrecision]=0 formControlName="vm_port"-->
<!--                        [nzMin]="0" [nzMax]="65535" [nzStep]="1"></nz-input-number>-->
<!--                        <span class="small tip">端口号范围在0~65535之间</span>-->
<!--                </label>-->
<!--                <div *ngIf="isInvalid(bind.get('vm_port'))" class="form-hint error">-->
<!--                    <div *ngIf="bind.get('vm_port').hasError('required')">-->
<!--                        云主机端口不能为空-->
<!--                    </div>-->
<!--                    <div *ngIf="bind.get('vm_port').hasError('min')">-->
<!--                        云主机端口范围在 0-65535-->
<!--                    </div>-->
<!--                    <div *ngIf="bind.get('vm_port').hasError('max')">-->
<!--                        云主机端口范围在 0-65535-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="field-item"-->
<!--            [ngClass]="{'required': protocolShow}">-->
<!--                <label for="">-->
<!--                    <span class="label-text">公网IP端口</span>-->
<!--                    &lt;!&ndash; <input nz-input type="text" formControlName="public_port" maxlength="5" placeholder="请输入公网IP端口"> &ndash;&gt;-->
<!--                    <nz-input-number [nzPrecision]=0 formControlName="public_port"-->
<!--                        [nzMin]="0" [nzMax]="65535" [nzStep]="1"></nz-input-number>-->
<!--                    <span class="small tip">端口号范围在0~65535之间</span>-->
<!--                </label>-->
<!--                <div *ngIf="isInvalid(bind.get('public_port'))" class="form-hint error">-->
<!--                    <div *ngIf="bind.get('public_port').hasError('required')">-->
<!--                        公网IP端口不能为空-->
<!--                    </div>-->
<!--                    <div *ngIf="bind.get('public_port').hasError('min')">-->
<!--                        公网IP端口范围在 0-65535-->
<!--                    </div>-->
<!--                    <div *ngIf="bind.get('public_port').hasError('max')">-->
<!--                        公网IP端口范围在 0-65535-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
        </div>
    </form>
    </ng-container>
</nz-modal>