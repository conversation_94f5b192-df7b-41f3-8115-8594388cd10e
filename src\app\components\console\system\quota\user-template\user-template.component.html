<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">配额管理</a></li>-->
<!--        <li><span>默认配额</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">
                默认配额
                <small class="danger">
                    <i class="fa fa-exclamation-circle"></i>
                    设置项目默认配额
                </small>
            </h3>
        </div>
        <div class="on-panel-body">
<!--            <div class="action-bar clearfix">-->
<!--                <form nz-form nzLayout="inline"-->
<!--                      (ngSubmit)="search()">-->
<!--                    <nz-input-group nzSearch-->
<!--                                    [nzAddOnAfter]="suffixIconButton">-->
<!--                        <input type="text" name="keyword"-->
<!--                               autocomplete="off"-->
<!--                               [(ngModel)]="keyword" nz-input-->
<!--                               placeholder="请输入资源类型名称" />-->
<!--                    </nz-input-group>-->
<!--                    <ng-template #suffixIconButton>-->
<!--                        <button nz-button nzType="primary"-->
<!--                                nzSearch><i nz-icon-->
<!--                                            nzType="search"></i></button>-->
<!--                    </ng-template>-->
<!--                </form>-->
<!--            </div>-->
            <nz-table #tableList
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <th>服务类型</th>
                        <th>资源类型</th>
                        <th>默认配额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.service}}</td>
                        <td>{{data.resourceTypeText}}</td>
                        <td>{{data.quota}}</td>
                        <td>
                            <div class="on-table-actions"
                                 [hidden]="busyStatus[data.id]">
                                <a class="on-table-action-item"
                                   (click)="changeQuota(data);">
                                    <i nzTitle="调制配额"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       nz-icon
                                       nzType="edit"
                                       nzTheme="outline"
                                       class="icon"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>


<nz-modal [(nzVisible)]="changeQuotaWindow" nzTitle="{{changeItem.resourceTypeText}}调整"
          (nzOnCancel)="changeQuotaWindow = false" [nzWidth]="500"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submit()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">当前配额：</span>
        <span>{{changeItem.quota}}</span>
    </div>
    <div class="select-container">
        <span class="select-tips">调整为：</span>
        <span><nz-input-number [nzPrecision]=0 [(ngModel)]="changeItem.changeQuota" [nzMin]="0" [nzMax]="200" [nzStep]="1">
                        </nz-input-number></span>
    </div>
    </ng-container>
</nz-modal>