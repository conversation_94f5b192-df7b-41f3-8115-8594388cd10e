import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import {ColumnChartTemplateComponent} from "./column-chart-template.component";

describe('ManagementComponent', () => {
    let component: ColumnChartTemplateComponent;
    let fixture: ComponentFixture<ColumnChartTemplateComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            declarations: [ColumnChartTemplateComponent]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(ColumnChartTemplateComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
