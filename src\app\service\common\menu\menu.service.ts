import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { menuData as defaultMenuData } from 'src/app/components/console/common/console-layout/menuData';
import { k8sMenuData } from 'src/app/components/console/common/console-layout/k8sMenuData';
import { operationMenuData } from 'src/app/components/console/common/console-layout/operationMenuData';

@Injectable({
  providedIn: 'root'
})
export class MenuService {
  // 当前菜单数据源类型
  private currentMenuType = 'default';

  // 默认菜单数据
  private defaultMenuData = defaultMenuData;

  // 菜单数据源映射表，用于存储不同类型的菜单数据
  private menuDataMap: { [key: string]: any[] } = {
    'default': this.defaultMenuData,
    'k8s': k8sMenuData,
    'operation': operationMenuData
  };

  // 菜单路由模式映射，用于根据URL判断应该使用哪个菜单
  private menuRoutePatterns: { [key: string]: RegExp[] } = {
    'k8s': [
      /\/console\/container\/k8s-detail\/[\w-]+/,
      /\/console\/container\/node-pool\/[\w-]+/,
      /\/console\/container\/node\/[\w-]+/,
      /\/console\/container\/namespace\/[\w-]+/,
      /\/console\/container\/serviceDeploy\/[\w-]+/,
      /\/console\/container\/workload\/[\w-]+/
    ],
    'operation': [
      /\/console\/system\/index/,
      /\/console\/system\/cloud-audit/,
      /\/console\/system\/account/,
      /\/console\/system\/order/,
      /\/console\/system\/permission/,
      /\/console\/system\/quota/,
      /\/console\/system\/user-template/,
      /\/console\/system\/service-plan/,
      /\/console\/system\/vdi-pool/,
      /\/console\/system\/detail\/.*/
    ]
  };

  // 使用BehaviorSubject存储当前菜单数据，以便可以响应式更新
  private menuDataSubject: BehaviorSubject<any[]> = new BehaviorSubject<any[]>(this.defaultMenuData);

  // 公开Observable，供组件订阅菜单变化
  public menuData$: Observable<any[]> = this.menuDataSubject.asObservable();

  constructor() {
    // 确保k8s菜单数据已注册
    if (!this.menuDataMap['k8s']) {
      this.registerMenuData('k8s', k8sMenuData);
    }
    // 确保operation菜单数据已注册
    if (!this.menuDataMap['operation']) {
      this.registerMenuData('operation', operationMenuData);
    }
  }

  /**
   * 注册菜单数据源
   * @param type 菜单类型
   * @param data 菜单数据
   */
  registerMenuData(type: string, data: any[]): void {
    this.menuDataMap[type] = data;
  }

  /**
   * 切换菜单数据源
   * @param type 菜单类型
   * @returns 是否切换成功
   */
  switchMenu(type: string): boolean {
    if (this.menuDataMap[type]) {
      this.currentMenuType = type;
      this.menuDataSubject.next(this.menuDataMap[type]);
      return true;
    }
    return false;
  }

  /**
   * 切换回默认菜单
   */
  switchToDefaultMenu(): void {
    this.switchMenu('default');
  }

  /**
   * 获取当前菜单类型
   */
  getCurrentMenuType(): string {
    return this.currentMenuType;
  }

  /**
   * 获取当前菜单数据
   */
  getCurrentMenuData(): any[] {
    return this.menuDataMap[this.currentMenuType];
  }

  /**
   * 根据URL判断应该使用哪个菜单类型
   * @param url 当前URL
   * @returns 菜单类型
   */
  getMenuTypeByUrl(url: string): string {
    // 遍历所有菜单路由模式
    for (const [menuType, patterns] of Object.entries(this.menuRoutePatterns)) {
      // 检查URL是否匹配任何一个模式
      if (patterns.some(pattern => pattern.test(url))) {
        return menuType;
      }
    }
    // 默认返回default类型
    return 'default';
  }

  /**
   * 根据URL自动切换菜单
   * @param url 当前URL
   * @returns 是否切换成功
   */
  switchMenuByUrl(url: string): boolean {
    const menuType = this.getMenuTypeByUrl(url);
    // 如果菜单类型与当前类型不同，则切换
    if (menuType !== this.currentMenuType) {
      return this.switchMenu(menuType);
    }
    return true; // 当前已经是正确的菜单类型
  }
}