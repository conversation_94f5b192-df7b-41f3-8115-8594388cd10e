<nz-modal [nzWidth]="600" [(nzVisible)]="isVisible" nzTitle="账户列表" [nzMaskClosable]="okLoading ? false : true"
    [nzOkLoading]="okLoading" (nzOnCancel)="handleCancel()" [nzCancelDisabled]="okLoading" (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <div class="on-panel-body" style="max-height:400px;overflow: auto;">
        <nz-input-group nzSearch style="width:300px"
                        [nzAddOnAfter]="suffixIconButton">
            <input type="text" name="keyword"
                   autocomplete="off"
                   [(ngModel)]="searchText" nz-input
                   placeholder="请输入账户名称" />
        </nz-input-group>
        <ng-template #suffixIconButton>
            <button nz-button nzType="primary"
                    nzSearch><i nz-icon
                                nzType="search"></i></button>
        </ng-template>
        <div class="userContent">
            <label *ngFor="let item of userBindingList" style="" [hidden]="searchText && item.name.indexOf(searchText) == -1">
                <input [(ngModel)]="item.checked" type="checkbox" name="isAccepted" [value]="item.id"/>&nbsp;&nbsp;{{item.name}}
            </label>
        </div>
    </div>
    </ng-container>
</nz-modal>
