<div class="table-content">
    <ol class="on-breadcrumb">
        <li><a routerLink="../">云日志</a></li>
        <li><span>安装部署引导</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">安装部署引导</h3>
        </div>
        <div class="on-panel-body">
            <nz-steps [nzCurrent]="0" nzDirection="vertical" nzStatus="process">
                <nz-step nzTitle="步骤一：安装前准备"></nz-step>
                <div class="steps-description">
                    <button class="mb5" nz-button nzType="primary" (click)="showModal()">
                        <span>新建采集规则</span>
                    </button>
                    <nz-modal [(nzVisible)]="isVisible" nzTitle="新建采集规则" (nzOnCancel)="handleCancel()"
                        (nzOnOk)="handleOk()" [nzOkLoading]="isOkLoading">
                        <ng-container *nzModalContent>
                        <form class="config-content md network-form">
                            <section>
                                <div class="field-group">
                                    <div class="field-item required">
                                        <label>
                                            <span class="label-text">规则名称</span>
                                            <input nz-input type="text" maxlength="50"
                                                placeholder="请输入规则名称">
                                            <span class="small tip label-padding">允许字母、数字和-，字符个数不限，区分大小写</span> 
                                        </label>
                                    </div>
                                </div>
                            </section>

                            <section>
                                <div class="field-group">
                                    <div class="field-item required">
                                        <label fro="">
                                            <span class="label-text">选择实例</span>
                                            <nz-select nzPlaceHolder="请选择实例">
                                                <nz-option></nz-option>
                                            </nz-select>
                                        </label>
                                    </div>
                                </div>
                            </section>

                            <section>
                                <div class="field-group">
                                    <div class="field-item required">
                                        <label>
                                            <span class="label-text">日志采集路径</span>
                                            <input nz-input type="text" maxlength="50"
                                                placeholder="请输入路径">
                                            <span class="small tip label-padding">采集路径必须是实际存在的目录，路径需要精确到文件名。</span>
                                            <span class="small tip label-padding">避免采集出错，当您输入完路径后，需要回车才能生效</span>
                                        </label>
                                    </div>
                                </div>
                            </section>

                        </form>
                        </ng-container>
                    </nz-modal>

                    <nz-table>
                        <thead>
                            <tr>
                                <ng-container *ngFor="let col of cols">
                                    <th
                                      *ngIf="col.width"
                                      nz-resizable
                                      nzBounds="window"
                                      nzPreview
                                      nzColumnKey="col.ColumnKey"
                                      [nzWidth]="col.width"
                                      [nzMaxWidth]="600"
                                      [nzMinWidth]="60"
                                      [nzSortFn]="true"
                                      (nzResizeEnd)="onResize($event, col.title)"
                                    >
                                      {{ col.title }}
                                      <nz-resize-handle nzDirection="right">
                                        <div class="resize-trigger"></div>
                                      </nz-resize-handle>
                                    </th>
                                    <th *ngIf="!col.width">
                                      {{ col.title }}
                                    </th>
                                  </ng-container>
                                <!-- <th width="15%">采集规则名称</th>
                                <th width="20%">关联实例</th>
                                <th width="20%">操作</th> -->
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                            </tr>
                        </tbody>
                    </nz-table>

                </div>
                <nz-step nzTitle="步骤二：安装部署agent" nzStatus="process"></nz-step>
                <div class="steps-description">
                    <nz-switch [(ngModel)]="switchValue" (click)="menuInit()" class="mb5"></nz-switch>
                    <div>
                        <div *ngIf="switchValue" class="menu">
                            <ul nz-menu nzMode="inline">
                                <ul>
                                    <ul>
                                        <li nz-menu-item nzSelected (click)="changeselfMenu('agebt')">安装准备</li>
                                        <li nz-menu-item (click)="changeselfMenu('linuxTool')">安装Linux版工具</li>
                                        <li nz-menu-item [nzSelected]="choose" (click)="changeselfMenu('windowsTool')">安装Windows版工具</li>
                                        <li nz-menu-item [nzSelected]="quesion" (click)="changeselfMenu('question')">常见问题</li>
                                    </ul>
                                </ul>
                            </ul>
                        </div>
                        <div *ngIf="switchValue" class="help">
                            <app-help #helpcomponent (getFunction)="changeMenu($event)">
                            </app-help>
                        </div>
                    </div>
                </div>
                
                
                <nz-step nzTitle="步骤三：管理日志" nzStatus="process"></nz-step>
                <div class="steps-description">
                    <button nz-button nzType="primary" [routerLink]="'../admin'" class="mb5">
                        <span>日志管理</span>
                    </button>
                </div>
            </nz-steps>
        </div>
    </div>
</div>