import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpResponse,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap, finalize } from 'rxjs/operators';

@Injectable()
export class HttpListenerInterceptor implements HttpInterceptor {
  private activeRequests = 0;

  constructor() {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    this.activeRequests++;
    
    return next.handle(request).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          // 请求成功完成
        }
      }),
      catchError((error: HttpErrorResponse) => {
        // 处理错误
        return throwError(error);
      }),
      finalize(() => {
        this.activeRequests--;
        // 如果没有活跃的请求，可以执行一些清理操作
        if (this.activeRequests === 0) {
          // 所有请求都已完成
        }
      })
    );
  }
}
