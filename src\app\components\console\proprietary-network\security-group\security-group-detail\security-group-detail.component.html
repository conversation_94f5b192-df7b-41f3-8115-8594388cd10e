<nz-modal [(nzVisible)]="isVisible" [nzMaskClosable]="false"
          [nzTitle]="'安全组详情'"
          [nzWidth]="800"
          [nzBodyStyle]="{padding: '0 24px'}"
          (nzAfterOpen)="modalOpened()"
          (nzOnCancel)="handleCancel()"
          [nzFooter]="null"
          >
  <ng-container *nzModalContent>
        <div class="on-panel-body">
            <nz-tabset
                (nzSelectChange)="switchRule($event)"
                [nzSelectedIndex]="currentSelectedIndex"
                [nzTabBarExtraContent]="newRuleTemplate">
                <nz-tab [nzTitle]="item.label" *ngFor="let item of directionList">
                </nz-tab>
            </nz-tabset>
            <ng-template #newRuleTemplate>
                <button nz-button nzType="primary"
                    [disabled]="isBusy"
                    type="button" (click)="newRule()">
                    <i nz-icon nzType="plus"></i>
                    添加规则
                </button>
            </ng-template>
            <nz-table #securityGroupRules
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzShowPagination]="false"
                [nzData]="currentRuleList">
                <thead>
                    <tr>
                        <th width="20%">名称</th>
                        <th width="20%">协议/应用</th>
                        <th width="20%">端口范围</th>
                        <th width="20%">IP源地址</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of securityGroupRules.data; trackBy: trackById">
                        <td>{{ item.name }}</td>
                        <td>{{ item.protocol }}</td>
                        <td>{{ item.direction === 'in' ? (item.destinationPortRange || '-') : (item.sourcePortRange || '-') }}</td>
                        <td>{{ item.direction === 'in' ? item.destinationIp : item.sourceIp }}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    [ngClass]="{'disabled': !canEditRule(item)}"
                                    (click)="editRule(item)">
                                    <i nzTooltipTitle="编辑"
                                    nzTooltipContent="bottom"
                                    nz-tooltip
                                    class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该安全组规则吗？"
                                    [nzCondition]="!canDeleteSgRule(item)"
                                    (nzOnConfirm)="deleteSgRule(item);"
                                    [ngClass]="{'disabled': !canDeleteSgRule(item)}">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="securityGroupRules.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
        </div>
  </ng-container>
</nz-modal>

<app-security-rule-config
    [ruleData]="currentRule"
    [customOk]="saveRule"
    [isEdit]="isEditingRule"
    [isLoading]="isAddingRule"
    (close)="isRuleConfigVisible = false"
    [isVisible]="isRuleConfigVisible">
</app-security-rule-config>