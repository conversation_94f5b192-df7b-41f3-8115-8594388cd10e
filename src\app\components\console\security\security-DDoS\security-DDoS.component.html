<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">安全组件</a></li>-->
<!--        <li><span>抗DDoS</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">抗DDoS</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="keyword" nz-input
                            placeholder="按照名称搜索" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <!-- <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                    [routerLink]="isArchiveUser === 'true' ? null : '../security-DDoS-config'" [queryParams]="{id: typeId}">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建抗DDoS
                      </a>
                </div> -->
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <a nz-button nzType="primary"
                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                    [routerLink]="isArchiveUser === 'true' ? null : '../security-DDoS-config-quota'" [queryParams]="{id: typeId}">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建抗DDoS
                      </a>
                </div>
            </div>
            <nz-table #backup style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="DDoSList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="600"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of backup.data; trackBy: trackById">
                        <td><span style="word-break: break-all; " >{{ item.name }}</span></td>
                        <td>{{ item.ipAdress || "-"}}</td>
                        <td>抗Ddos</td>
                        <td>{{ item.broadband || "-"}}</td>
                       <!-- <td>{{ item.userName || "-"}}</td>
                        <td>{{ item.cansecretVisible ?item.userPassword : "***"}}
                            <i class="suffix" [title]="item.cansecretVisible ? '隐藏密码' : '查看密码'" nz-icon
                            [nzType]="item.cansecretVisible ? 'eye-invisible' : 'eye'"
                            (click)="item.cansecretVisible = !item.cansecretVisible" style="margin-bottom: 5px;"></i>
                        </td>
                        <td>
                            <div *ngIf="item.gdUr != ''">
                                <a href="javascript:void(0)" (click)="gdUrl(item)">{{ item.gdUrl}}</a>
                                <span >
                                    仅限内网访问
                                </span>
                            </div>
                            <div *ngIf="item.gdUr === ''">
                                -
                            </div>
                        </td>-->
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item"
                                    (click)="getDDoSInfo(item);">
                                    <i nzTitle="跳转"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-xinchuangkoudakai"></i>
                                </div>
<!--                                <div class="on-table-action-item"-->
<!--                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"-->
<!--                                    (click)="isArchiveUser === 'true' ? null : showEditModal(item)">-->
<!--                                    <i nzTooltipTitle="编辑"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        class="icon fa fa-edit"></i>-->
<!--                                </div>-->

                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzPlacement="top"
                                    [title]="isArchiveUser === 'true' ? null : '确定要删除该抗DDoS吗？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteDDoS(item);"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTooltipTitle="删除"
                                    nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="backup.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 编辑DDoS弹出框 -->
<nz-modal [(nzVisible)]="isVisible" nzTitle="编辑DDoS" (nzOnCancel)="handleCancel()"
(nzOnOk)="updateDdos()" [nzOkLoading]="isOkLoading" [nzCancelLoading]="isLoading" [nzWidth]="620">
<ng-container *nzModalContent>
<form class="config-content md network-form modalForm" [formGroup]="ddosItem">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">IP地址</span>
                        <input nz-input type="text"  formControlName="ipAdress" maxlength="150" placeholder="多个IP请用逗号分隔">
                    </label>
                    <div *ngIf="isInvalid(ddosItem.get('ipAdress'))"
                         class="form-hint error">
                        <div *ngIf="ddosItem.get('ipAdress').hasError('required')">
                            IP地址不能为空
                        </div>
                        <div *ngIf="ddosItem.get('ipAdress').hasError('pattern')">
                            请输入正确的IP地址，多个IP用逗号分隔
                        </div>
                    </div>
                </div>
            </div>
    </form>
</ng-container>
</nz-modal>
