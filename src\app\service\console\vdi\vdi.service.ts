import { Injectable } from '@angular/core';
import { RequestService } from '../../common/request/request.service';
import * as URL from '../../common/URL';

@Injectable({
    providedIn: 'root'
})
export class VdiService {
    constructor(
        private req: RequestService
    ) {}

    // ==================== VDI Pool 相关方法 ====================

    /**
     * 获取虚拟机池列表
     * @param params 查询参数
     */
    getVdiPoolList(params: any) {
        return this.req.post(URL.VDI_POOL_URL + '/query', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 创建虚拟机池
     * @param params 创建参数
     */
    createVdiPool(params: any) {
        return this.req.post('/cloud/api/aic/order/vdiPool/save', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 删除虚拟机池
     * @param id 虚拟机池ID
     */
    deleteVdiPool(id: string | number) {
        return this.req.delete(URL.VDI_POOL_URL + '/delete/' + id)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    // ==================== VDI 相关方法 ====================

    /**
     * 获取VDI列表
     * @param params 查询参数
     */
    getVdiList(params: any) {
        return this.req.post(URL.VDI_URL + '/query', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 创建VDI
     * @param params 创建参数
     */
    createVdi(params: any) {
        return this.req.post('/cloud/api/aic/order/vdi/save', params)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 删除VDI
     * @param id VDI ID
     */
    deleteVdi(id: string | number) {
        return this.req.delete(URL.VDI_URL + '/delete/' + id)
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * VDI开机
     * @param id VDI ID
     */
    powerOnVdi(id: string | number) {
        return this.req.post(URL.VDI_URL + '/powerOn/' + id, {})
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * VDI关机
     * @param id VDI ID
     */
    powerOffVdi(id: string | number) {
        return this.req.post(URL.VDI_URL + '/powerOff/' + id, {})
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * VDI重启
     * @param id VDI ID
     */
    rebootVdi(id: string | number) {
        return this.req.post(URL.VDI_URL + '/reboot/' + id, {})
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }

    /**
     * 获取远程桌面连接URL
     * @param id VDI ID
     */
    getRemoteDesktopUrl(id: string | number) {
        return this.req.get(URL.VDI_URL + '/remoteDesktop/' + id, {})
            .then(rs => {
                return rs;
            })
            .catch(err => {
                return { error: err };
            });
    }
}
