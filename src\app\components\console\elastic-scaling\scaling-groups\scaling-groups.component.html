<div class="scaling-groups table-content">
    <ol class="on-breadcrumb">
        <li><span>弹性伸缩组</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">弹性伸缩组</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <div class="pull-right" >
                    <a nz-button nzType="primary" 
                       [ngClass]="{'disabled': resLimit}"
                        [routerLink]="'../scaling-group-config'">
                        <i nz-icon nzType="plus" 
                           nzTheme="outline"></i>
                        创建弹性伸缩组
                    </a>
                </div>
            </div>
            <nz-table #groups 
            [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="scalingGroups">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width">
                              {{ col.title }}
                            </th>
                        </ng-container>
                        <!-- <th width="15%">名称/ID</th>
                        <th width="15%">最小实例数/最大实例数</th>
                        <th width="20%">负载均衡模式</th>
                        <th width="20%">实例移除策略</th>
                        <th width="10%">默认冷却时间(秒)</th>
                        <th width="20%">操作</th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of groups.data">
                    <td>
                        {{ item.name }}/{{ item.id }}
                    </td>
                    <td>
                        {{ item.spAutoScalingPolicy.minValue }}/{{ item.spAutoScalingPolicy.maxValue }}
                    </td>
                    <td>
                        {{ item.spAutoScalingPolicy.spLoadBalancerId }}
                    </td>
                    <td>
                        {{ item.spAutoScalingPolicy.removePolicy }}
                    </td>
                    <td>
                        {{ item.spAutoScalingPolicy.removeCD }}
                    </td>
                    <td>
                        <div class="on-table-actions"
                            [hidden]="busyStatus[item.id]">
                            <a class="on-table-action-item"
                               (click)="showModal(item);">
                                <i nzTitle="查看配置"
                                nzPlacement="bottom"
                                nz-tooltip
                                class="icon fa fa-search"></i>
                            </a>
                            <a routerLink="../scaling-group-config"
                                class="on-table-action-item"
                                [queryParams]="{ id: item.id, spid: item.spAutoScalingPolicy.id }">
                                <i nzTitle="编辑伸缩组"
                                nzPlacement="bottom"
                                nz-tooltip
                                class="icon fa fa-edit"></i>
                            </a>
                            <div class="on-table-action-item"
                                nz-popconfirm
                                nzPlacement="top"
                                nzTitle="确定要删除该伸缩组吗？"
                                (nzOnConfirm)="deleteConfirm(item);">
                                <i nzTitle="删除"
                                    nzPlacement="bottom"
                                    nz-tooltip
                                    class="icon fa fa-trash-o"></i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                            [hidden]="!busyStatus[item.id]">
                            <div
                                class="action-loading-placeholder">
                                <i class="icon" nz-icon
                                    [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                    </td>
                </tr>
                <tr [hidden]="groups.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>

    </div>
</div>

<!-- <app-scaling-group-vm [isVisible]="vmModalVisible" [vmId]="currentVmId" [initData]="vmInitData" (close)="hideVmModal()">
</app-scaling-group-vm> -->

<nz-modal [nzWidth]="600"
    [(nzVisible)]="isVisible"
    nzTitle="配置详情" [nzCancelText]="null"
    [nzFooter]="null"
    (nzOnOk)="isVisible = false"
    (nzOnCancel)="isVisible = false">
    <ng-container *nzModalContent>
        <table class="monitor-info">
            <tbody>
                <tr>
                    <td width="35%" class="th">监控规则</td>
                    <td>{{ getMonitorRuleText(checkList) }}</td>
                </tr>
                <tr>
                    <td class="th">名称</td>
                    <td>{{ checkList.name }}</td>
                </tr>
                <tr>
                    <td class="th">最小实例数</td>
                    <td>{{ checkList.spAutoScalingPolicy.minValue }}台</td>
                </tr>
                <tr>
                    <td class="th">最大实例数</td>
                    <td>{{ checkList.spAutoScalingPolicy.maxValue }}台</td>
                </tr>
                <tr>
                    <td class="th">负载均衡模式</td>
                    <td>{{ checkList.spAutoScalingPolicy.spLoadBalancerId }}</td>
                </tr>
                <tr>
                    <td class="th">实例移除策略</td>
                    <td>{{ checkList.spAutoScalingPolicy.removePolicy }}</td>
                </tr>
                <tr>
                    <td class="th">默认冷却时间</td>
                    <td>{{ checkList.spAutoScalingPolicy.removeCD }}秒</td>
                </tr>
            </tbody>
        </table>
    </ng-container>
</nz-modal>