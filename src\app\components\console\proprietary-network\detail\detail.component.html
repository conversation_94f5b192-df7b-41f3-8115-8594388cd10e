<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="/console/proprietary-network">专有网络</a></li>-->
<!--        <li><span>详情</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <div class="right-button-group">
                <div *ngIf="permission('create')">
                    <a nz-button nzType="primary" class="primary"
                            [ngClass]="{'disabled': isArchiveUser === 'true'}"
                            (click)="networkModalVisible = true">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建子网
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">{{ vpcItem.name ? vpcItem.name + ' - ' : ''}}详情</span>
            </div>
            <nz-table
                #networks
                [nzShowPagination]="false"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzData]="vpcItem.ovdcNetworkList || []">
                <thead>
                    <tr>
                        <th width="25%">ID</th>
                        <th width="15%">名称</th>
<!--                        <th width="10%">OVDC</th>-->
<!--                        <th width="25%">路由ID</th>-->
                        <th width="5%">类型</th>
                        <th width="20%">网段</th>
<!--                        <th width="25%">DNS</th>-->
<!--                        <th width="20%">已用/可用IP数</th>-->
                        <th width="10%">操作</th>
                    </tr>
                </thead>
                <tbody *ngIf="networks.data.length">
                    <tr *ngFor="let item of networks.data; trackBy: trackById">
                        <td>{{ item.resourceId }}</td>
                        <td>{{ item.name }}</td>
<!--                        <td>{{item.ovdcName}}</td>-->
<!--                        <td>{{ item.routerId }}</td>-->
                        <td>{{ item.type }}</td>
                        <td>
                            <nz-table *ngIf="item.ipScopeList && item.ipScopeList.length"
                                      #ipScopeList
                                      [nzShowPagination]="false"
                                      [nzData]="item.ipScopeList || []"
                                      class="custom-table"
                            >
                                <tbody *ngFor="let scopeItem of ipScopeList.data; trackBy: trackById">
                                    <tr>
                                        <td align="right">CIDR：</td><td align="left">{{ scopeItem.cidr }}</td>
                                    </tr>
                                    <tr>
                                        <td align="right">GATEWAY：</td><td align="left">{{ scopeItem.gateway }}</td>
                                    </tr>
                                    <tr>
                                        <td align="right">DNS：</td><td align="left">{{ scopeItem.dns1 }}<br> {{ scopeItem.dns2 }}</td>
                                    </tr>
                                </tbody>
                            </nz-table>
                        </td>
<!--                        <td>-->
<!--                            {{ item.ipScopeList[0].ipRangeList[0].ipBegin }}-->
<!--                            ~-->
<!--                            {{ item.ipScopeList[0].ipRangeList[0].ipEnd }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ item.ipScopeList[0].numberOfUsedIp }}-->
<!--                            /-->
<!--                            {{ item.ipScopeList[0].numberOfTotalIp }}-->
<!--                        </td>-->
                        <td>
                            <div class="on-table-actions" *ngIf="ecspermission('create')"
                                [hidden]="busyStatus[item.id]">
                                <a class="on-table-action-item" [title]="'创建ECS'"
                                    [routerLink]="['/console/cloud-server/instance-config',
                                    {vpcId: vpcId, ovdcNetworkId: item.id}]">
                                    <i
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        nz-icon
                                        nzType="cloud-server"
                                        nzTheme="outline"
                                        class="icon"></i>
                                </a>
<!--                                <a class="on-table-action-item" *ngIf="permission('create')"-->
<!--                                    [routerLink]="['/console/load-balance/load-balance-config',-->
<!--                                    {vpcId: vpcId, ovdcNetworkId: item.id}]">-->
<!--                                    <i nzTitle="创建SLB"-->
<!--                                        nzTooltipContent="bottom"-->
<!--                                        nz-tooltip-->
<!--                                        nz-icon-->
<!--                                        nzType="deployment-unit"-->
<!--                                        nzTheme="outline"-->
<!--                                        class="icon"></i>-->
<!--                                </a>-->
                                <div class="on-table-action-item" *ngIf="permission('delete')"
                                    nz-popconfirm
                                     nzTooltipContent="top"
                                    [nzTitle]="isArchiveUser === 'true' ? null : '确定要删除该子网吗？'"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteNetwork(item);"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="networks.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
        </div>
    </div>
</div>

<app-network-config
    [isVisible]="networkModalVisible"
    [vpc]="vpcItem"
    type="network" (submit)="getVpcInfo()"
    (close)="networkModalVisible = false">
</app-network-config>

<app-network-config (refreshParent)="getVpcInfo(null)"></app-network-config>
