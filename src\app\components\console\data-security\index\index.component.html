<div class="table-content">
    <ol class="on-breadcrumb">
        <li><span>数据安全</span></li>
    </ol>
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title" >
                数据安全
            </h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="keyword" nz-input
                            placeholder="请输入名称" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                        [ngClass]="{'disabled': resLimit}"
                        [routerLink]="resLimit ? null : '../security-config'">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建数据安全
                    </a>
                </div>
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <a nz-button nzType="primary"
                        [ngClass]="{'disabled': resLimit || isArchiveUser === 'true'}"
                        [routerLink]="resLimit || isArchiveUser === 'true' ? null : '../security-config-quota'">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建数据安全
                    </a>
                </div>
            </div>
            <nz-table style="overflow-x: auto"
                #security
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="securityList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="400"
                              [nzMinWidth]="60"
                              [nzSortFn]="true"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of security.data">
                        <td>{{ item.name }}</td>
                        <td>{{ item.securityName }}</td>
                        <td>{{ item.deployStatusName }}</td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item"
                                    (click)="!canDeleteVM(item)? null : jumpNewWindow(item)" [ngClass]="{'disabled': !canDeleteVM(item)} ">
                                    <i nzTooltipTitle="安全管理中心"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon iconfont icon-xinchuangkoudakai"></i>
                                </div>
                                <div class="on-table-action-item" *ngIf="sslvpnFlag || bastionFlag"
                                     (click)="!canAccessVM(item)? null : accessVM(item);" [ngClass]="{'disabled': !canAccessVM(item)} ">
                                    <i nzTooltipTitle="公网访问"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon fa fa-desktop"></i>
                                </div>
                                <div class="on-table-action-item"
                                nz-popconfirm
                                nzPlacement="top"
                                [nzTitle]="isArchiveUser === 'true' ? null : '确定要删除吗？'"
                                (nzOnConfirm)="(isArchiveUser === 'true' || !canDeleteVM(item))? null : deleteSecurity(item);"
                                [nzCondition]="!canDeleteVM(item)"
                                [ngClass]="{'disabled': !canDeleteVM(item) || isArchiveUser === 'true'  } ">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipContent="top"
                                     nzPopconfirmTitle="确定要取消挂载吗？"
                                     (nzOnConfirm)="powerOffVM(item);"
                                     [nzCondition]="!canPowerOffVM(item)"
                                     [hidden]="!(canPowerOffVM(item))"
                                     [ngClass]="{'disabled': busyStatus[item.id]}">
                                    <i nzTooltipTitle="取消挂载"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-guanji">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipContent="top"
                                     nzPopconfirmTitle="确定要挂载吗？"
                                     (nzOnConfirm)="powerOnVM(item);"
                                     [nzCondition]="!canPowerOnVM(item)"
                                     [hidden]="!canPowerOnVM(item)"
                                     [ngClass]="{'disabled': busyStatus[item.id]}">
                                    <i nzTooltipTitle="挂载"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-qidong"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="security.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
