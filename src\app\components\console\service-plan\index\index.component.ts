import { Component, OnInit } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { ServicePlanService } from 'src/app/service/console/service-plan/service-plan.service';
import { environment } from 'src/environments/environment';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { ServicePlanType, ServicePlanTypeMap } from '../service-plan-type.enum';

const BUSY_TEXT_MAP = {
    'delete': '删除中',
};

@Component({
    selector: 'app-index',
    templateUrl: './index.component.html',
    styleUrls: ['./index.component.less']
})
export class IndexComponent implements OnInit {
    constructor(
        private msg: NzMessageService,
        private modal: NzModalService,
        private servicePlanService: ServicePlanService
    ) {}
    isAdmin = window.localStorage.getItem('isAdmin') === 'true';
    isArchiveUser = window.localStorage.getItem('isArchiveUser') === 'true';
    isLoading: boolean = false;
    keyword: string = '';
    selectedType: string = '';
    servicePlanTypeOptions = Object.keys(ServicePlanTypeMap).map(key => ({
        label: ServicePlanTypeMap[key],
        value: key
    }));
    servicePlanList = [];
    busyStatus = {};
    editModalVisible: boolean = false;
    editData: any = null;

    // 排序
    cols = [
        {
            title: '名称',
            ColumnKey: "name",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '25%',
        },
        {
            title: '类型',
            ColumnKey: "servicePlanType",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '10%',
        },
        {
            title: '服务编码',
            ColumnKey: "servicePlanCode",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '15%',
        },
        {
            title: '价格',
            ColumnKey: "price",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '10%',
        },
        {
            title: '生效日期',
            ColumnKey: "autoEffectiveDate",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '15%',
        },
        {
            title: '到期日期',
            ColumnKey: "autoExpiryDate",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '15%',
        },
        {
            title: '操作',
            ColumnKey: "",
            allowSort: '',
            sortFlag: false,
            showSort: false,
            width: '10%',
        }
    ];
    filters = {
        pageNum: 0,
        pageSize: environment.pageSize,
        orderBy1: false,
        orderName1: '',
        orderBy2: false,
        orderName2: '',
    };

    pager = {
        page: 1,
        pageSize: environment.pageSize,
        total: 0,
    };
    sortName = '';
    sortValue = false;
    openFlag:boolean = true;
    fristQuery:boolean = false;
    oldSortName;
    sort;
    index;

    ngOnInit() {
        this.getServicePlanList();
    }

    getServicePlanList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);

        // 初始化bean对象
        params.bean = params.bean || {};

        // 添加名称搜索条件
        if (keyword) {
            params.bean.name = keyword;
        }

        // 添加类型筛选条件
        if (this.selectedType) {
            params.bean.servicePlanType = this.selectedType;
        }

        this.isLoading = true;
        this.servicePlanService.query(params)
        .then(rs => {
            if (rs.success) {
                this.servicePlanList = rs.data.dataList || [];
                this.pager = {
                    page: rs.data.pageNum + 1,
                    pageSize: rs.data.pageSize,
                    total: rs.data.recordCount,
                };
            } else {
                this.msg.error(`获取服务计划列表失败${ rs.message ? ': ' + rs.message : '' }`);
            }

            this.isLoading = false;
        })
        .catch(err => {
            this.msg.error('获取服务计划列表失败');
            this.isLoading = false;
        });
    }

    trackById(item) {
        return item.id;
    }

    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getServicePlanList();
    }

    search() {
        this.filters.pageNum = 0;
        this.getServicePlanList();
    }

    onTypeChange() {
        this.filters.pageNum = 0;
        this.getServicePlanList();
    }

    getBusyText(item): string {
        return BUSY_TEXT_MAP[this.busyStatus[item.id]] || '';
    }

    onParamsChange(params: NzTableQueryParams): void {
        if (this.fristQuery) {
            this.fristQuery = false;
            return;
        }
        if (params.sort.length > 0) {
            const sortField = params.sort.filter(item => item.value !== null)[0];
            if (sortField) {
                this.sortName = sortField.key;
                this.sortValue = sortField.value === 'ascend' ? false : true;
                if (this.oldSortName !== this.sortName) {
                    this.oldSortName = this.sortName;
                    this.sort = 1;
                } else {
                    this.sort++;
                }
                if (this.sort === 1) {
                    this.filters.orderName1 = this.sortName;
                    this.filters.orderBy1 = this.sortValue;
                    this.filters.orderName2 = '';
                    this.filters.orderBy2 = false;
                } else if (this.sort === 2) {
                    this.filters.orderName2 = this.sortName;
                    this.filters.orderBy2 = this.sortValue;
                } else {
                    this.sort = 0;
                    this.filters.orderName1 = '';
                    this.filters.orderBy1 = false;
                    this.filters.orderName2 = '';
                    this.filters.orderBy2 = false;
                }
                this.getServicePlanList();
            }
        }
    }

    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
    }

    getServicePlanType(type: string): string {
        return ServicePlanTypeMap[type] || type;
    }

    formatDate(date: string): string {
        if (!date) return '';
        return date;
    }

    openEditModal(item?: any) {
        this.editData = item ? { ...item } : null;
        this.editModalVisible = true;
    }

    closeEditModal() {
        this.editModalVisible = false;
        this.editData = null;
    }

    handleEditSubmit() {
        this.closeEditModal();
        this.getServicePlanList();
    }

    deleteServicePlan(item) {
        this.busyStatus[item.id] = 'delete';
        this.servicePlanService.delete(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('服务计划删除成功');
                this.getServicePlanList();
            } else {
                this.msg.error(`服务计划删除失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('服务计划删除失败');
            this.busyStatus[item.id] = '';
        });
    }
}
