/* 基础样式 */
.flex-col {
  display: flex;
  flex-direction: column;
  font-size: 14px;
}

div {
  box-sizing: border-box;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #212332;
  background: #F5F7FC;
  width: 100%;
  padding-top: 58px;
}

/* 导航样式 */
nav {
  padding: 30px;
}

nav a {
  font-weight: bold;
  color: #2c3e50;
}

nav a.router-link-exact-active {
  color: #42b983;
}

a {
  padding-left: 40px;
  color: #212332;
  text-decoration: none;
}

a.document {
  padding-left: 0 !important;
}

a:hover,
a:active {
  font-weight: bold;
  color: #155EEF;
}

a.primary:hover {
  color: #fff;
}

/* Flex 布局 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-row-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* 位置样式 */
.p-r {
  position: relative;
  z-index: 3;
}

.p-absolute {
  position: absolute;
}

/* 宽度样式 */
.w-full {
  width: 100%;
}

/* 文本样式 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

/* 字体样式 */
.fs-14 {
  font-size: 14px !important;
}

.fs-16 {
  font-size: 16px;
  font-weight: 500;
}

.fs-18 {
  font-size: 18px;
  font-weight: 400;
}

.fs-20 {
  font-size: 20px;
  font-weight: 500;
}

.fs-24 {
  font-size: 24px;
  font-weight: 400;
}

/* 颜色样式 */
.fc-white {
  color: #fff;
}

.fc-main {
  color: #212332;
}

.fc-sub {
  color: #5B6167;
}

/* 按钮样式 */
.primary {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background: #155EEF;
  border-radius: 6px;
  color: #fff;
  border: none;
  box-shadow: none;
  padding: 10px 20px;
}

.primary.shadow {
  box-shadow: 0px 4px 6px 0px rgba(10, 34, 66, 0.15);
}

.primary-font-color,
.fc-primary {
  color: #155EEF;
}

/* 容器样式 */
.container {
  position: relative;
  box-sizing: border-box;
  width: 1200px;
  padding: 0;
  margin: 0 auto;
}

.container-full {
  position: relative;
  width: 100%;
  height: auto;
}

/* 背景样式 */
.bg-white {
  background: #fff;
}

/* 边框样式 */
.rounded-12 {
  border-radius: 12px;
}

/* 头部导航 */
.header {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #212332;
  background: rgba(255,255,255,0.3);
  padding-left: 24px;
  padding-right: 24px;
}

.headerScroll {
  background: #fff;
}

.logo {
  min-width: 70px;
  min-height: 26px;
}

.logo img {
  width: 100%;
  height: 100%;
}

.navList a {
  padding-left: 40px;
  color: #212332;
  text-decoration: none;
}

.navList a:first-child {
  padding-left: 50px;
}

.navList a:hover,
.navList a.active,
.navList .router-link-active {
  font-weight: bold;
  color: #155EEF;
}

/* 背景图片 */
.bg-1 {
  height: 600px;
  background: #E4E9F4;
  background-image: url("/assets/images/index/AiH-bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom right;
}

/* 登录信息 */
.login-info {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  align-content: center;
}

.login-info a {
  display: flex;
  align-content: center;
  align-items: center;
}

.login-info a i {
  color: #155EEF;
  font-weight: bold;
  margin-right: 10px;
  height: 24px;
  line-height: 24px;
}

.login-info .dropdown {
  max-width: 200px;
  display: flex;
}

.login-info .dropdown .menu-item {
  display: flex;
  align-content: flex-end;
}
