import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CloudMonitorRoutingModule } from './cloud-monitor-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';
import { MonitorListComponent } from 'src/app/components/console/cloud-monitor/monitor-list.component';
// import { LineChartTemplateComponent } from 'src/app/components/console/dashboard-template/line-chart/line-chart-template.component';
import { MonitorBrandComponent} from 'src/app/components/console/cloud-monitor/brand/monitor-brand.component';
import { MonitorDetailComponent} from 'src/app/components/console/cloud-monitor/brand/detail/monitor-detail.component';
import { LOCALE_ID } from '@angular/core';
// import { NgxEchartsModule } from 'ngx-echarts';
import {DashboardTemplateModule} from "../dashboard-template/dashboard-template.module";

@NgModule({
    declarations: [MonitorListComponent, MonitorBrandComponent,MonitorDetailComponent],
    imports: [CommonModule, SharedModule, CloudMonitorRoutingModule, DashboardTemplateModule],
    providers: [
        { provide: LOCALE_ID, useValue: 'zh-CN' }
    ],
})
export class CloudMonitorModule {
    constructor(moduleRef: NgModuleRef<CloudMonitorModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
