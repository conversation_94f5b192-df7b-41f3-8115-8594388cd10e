# Angular 15 到 Angular 16 升级总结

## 升级内容

1. **Angular核心包升级**
   - 从Angular 15.2.10升级到Angular 16.2.12
   - 使用命令：`ng update @angular/core@16 @angular/cli@16 --force --allow-dirty`

2. **ng-zorro-antd升级**
   - 从ng-zorro-antd 15.1.1升级到ng-zorro-antd 16.2.2
   - 使用命令：`ng update ng-zorro-antd@16 --force --allow-dirty`

3. **Angular CDK升级**
   - 从@angular/cdk 15.2.9升级到@angular/cdk 16.2.12
   - 通过手动修改package.json实现

4. **其他依赖升级**
   - ngx-filesaver从15.0.0升级到16.0.0
   - ngx-bootstrap从9.0.0升级到11.0.2
   - ngx-pagination从6.0.1升级到6.0.3
   - rxjs从7.5.0升级到7.8.1
   - tslib从2.3.1升级到2.6.2
   - typescript从4.9.5升级到5.1.6
   - 添加@ctrl/tinycolor依赖，用于ng-zorro-antd

5. **代码修改**
   - 修复AI组件中的sanitizer初始化问题
   - 修复hmr-module-helper.ts中使用了不再支持的entryComponents属性的问题
   - 更新tsconfig.json和tsconfig.base.json文件中的module、moduleResolution和lib选项

## 升级结果

1. **构建成功**
   - 使用`npm run build`命令成功构建项目
   - 没有出现编译错误

2. **开发服务器启动成功**
   - 使用`npm start`命令成功启动开发服务器
   - 服务器在http://localhost:4200/上运行

## 遇到的问题及解决方案

1. **缺少@ctrl/tinycolor依赖**
   - 问题：ng-zorro-antd 16版本需要@ctrl/tinycolor依赖
   - 解决方案：安装@ctrl/tinycolor依赖

2. **AI组件中的sanitizer初始化问题**
   - 问题：在构造函数初始化前使用了sanitizer
   - 解决方案：将iframeUrl的初始化移到ngOnInit方法中

3. **hmr-module-helper.ts中使用了不再支持的entryComponents属性**
   - 问题：Angular 16中已经移除了entryComponents属性
   - 解决方案：使用declarations属性替代entryComponents属性

4. **TypeScript版本兼容性**
   - 问题：Angular 16需要更高版本的TypeScript
   - 解决方案：将TypeScript升级到5.1.6版本

5. **tsconfig.json配置更新**
   - 问题：需要更新TypeScript配置以适应Angular 16
   - 解决方案：更新module、moduleResolution和lib选项
