<div style="text-align: left; font-weight: bold; padding-left: 20px; font-size: 12px">{{ title }}</div>
<div echarts [options]="chartOptions" [merge]="updateOptions" [style.height.px]="chartHeight"></div>
<div style="text-align: center;">
      <span *ngFor="let key of dataKeys; let i = index" style="margin: 0 10px;">
        <span style="display: inline-block; width: 10px; height: 10px; margin-right: 5px; background-color: {{ colors[i % colors.length] }}; vertical-align: middle;"></span>
          {{ key }}
      </span>
</div>