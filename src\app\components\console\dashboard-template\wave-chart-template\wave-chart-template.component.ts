import { Component, Input, OnChanges, OnDestroy, AfterViewInit, HostListener } from '@angular/core';
import { EChartsOption } from 'echarts';
import { ECharts } from 'echarts';
import ResizeObserver from 'resize-observer-polyfill';

export interface WaveChartColorConfig {
    waveColor?: string;        // 波形颜色
    backgroundColor?: string;  // 背景颜色
    textColor?: string;        // 文本颜色
    gridColor?: string;        // 网格颜色
}

@Component({
    selector: 'app-wave-chart-template',
    templateUrl: './wave-chart-template.component.html',
    styleUrls: ['./wave-chart-template.component.less']
})
export class WaveChartTemplateComponent implements OnChanges, OnDestroy, AfterViewInit {
    private chartInstance: ECharts;
    private resizeObserver: ResizeObserver;

    @Input() title: string = '';
    @Input() chartData: number[] = [];
    @Input() colorConfig: WaveChartColorConfig = {};
    @Input() xAxisLabel: string = '时间';
    @Input() yAxisLabel: string = '值';
    @Input() chartHeight: string = '300px';
    @Input() unit: string = '';
    @Input() smooth: boolean = true;
    @Input() showArea: boolean = true;
    @Input() showGrid: boolean = true;

    chartOption: EChartsOption;

    // 默认颜色配置
    private defaultColors: WaveChartColorConfig = {
        waveColor: '#5470C6',
        backgroundColor: 'rgba(84, 112, 198, 0.1)',
        textColor: '#666',
        gridColor: '#eee'
    };

    // 合并后的最终配置
    get mergedColors(): WaveChartColorConfig {
        return {
            waveColor: this.colorConfig.waveColor || this.defaultColors.waveColor,
            backgroundColor: this.colorConfig.backgroundColor || this.defaultColors.backgroundColor,
            textColor: this.colorConfig.textColor || this.defaultColors.textColor,
            gridColor: this.colorConfig.gridColor || this.defaultColors.gridColor
        };
    }

    @HostListener('window:resize')
    onResize() {
        if (this.chartInstance) {
            this.chartInstance.resize();
        }
    }

    ngAfterViewInit() {
        // 确保在视图初始化后初始化图表
        setTimeout(() => {
            this.initChart();
        }, 0);
    }

    onChartInit(ec: ECharts) {
        this.chartInstance = ec;
        this.initResizeObserver();
    }

    // 使用ResizeObserver监听容器变化
    private initResizeObserver() {
        const container = document.querySelector('.wave-chart-container');
        if (container) {
            this.resizeObserver = new ResizeObserver(() => {
                this.resizeChart();
            });
            this.resizeObserver.observe(container);
        }
    }

    ngOnDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
    }

    private resizeChart() {
        if (this.chartInstance) {
            this.chartInstance.resize({
                width: 'auto', // 自动适应容器
                height: typeof this.chartHeight === 'number' ? this.chartHeight : parseInt(String(this.chartHeight), 10)
            });
        }
    }

    ngOnChanges() {
        this.initChart();
    }

    private initChart() {
        // 生成X轴数据（时间点）
        const xAxisData = this.generateXAxisData();

        this.chartOption = {
            title: {
                text: this.title,
                textStyle: {
                    color: this.mergedColors.textColor,
                    fontSize: 14
                },
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: '#FFF',
                borderColor: '#DDD',
                borderWidth: 1,
                padding: 10,
                textStyle: {
                    color: '#333',
                    fontSize: 12
                },
                axisPointer: {
                    type: 'line',
                    lineStyle: {
                        color: this.mergedColors.waveColor,
                        opacity: 0.5
                    }
                },
                formatter: (params: any) => {
                    const time = params[0].axisValue;
                    const value = params[0].value;
                    return `<div style="font-weight: 500;">${time}</div>
                            <div style="margin-top: 4px;">
                              <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${this.mergedColors.waveColor};margin-right:8px"></span>
                              ${this.yAxisLabel}: <span style="font-weight:600;margin-left:4px">${value}${this.unit}</span>
                            </div>`;
                }
            },
            grid: {
                top: '15%',
                bottom: '10%',
                left: '3%',
                right: '4%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: xAxisData,
                boundaryGap: false,
                axisLine: {
                    lineStyle: {
                        color: this.mergedColors.textColor
                    }
                },
                axisLabel: {
                    color: this.mergedColors.textColor,
                    fontSize: 10
                },
                splitLine: {
                    show: this.showGrid,
                    lineStyle: {
                        color: this.mergedColors.gridColor,
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: this.yAxisLabel,
                nameTextStyle: {
                    color: this.mergedColors.textColor
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: this.mergedColors.textColor
                    }
                },
                axisLabel: {
                    color: this.mergedColors.textColor,
                    formatter: `{value}${this.unit}`
                },
                splitLine: {
                    show: this.showGrid,
                    lineStyle: {
                        color: this.mergedColors.gridColor,
                        type: 'dashed'
                    }
                }
            },
            series: [{
                name: this.yAxisLabel,
                type: 'line',
                smooth: this.smooth,
                symbol: 'none',
                sampling: 'average',
                itemStyle: {
                    color: this.mergedColors.waveColor
                },
                areaStyle: this.showArea ? {
                    color: this.mergedColors.backgroundColor,
                    opacity: 0.8
                } : undefined,
                data: this.chartData
            }]
        };
    }

    // 生成X轴数据（最近1小时的时间点）
    private generateXAxisData(): string[] {
        const now = new Date();
        const result: string[] = [];

        // 生成最近1小时的时间点，每2分钟一个点，总共生成30个点
        for (let i = 29; i >= 0; i--) {
            // 计算时间点，从现在开始往前推算
            // 60分钟 * 60秒 * 1000毫秒 = 3600000毫秒（一小时）
            // 3600000 / 30 = 120000毫秒（每两分钟）
            const time = new Date(now.getTime() - i * 120000);
            result.push(this.formatTime(time));
        }

        return result;
    }

    // 格式化时间为 HH:MM 格式
    private formatTime(date: Date): string {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
}
