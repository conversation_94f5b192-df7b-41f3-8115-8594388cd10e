<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="/console/load-balance">负载均衡</a></li>-->
<!--        <li><span>监听详情</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">{{ defaultPool ? defaultPool + ' - ' : '' }}监听详情</h3>
        </div>
        <div class="on-panel-body">
            <nz-table #monitors
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzShowPagination]="false"
                [nzData]="monitorList">
                <thead>
                    <tr>
                        <th width="5%" nzShowExpand></th>
                        <th width="15%">协议</th>
                        <th width="15%">前端端口</th>
                        <th width="25%">均衡算法</th>
                        <th width="20%">后端端口</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- <tr><td>{{ monitors.data | json }}</td></tr> -->
                    <ng-container *ngFor="let item of monitors.data; trackBy: trackById">
                        <tr>
                            <td nzShowExpand [(nzExpand)]="item.expand"></td>
                            <td>{{ item.virutalServerprotocol }}</td>
                            <td>{{ item.virutalServerPort }}</td>
                            <td>{{ algorithmTypeMap[item.algorithm] }}</td>
                            <td>{{ item.port }}</td>
                            <td>
                                <div class="on-table-actions"
                                    [hidden]="busyStatus[item.id]">
                                    <div class="on-table-action-item"
                                        (click)="showEditModal(item)">
                                        <i nzTitle="编辑"
                                            nzTooltipContent="bottom"
                                            nz-tooltip
                                            class="icon fa fa-edit"></i>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr [nzExpand]="item.expand">
                            <td colspan="6">
                                <!-- <div class="sub-table-section">
                                    <div class="sub-title">
                                        健康检查设置
                                    </div>
                                    <nz-table #members [nzData]="item.healthCheck" nzSize="middle" [nzShowPagination]="false">
                                        <thead>
                                            <tr>
                                                <th width="5%">协议</th>
                                                <th width="15%">端口</th>
                                                <th width="15%">检查间隔（秒）</th>
                                                <th width="25%">超时时间（秒）</th>
                                                <th width="20%">最大重试次数</th>
                                                <th width="20%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let hc of members.data" [ngClass]="{'editing': hc.edit}">
                                                <td>{{ hc.mode }}</td>
                                                <td>{{ item.healthCheckPort }}</td>
                                                <td>
                                                    <span *ngIf="!hc.edit; else editInterval">
                                                        {{ hc.interval }}
                                                    </span>
                                                    <ng-template #editInterval>
                                                        <input
                                                            nz-tooltip
                                                            [nzTitle]="hcError.interval.error"
                                                            [nzVisible]="!!hcError.interval.error"
                                                            nzTooltipContent="top"
                                                            type="number"
                                                            nz-input
                                                            [disabled]="!!busyStatus['hc-' + hc.id]"
                                                            [(ngModel)]="currentHc.interval" />
                                                    </ng-template>
                                                </td>
                                                <td>
                                                    <span *ngIf="!hc.edit; else editTimeout">
                                                        {{ hc.timeout }}
                                                    </span>
                                                    <ng-template #editTimeout>
                                                        <input
                                                            nz-tooltip
                                                            [nzTitle]="hcError.timeout.error"
                                                            [nzVisible]="!!hcError.timeout.error"
                                                            nzTooltipContent="top"
                                                            type="number"
                                                            nz-input
                                                            [disabled]="!!busyStatus['hc-' + hc.id]"
                                                            [(ngModel)]="currentHc.timeout" />
                                                    </ng-template>
                                                </td>
                                                <td>
                                                    <span *ngIf="!hc.edit; else editMaxRetry">
                                                        {{ hc.maxRetry }}
                                                    </span>
                                                    <ng-template #editMaxRetry>
                                                        <input
                                                            nz-tooltip
                                                            [nzTitle]="hcError.maxRetry.error"
                                                            [nzVisible]="!!hcError.maxRetry.error"
                                                            nzTooltipContent="top"
                                                            type="number"
                                                            nz-input
                                                            [disabled]="!!busyStatus['hc-' + hc.id]"
                                                            [(ngModel)]="currentHc.maxRetry" />
                                                    </ng-template>
                                                </td>
                                                <td>
                                                    <div class="on-table-actions"
                                                        [hidden]="busyStatus['hc-' + hc.id]">
                                                        <div class="on-table-action-item"
                                                            *ngIf="!hc.edit"
                                                            (click)="editHc(hc)">
                                                            <i nzTitle="编辑"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-edit"></i>
                                                        </div>
                                                        <div class="on-table-action-item"
                                                            *ngIf="hc.edit"
                                                            (click)="saveHc(hc)">
                                                            <i nzTitle="保存"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-save"></i>
                                                        </div>
                                                        <div class="on-table-action-item"
                                                            *ngIf="hc.edit"
                                                            (click)="hc.edit = false">
                                                            <i nzTitle="取消"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-undo"></i>
                                                        </div>
                                                    </div>
                                                    <div class="on-table-actions"
                                                        [hidden]="!busyStatus['hc-' + hc.id]">
                                                        <div
                                                            class="action-loading-placeholder">
                                                            <i class="icon" nz-icon
                                                                [nzType]="'loading'"></i>
                                                            {{ getBusyText(hc, 'hc') }}
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </nz-table>
                                </div> -->

                                <div class="sub-table-section">
                                    <div class="sub-title">
                                        云服务器设置

                                        <div class="pull-right">
                                            <button nz-button
                                                (click)="addEcsModalVisible = true"
                                                nzSize="small"
                                                nzType="primary">
                                                <i nz-icon nzType="plus"
                                                    nzTheme="outline"></i>
                                                添加云服务器
                                            </button>
                                        </div>
                                    </div>
                                    <nz-table #cloudServers [nzData]="item.memberList" nzSize="middle" [nzShowPagination]="false">
                                        <thead>
                                            <tr>
                                                <th width="33%">服务器名称</th>
                                                <!-- <th width="25%">健康状态</th> -->
                                                <th width="33%">权重</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let ecs of cloudServers.data" [ngClass]="{'editing': ecs.edit}">
                                                <td>{{ ecs.vmName }}</td>
                                                <!-- <td>
                                                    <span class="dot {{ 'dot-' + (ecs.healthStatus === 'UP' ? 'green' : 'red')}}">
                                                        {{ ecs.healthStatus === 'UP' ? '健康' : '不健康' }}
                                                    </span>
                                                </td> -->
                                                <td>
                                                    <span *ngIf="!ecs.edit; else editWeight">
                                                        {{ ecs.weight }}
                                                    </span>
                                                    <ng-template #editWeight>
                                                        <nz-input-number 
                                                            [nzMin]="1"
                                                            [nzMax]="100" [nzStep]="1"
                                                            nz-tooltip
                                                            [nzPrecision]="0"
                                                            [nzTitle]="ecsError.weight.error"
                                                            [nzVisible]="!!ecsError.weight.error"
                                                            nzTooltipContent="top"
                                                            [nzDisabled]="!!busyStatus['ecs-' + ecs.id]"
                                                            [(ngModel)]="currentEcs.weight">
                                                        </nz-input-number>
                                                    </ng-template>
                                                </td>
                                                <td>
                                                    <div class="on-table-actions"
                                                        [hidden]="busyStatus['ecs-' + ecs.id]">
                                                        <div class="on-table-action-item"
                                                            *ngIf="!ecs.edit"
                                                            (click)="editEcs(cloudServers.data, ecs)">
                                                            <i nzTitle="编辑"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-edit"></i>
                                                        </div>
                                                        <div class="on-table-action-item"
                                                            *ngIf="ecs.edit"
                                                            (click)="saveEcs(ecs)">
                                                            <i nzTitle="保存"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-save"></i>
                                                        </div>
                                                        <div class="on-table-action-item"
                                                            *ngIf="ecs.edit"
                                                            (click)="ecs.edit = false">
                                                            <i nzTitle="取消"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-undo"></i>
                                                        </div>
                                                        <div class="on-table-action-item"
                                                            *ngIf="!ecs.edit"
                                                            nz-popconfirm
                                                            nzTooltipContent="top"
                                                            nzTitle="确定要移除该云服务器吗？"
                                                            [ngClass]="{'disabled': !canRemoveEcs()}"
                                                            [nzCondition]="!canRemoveEcs()"
                                                            (nzOnConfirm)="removeEcs(ecs);">
                                                            <i nzTitle="移除"
                                                                nzTooltipContent="bottom"
                                                                nz-tooltip
                                                                class="icon fa fa-trash-o">
                                                            </i>
                                                        </div>
                                                    </div>
                                                    <div class="on-table-actions"
                                                        [hidden]="!busyStatus['ecs-' + ecs.id]">
                                                        <div
                                                            class="action-loading-placeholder">
                                                            <i class="icon" nz-icon
                                                                [nzType]="'loading'"></i>
                                                            {{ getBusyText(ecs, 'ecs') }}
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </nz-table>
                                </div>
                            </td>
                        </tr>
                    </ng-container>
                    <tr [hidden]="monitors.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="editModalVisible"
    nzTitle="编辑监听配置"
    nzOkText="保存"
    [nzMaskClosable]="!isSaving"
    [nzOkLoading]="isSaving"
    [nzBodyStyle]="{padding: '8px'}"
    (nzOnCancel)="hideEditModal()"
    (nzOnOk)="saveMonitorConfig()">
    <ng-container *nzModalContent>
    <form [formGroup]="monitorConfig"
        class="config-content sm">
        <div class="field-group">
            <div class="field-item select-group">
                <label for="">
                    <span class="label-text">名称</span>
                    <span>{{ monitorList[0] ? monitorList[0].name : '' }}</span>
                </label>
            </div>
            <div class="field-item">
                <label>
                    <span class="label-text">协议</span>
                    <!-- <nz-select formControlName="protocol"
                    nzPlaceHolder="请选择监听协议">
                        <nz-option
                            *ngFor="let item of ['TCP', 'HTTP']"
                            [nzValue]="item"
                            [nzLabel]="item">
                        </nz-option>
                    </nz-select> -->
                    <span>{{ monitorConfig.value.protocol }}</span>
                </label>
                <!-- <div *ngIf="isInvalid(monitorConfig.get('protocol'))"
                    class="form-hint error">
                    <div *ngIf="monitorConfig.get('protocol').hasError('required')">
                        请选择监听协议
                    </div>
                </div> -->
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">前端端口</span>
                    <input nz-input type="number"
                        nz-tooltip
                        nzTitle="1 ~ 65535"
                        nzTooltipContent="top"
                        formControlName="vsPort"
                        maxlength="5" placeholder="请输入前端端口号">
                </label>
                <div *ngIf="isInvalid(monitorConfig.get('vsPort'))"
                    class="form-hint error">
                    <div *ngIf="monitorConfig.get('vsPort').hasError('required')">
                        请填写端口号
                    </div>
                    <div *ngIf="monitorConfig.get('vsPort').hasError('pattern') ||
                        monitorConfig.get('vsPort').hasError('min') ||
                        monitorConfig.get('vsPort').hasError('max')">
                        请输入有效的端口号
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">后端端口</span>
                    <input nz-input type="number"
                        nz-tooltip
                        nzTitle="1 ~ 65535"
                        nzTooltipContent="top"
                        formControlName="port"
                        maxlength="5" placeholder="请输入后端端口号">
                </label>
                <div *ngIf="isInvalid(monitorConfig.get('port'))"
                    class="form-hint error">
                    <div *ngIf="monitorConfig.get('port').hasError('required')">
                        请填写端口号
                    </div>
                    <div *ngIf="monitorConfig.get('port').hasError('pattern') ||
                        monitorConfig.get('port').hasError('min') ||
                        monitorConfig.get('port').hasError('max')">
                        请输入有效的端口号
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label for="">
                    <span class="label-text">均衡算法</span>
                    <nz-select formControlName="algorithm"
                        nzPlaceHolder="请选择均衡算法">
                        <nz-option
                            *ngFor="let item of algorithmTypeMap | keyvalue;"
                            [nzValue]="item.key"
                            [nzLabel]="item.value">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(monitorConfig.get('algorithm'))"
                    class="form-hint error">
                    <div *ngIf="monitorConfig.get('algorithm').hasError('required')">
                        请选择均衡算法
                    </div>
                </div>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="addEcsModalVisible"
    [nzMaskClosable]="!isAdding"
    nzTitle="添加云服务器"
    nzOkText="添加"
    [nzOkLoading]="isAdding"
    [nzBodyStyle]="{padding: '8px'}"
    (nzOnCancel)="hideAddModal()"
    (nzOnOk)="addEcs()">
    <ng-container *nzModalContent>
    <form [formGroup]="ecs"
        class="config-content sm">
        <div class="field-group">
            <div class="field-item required">
                <label for="">
                    <span class="label-text">云服务器</span>
                    <nz-select
                        nzShowSearch
                        formControlName="vmName"
                        (ngModelChange)="vmChange($event)"
                        nzPlaceHolder="请选择云服务器">
                        <nz-option
                            *ngFor="let item of instanceList"
                            [nzValue]="item"
                            [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isEcsInvalid(ecs.get('vmName'))"
                    class="form-hint error">
                    <div *ngIf="ecs.get('vmName').hasError('required')">
                        请选择云服务器
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">权重</span>
                    <nz-input-number
                        style="width: 250px"
                        [nzPrecision]="0"
                        [nzMin]="1"
                        [nzMax]="100" [nzStep]="1"
                        formControlName="weight">
                    </nz-input-number>
                </label>
                <div *ngIf="isEcsInvalid(ecs.get('weight'))"
                    class="form-hint error">
                    <div *ngIf="ecs.get('weight').hasError('required')">
                        请填写权重
                    </div>
                    <div *ngIf="ecs.get('weight').hasError('pattern') ||
                        ecs.get('weight').hasError('min') ||
                        ecs.get('weight').hasError('max')">
                        请输入有效的权重：1 ~ 99
                    </div>
                </div>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>