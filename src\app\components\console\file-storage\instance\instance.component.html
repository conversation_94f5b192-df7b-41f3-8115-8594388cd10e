<div class="table-content">
<!--  <ol class="on-breadcrumb">-->
<!--    <li><a routerLink="../">文件存储</a></li>-->
<!--    <li><span>文件系统</span></li>-->
<!--  </ol>-->
  <div class="on-panel">
    <div class="on-panel-header">
      <h3 class="title">文件存储 <em class="sub-title">文件存储访问需要在云主机配置DNS:10.204.101.245</em></h3>
    </div>
    <div class="on-panel-body">
      <div class="action-bar clearfix">
        <form nz-form nzLayout="inline"
          (ngSubmit)="getList()">
          <nz-input-group nzSearch
              [nzAddOnAfter]="suffixIconButton">
              <input type="text" name="keyword"
                  autocomplete="off"
                  [(ngModel)]="keyword" nz-input
                  placeholder="请输入文件名称" />
          </nz-input-group>
          <ng-template #suffixIconButton>
              <button nz-button nzType="primary"
                  nzSearch><i nz-icon
                      nzType="search"></i></button>
          </ng-template>
         </form>
        <div class="pull-right" *ngIf="isAdmin === 'true'">
            <!-- <button nz-button nzType="primary"
                (click)="createFileStore()">
                <i nz-icon nzType="plus"
                    nzTheme="outline"></i>
                创建文件系统
            </button> -->
            <a nz-button nzType="primary"
            [routerLink]="'../file-storage-config'">
            <i nz-icon nzType="plus"
                nzTheme="outline"></i>
                创建文件系统
              </a>
        </div>
        <div class="pull-right" *ngIf="isAdmin === 'false'">
          <a nz-button nzType="primary"
          [ngClass]="{'disabled': isArchiveUser === 'true'}"
          [routerLink]="isArchiveUser === 'true' ? null : '../file-storage-config-quota'">
          <i nz-icon nzType="plus"
              nzTheme="outline"></i>
              创建文件系统
            </a>
      </div>
        <!-- <div class="dilatation" *ngIf="isAdmin === 'true'">
          <a nz-button nzType="primary"
          [routerLink]="'../dilatation-config'">
          <i nz-icon nzType="edit"
              nzTheme="outline"></i>
              扩容
           </a>
        </div> -->
      </div>
      <nz-table #tableList style="overflow-x: auto"
        [nzItemRender]="renderItemTemplate"
        [nzLoading]="isLoading"
        [nzLoadingDelay]="300"
        [nzFrontPagination]="false"
        [nzTotal]="pager.total"
        [nzPageIndex]="pager.page"
        [nzPageSize]="pager.pageSize"
        (nzPageIndexChange)="pageChanged($event)"
        (nzQueryParams)="onParamsChange($event)"
        [nzData]="tableListData">
        <thead>
          <tr>
            <ng-container *ngFor="let col of cols">
              <th
                *ngIf="col.width"
                nz-resizable
                nzBounds="window"
                nzPreview
                nzBreakWord="true"
                [nzWidth]="col.width"
                [nzMaxWidth]="600"
                [nzMinWidth]="80"
                [nzShowSort]="col.showSort"
                [nzSortFn]="col.sortFlag"
                [nzSortOrder]="col.allowSort"
                [nzColumnKey]="col.ColumnKey"
                (nzResizeEnd)="onResize($event, col.title)"
              >
                {{ col.title }}
                <nz-resize-handle nzDirection="right">
                  <div class="resize-trigger"></div>
                </nz-resize-handle>
              </th>
              <th *ngIf="!col.width" style="min-width: 160px;">
                {{ col.title }}
              </th>
            </ng-container>
            <!-- <th width="20%">文件系统</th>
            <th>状态</th>
            <th>容量</th>
            <th width="20%">共享目录</th>
            <th>更多操作</th> -->
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of tableList.data; index as i">
            <td width="20%">
                <span style="word-break: break-all;">{{data.name}}</span>
            </td>
            <!-- <td width="15%">
              <span style="word-break: break-all;">{{data.ipLists || "-"}}</span>
            </td> -->
            <!-- <td>{{data.status || "-"}}</td> -->
            <td>{{data.sizeG || "-"}} GB</td>
            <td>
              <span style="word-break: break-all; " class="pathSpan1">{{data.sharePath || "-"}}</span>
              <div class="on-table-actions pathSpan2">
                <div class="on-table-action-item"
                  (click)="copy(data.sharePath)">
                  <i nzTitle="复制mount命令"
                      nzPlacement="bottom"
                      nz-tooltip nz-icon
                      nzType="copy"  nzTheme="outline"></i>
                </div>
              </div>
              <input id="copy_content" type="text" value=""  style="position: absolute;top: 0;left: 0;opacity: 0;z-index: -10;"/>
            </td>
            <!-- <td>{{data.createTime || "-"}}</td>
            <td>{{data.updateTime || "-"}}</td> -->
            <td>
                <div class="on-table-actions"
                  [hidden]="busyStatus[data.id]">
                  <div class="on-table-action-item"
                    (click)="getDetail(data);">
                    <i nzTitle="查看详情"
                        nzPlacement="bottom"
                        nz-tooltip
                        class="icon fa fa-search"></i>
                  </div>
                  <!-- <div class="on-table-action-item"
                      (click)="showIpList(data)">
                      <i nzTitle="用户白名单"
                          nzPlacement="bottom"
                          nz-tooltip
                          class="icon fa fa-file-text-o"></i>
                  </div> -->
                  <!-- <div class="on-table-action-item"
                      (click)="expansion(data)">
                      <i nzTitle="扩容"
                          nzPlacement="bottom"
                          nz-tooltip
                          class="icon fa fa-external-link"></i>
                  </div> -->
                  <div class="on-table-action-item"
                    nz-popconfirm
                    nzPlacement="top"
                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                    [nzTitle]="isArchiveUser === 'true'?null:'文件系统删除后，内部数据会一起删除并且无法恢复，确定要删除该文件系统吗？'"
                    (nzOnConfirm)="isArchiveUser === 'true'?null:delete(data);">
                    <i nzTitle="删除"
                        nzPlacement="bottom"
                        nz-tooltip
                        class="icon fa fa-trash-o"></i>
                  </div>
              </div>
            <div class="on-table-actions"
                [hidden]="!busyStatus[data.id]">
                <div
                    class="action-loading-placeholder">
                    <i class="icon" nz-icon
                        [nzType]="'loading'"></i>
                    {{ getBusyText(data) }}
                </div>
            </div>
            </td>
          </tr>
          <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
        </tbody>
      </nz-table>
      <ng-template #renderItemTemplate let-type let-page="page">
        <a *ngIf="type === 'prev'">« 上一页</a>
        <a *ngIf="type === 'next'">下一页 »</a>
        <a *ngIf="type === 'page'">{{ page }}</a>
      </ng-template>
    </div>
  </div>
</div>
<!--创建文件存储弹出框-->
<nz-modal [(nzVisible)]="showCreateWindow" nzTitle="创建文件系统"
          (nzOnCancel)="showCreateWindow = false"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="confirmCreateWindow()">
  <ng-container *nzModalContent>
  <div class="tc-content">
    <div class="select-container">
      <span class="select-tips">名称：</span>
      <input nz-input
        style="width: 300px"
        (blur)="checkName($event)"
        maxlength="63"
        placeholder="请填写文件系统名称"
        [disabled]="isLoading"
        [(ngModel)]="name">
      <ol class="folder-tips" [ngClass]="{'folder-tips-error': !nameCheck}">
        <li class="upload-tips">名称仅允许包含小写字母, 数字, -</li>
        <li class="upload-tips">需要以小写字母或数字开头和结尾</li>
        <li class="upload-tips">长度为3-63个字符</li>
      </ol>
    </div>
    <div class="select-container">
      <span class="select-tips">容量：</span>
      <nz-input-number [nzPrecision]=0 [(ngModel)]="size" [nzMin]="10" [nzMax]="500" [nzStep]="1"></nz-input-number>
      <span class="select-tip"> GB (范围：10-500GB)</span>
    </div>
  </div>
  </ng-container>
</nz-modal>
<!--用户白名单弹出框-->
<!-- <nz-modal [(nzVisible)]="showIpListWindow" [nzTitle]="ipListTitleTemplate"
          (nzOnCancel)="showIpListWindow = false"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="confirmIplistWindow()">
  <ng-template #ipListTitleTemplate>
    <span class="modal-title">{{'文件系统 ' + operateObj.name + ' 用户白名单'}}</span>
  </ng-template>
  <div class="tc-content">
    <div class="select-container">
      <span class="select-tips" style="vertical-align: top;width: 150px;">用户白名单(最多16组)：</span>
      <div style="display: inline-block">
        <div *ngFor="let data of ipWhiteList; index as i">
          <input nz-input
              style="width: 250px;"
                placeholder="请输入用户白名单"
                [disabled]="isLoading"
                class="ip-input {{ data.check ? '' : 'error-input' }}"
                [(ngModel)]="data.ip">
          <i class="fa fa-times-circle choosable-delete-icon"
              *ngIf="!showAlertDefDetailWindow"
              nzTitle="删除用户白名单"
              nzPlacement="bottomCenter"
              nz-tooltip (click)="deleteIP(i)">
          </i>
        </div>
        <p class="choosable-add-text" (click)="addIP()">
            <i class="fa fa-plus-circle"></i>
            <span>添加用户白名单</span>
        </p>
      </div>
    </div>
  </div>
</nz-modal> -->
<!--扩容弹出框-->
<!-- <nz-modal [(nzVisible)]="showChangeSizeWindow" [nzTitle]="expanTitleTemplate"
          (nzOnCancel)="showChangeSizeWindow = false"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="confirmExpan()">
  <ng-template #expanTitleTemplate>
    <span class="modal-title">{{'文件系统 ' + operateObj.name + ' 扩容'}}</span>
  </ng-template>
  <div class="tc-content">
    <div class="select-container">
      <span class="select-tips">总容量增加至：</span>
      <nz-input-number [nzPrecision]=0 [(ngModel)]="size" [nzMin]="minSize" [nzMax]="500" [nzStep]="1"></nz-input-number>
      <span class="select-tip"> GB (范围：10-500GB)</span>
      <p class="warning-text">**容量只能增大不能减小,且只能为整数！**</p>
    </div>
  </div>
</nz-modal> -->
<!--文件存储详情弹出框-->
<nz-modal [(nzVisible)]="showDetailWindow" [nzTitle]="detailTitleTemplate"
          [nzCancelText]=null
          nzOkText="关闭"
          (nzOnCancel)="showDetailWindow = false"
          (nzOnOk)="showDetailWindow = false">
  <ng-template #detailTitleTemplate>
    <span class="modal-title">{{'文件系统 ' + operateObj.name + ' 详情'}}</span>
  </ng-template>
  <ng-container *nzModalContent>
  <div class="tc-content">
    <div class="select-container">
      <span class="select-tips">名称：</span>
      <span class="select-value">{{detailData.name || '-'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">总容量：</span>
      <span class="select-value">{{detailData.total || '0'}}</span>
    </div>
    <div class="select-container">
      <span class="select-tips">已用容量约为：</span>
      <span class="select-value">{{detailData.used || '0'}}</span>
    </div>
    <!-- <div class="select-container">
      <span class="select-tips">剩余容量约为：</span>
      <span class="select-value">{{detailData.avail || '0'}}</span>
    </div> -->
    <!-- <div class="select-container">
      <span class="select-tips" style="vertical-align: top">IP白名单：</span>
      <div class="select-tips-info">
        <span style="display: inline-block; margin-right: 10px;" *ngFor="let data of detailData.ips; index as i">
          {{data || '-'}}
        </span>
      </div>
    </div> -->
  </div>
  </ng-container>
</nz-modal>
