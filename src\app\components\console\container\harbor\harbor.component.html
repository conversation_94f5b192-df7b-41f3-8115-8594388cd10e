<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../harbor">容器服务</a></li>-->
<!--        <li><span>Harbor(私有)</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <div class="right-button-group">
                <div class="pull-right">
                    <a nz-button disabled
                       [ngClass]="{'disabled': resLimit}"
                       [routerLink]="resLimit ? null : '../harbor-config'">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建Harbor
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">
                    Harbor（私有）
                    <div class="danger" *ngIf="showResLimit">
                        <i class="fa fa-exclamation-circle"></i>
                        每个用户最多可免费创建1个Harbor集群，如有其它需求请提交工单联系！
                    </div>
                </span>
            </div>
            <nz-table #tableList [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzItemRender]="renderItemTemplate"
                [nzFrontPagination]="false"
                [nzTotal]="page.total"
                [nzPageIndex]="page.current"
                [nzPageSize]="page.size"
                (nzPageIndexChange)="pageChange($event)"
                [nzData]="tableListData">
                <thead>
                    <tr>
                        <th width="15%">集群名称</th>
                        <th>集群版本</th>
                        <th>网络</th>
                        <th>子网</th>
                        <th>负载均衡</th>
                        <th>状态</th>
                        <th>节点数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.clusterName || '无'}}</td>
                        <td>{{data.version}}</td>
                        <td>{{data.vpcNetName}}</td>
                        <td>{{data.subNetName}}</td>
                        <td>{{data.loadBalanceName ? data.loadBalanceName : '无'}}
                        </td>
                        <td>
                            <span class="dot {{ getStatusClass(data) }}">{{ getStatusText(data) }}</span>
                        </td>
                        <td>{{data.nodeNum}}</td>
                        <td>{{getdate(data.createTm)}}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[data.id]">
                                <div class="on-table-action-item"
                                    (click)="toDetail(data);"
                                    [ngClass]="{'disabled': !canGoToDetail(data)}">
                                    <i nzTitle="查看详情"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-search"></i>
                                </div>
                                <div class="on-table-action-item"
                                    (click)="turnOn(data);"
                                    [ngClass]="{'disabled': !canTurnOn(data)}">
                                    <i nzTitle="启动"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-play-circle-o"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzCondition]="!canDelete(data)"
                                    nzTitle="确定要删除该集群吗？"
                                    (nzOnConfirm)="delete(data);"
                                    [ngClass]="{'disabled': !canDelete(data)}">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[data.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(data) }}
                                </div>
                            </div>


                            <!-- <div class="table-btn-box"
                    [ngClass]="{'disabled-btn':data.status !== 2}">
                <a href="javascript:void(0);"
                    [hidden]="loadingMap[data.id] && loadingMap[data.id] == 'delete'"
                    (click)="data.status === 2 && toDetail(data.id)"
                    nzTitle="{{ data.status === 2 ? '查看详情' : '' }}"
                    nzTooltipContent="bottomCenter"
                    nz-tooltip>
                  <span class="fa fa-search"></span>
                </a>
              </div>
              <div class="table-btn-box"
                    [ngClass]="{'disabled-btn':data.status === 1}">
                <a href="javascript:void(0);"
                    [hidden]="loadingMap[data.id] && loadingMap[data.id] == 'delete'"
                    (click)="data.status !== 1 && delete(data)"
                    nzTitle="{{ data.status !== 1 ? '删除' : '' }}"
                    nzTooltipContent="bottomCenter"
                    nz-tooltip>
                  <span class="fa fa-trash-o"></span>
                </a>
              </div> -->
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'pre'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>

    </div>
</div>