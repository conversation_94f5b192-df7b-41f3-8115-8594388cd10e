<nz-modal [(nzVisible)]="isVisible" [nzMaskClosable]="false"
          [nzTitle]="'新增节点池'"
          nzOkText="创建" [nzOkLoading]="isCreating"
          [nzWidth]="1000"
          [nzBodyStyle]="{padding: '0 24px'}"
          (nzAfterOpen)="modalOpened()"
          (nzOnCancel)="handleCancel()"
          (nzOnOk)="addOrUpdate()">
    <ng-container *nzModalContent>
    <form [formGroup]="nodePoolForm" class="config-content md network-form">
        <section class="field-section">
            <div class="field-title">
                基本信息
            </div>
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">节点池名称</span>
                        <input nz-input type="text"
                               formControlName="name"
                               maxlength="50"
                               placeholder="请输入节点池名称">
                    </label>
                    <div *ngIf="isInvalid(nodePoolForm.get('name'))"
                         class="form-hint error">
                        <div *ngIf="nodePoolForm.get('name').hasError('required')">
                            节点池名称不能为空
                        </div>
                        <div *ngIf="nodePoolForm.get('name').hasError('maxlength')">
                            节点池名称长度不能超过{{ nodePoolForm.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-title">
                节点配置
            </div>
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">计费方式</span>
                        <nz-radio-group [(ngModel)]="selectedChargeType" [ngModelOptions]="{standalone: true}">
                            <label nz-radio-button [nzValue]="item.key" *ngFor="let item of chargeType | keyvalue">
                                {{ item.value }}
                            </label>
                        </nz-radio-group>
                    </label>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">可用区</span>
                        <nz-radio-group formControlName="resourceRegion" nzSize="middle">
                            <label *ngFor="let item of resourceRegionList"
                                   [nzValue]="item.region"
                                   nz-radio-button>{{ item.name }}</label>
                        </nz-radio-group>
                    </label>
                    <div *ngIf="isInvalid(nodePoolForm.get('resourceRegion'))"
                         class="form-hint error">
                        <div *ngIf="nodePoolForm.get('resourceRegion').hasError('required')">
                            请选择可用区
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">节点规格</span>
                        <nz-radio-group formControlName="type" nzSize="middle" (ngModelChange)="changeType()">
                            <label *ngFor="let item of serverStandardTypeList"
                                   [nzValue]="item.key"
                                   nz-radio-button>{{ item.title }}</label>
                        </nz-radio-group>
                    </label>
                    <label>
                        <div align="right" style="margin: 10px 0 5px" *ngIf="false">
                            <input nz-input type="text"
                                   formControlName="flavor"
                                   [(ngModel)]="searchFlavor"
                                   maxlength="50"
                                   placeholder="查询规格名称">
                        </div>
                        <div style="max-height: 250px;overflow: auto">
                            <nz-radio-group formControlName="flavor" nzSize="middle" style="width:850px;float:right;">
                                <label *ngFor="let item of flavorList" style="margin: 5px;padding: 0 10px; width:400px;"
                                       [nzValue]="item.name" [hidden]="searchFlavor && item.name.indexOf(searchFlavor) === -1"
                                       nz-radio-button>{{ item.name }} (CPU:{{item.cpuUnit}} | MEMORY:{{item.memoryUnit}})</label>
                            </nz-radio-group>
                        </div>
                    </label>
                    <div *ngIf="isInvalid(nodePoolForm.get('flavor'))"
                         class="form-hint error">
                        <div *ngIf="nodePoolForm.get('flavor').hasError('required')">
                            请选择规格
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">容器引擎</span>
                        <nz-radio-group [(ngModel)]="containerEngine" [ngModelOptions]="{standalone: true}">
                            <label nz-radio-button [nzValue]="item.key" *ngFor="let item of containerEngineType | keyvalue">
                                {{ item.value }}
                            </label>
                        </nz-radio-group>
                    </label>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">操作系统</span>
                        <nz-radio-group [(ngModel)]="os" [ngModelOptions]="{standalone: true}">
                            <label nz-radio-button [nzValue]="item.key" *ngFor="let item of osType | keyvalue">
                                {{ item.value }}
                            </label>
                        </nz-radio-group>
                    </label>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">密码</span>
                        <div class="label-padding">
                            <p class="small tip">
                                登录帐号是 root(拥有sudo权限)，通过支持 ssh 的工具来访问您的云服务器
                            </p>
                            <div class="input-group-suffix">
                                <input [type]="passwordVisible ? 'text' : 'password'"
                                       formControlName="password"
                                       (focus)="checkPasswordStatus()"
                                       (input)="validatePassword()"
                                       nz-tooltip
                                       nzTooltipTitle="密码丢失将无法找回，请谨慎保管！"
                                       nzTooltipTrigger="focus"
                                       placeholder="请输入密码" (keydown.enter)="keyEnter()" class="clspass">
                                <i class="suffix" [title]="passwordVisible ? '隐藏密码' : '查看密码'" nz-icon
                                   [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
                                   (click)="passwordVisible = !passwordVisible"></i>
                            </div>
                            <span class="small tip">
                            密码须包含大小写字母和数字，支持常用特殊字符，长度为8-20位
                        </span>
                            <div class="password-hint" [ngClass]="{'unfold': passwordStatus.focus}">
                                <ul>
                                    <li [ngClass]="{'checked': passwordStatus.uppercase}">
                                        <i class="icon" nz-icon [nzType]="passwordStatus.uppercase ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                        至少包含一个大写字母
                                    </li>
                                    <li [ngClass]="{'checked': passwordStatus.lowercase}">
                                        <i class="icon" nz-icon [nzType]="passwordStatus.lowercase ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                        至少包含一个小写字母
                                    </li>
                                    <li [ngClass]="{'checked': passwordStatus.digital}">
                                        <i class="icon" nz-icon [nzType]="passwordStatus.digital ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                        至少包含一个数字
                                    </li>
                                    <li [ngClass]="{'checked': passwordStatus.length}">
                                        <i class="icon" nz-icon [nzType]="passwordStatus.length ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                        长度在8-26位之间
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="form-hint error" *ngIf="isInvalid(nodePoolForm.get('password'))">
                            <div *ngIf="nodePoolForm.get('password').hasError('required')">
                                登录密码不能为空
                            </div>
                            <div *ngIf="nodePoolForm.get('password').hasError('pwError')">
                                密码格式不符合要求
                            </div>
                        </div>
                    </label>
                </div>
            </div>
            <div class="field-title">
                存储配置
            </div>
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span class="label-text">系统盘</span>
                        <nz-input-number [nzPrecision]=0 formControlName="rootVolumeSize" [nzMin]="40" [nzMax]="1024" [nzStep]="1">
                        </nz-input-number> GB
                    </label>
                    <div *ngIf="isInvalid(nodePoolForm.get('rootVolumeSize'))"
                         class="form-hint error">
                        <div *ngIf="nodePoolForm.get('rootVolumeSize').hasError('required')">
                            请输入系统盘大小
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">数据盘</span>
                        <nz-input-number [nzPrecision]=0 formControlName="dataVolumeSize" [nzMin]="20" [nzMax]="32768" [nzStep]="1">
                        </nz-input-number> GB
                    </label>
                </div>
            </div>

            <div class="field-title">
                网络配置
            </div>
            <div class="field-group">
                <div class="field-item">
                    <label>
                        <span class="label-text">VPC</span>
                        <span>{{bean.spVpcName}}</span>
                    </label>

                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">子网</span>
                        <nz-radio-group formControlName="hostNetworkSubnetId" nzSize="middle" (ngModelChange)="changeType()">
                            <label *ngFor="let item of subnetList"
                                   [nzValue]="item.id"
                                   nz-radio-button>{{ item.name }}</label>
                        </nz-radio-group>
                    </label>
                    <div *ngIf="isInvalid(nodePoolForm.get('rootVolumeSize'))"
                         class="form-hint error">
                        <div *ngIf="nodePoolForm.get('rootVolumeSize').hasError('required')">
                            请选择子网
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">节点IP</span>
                        <nz-radio-group [(ngModel)]="ip" [ngModelOptions]="{standalone: true}">
                            <label nz-radio-button [nzValue]="item.key" *ngFor="let item of ips | keyvalue">
                                {{ item.value }}
                            </label>
                        </nz-radio-group>
                    </label>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">安全组</span>
                        <nz-radio-group [(ngModel)]="security" [ngModelOptions]="{standalone: true}">
                            <label nz-radio-button [nzValue]="item.key" *ngFor="let item of securitys | keyvalue">
                                {{ item.value }}
                            </label>
                        </nz-radio-group>
                    </label>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>
