<nz-modal [(nzVisible)]="isVisible" [nzMaskClosable]="false"
    [nzTitle]="type === 'vpc' ? '创建专有网络' : '创建子网'"
    nzOkText="创建" [nzOkLoading]="isCreating"
    [nzWidth]="650"
    [nzBodyStyle]="{padding: '0 24px'}"
    (nzAfterOpen)="modalOpened()"
    (nzOnCancel)="handleCancel()"
    (nzOnOk)="createVpc(type)">
    <ng-container *nzModalContent>
    <form [formGroup]="network" class="config-content md network-form dialog">
        <section class="field-section" [hidden]="!(type === 'vpc')">
            <div class="field-title">
                <div></div>
                基本信息
            </div>
            <div class="field-group">
                <div class="field-item required">
                    <label class="w-75">
                        <div
                            class="label-text">专有网络名称</div>
                        <input nz-input type="text"
                            formControlName="name"
                            maxlength="50"
                            placeholder="请输入专有网络名称">
                    </label>
                    <div *ngIf="isInvalid(network.get('name'))"
                        class="form-hint error">
                        <div
                            *ngIf="network.get('name').hasError('required')">
                            专有网络名称不能为空
                        </div>
                        <div
                            *ngIf="network.get('name').hasError('pattern')">
                            专有网络名称必须以字母开头，只能输入英文、数字、中划线
                        </div>
                        <div
                            *ngIf="network.get('name').hasError('maxlength')">
                            专有网络名称长度不能超过{{ network.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="field-section"
            formArrayName="ovdcNetworkList">
            <div class="field-title">
                <div></div>
                子网配置
            </div>
            <div class="field-group" [formGroupName]="0">
                <div class="field-item required">
                    <label class="w-75">
                        <div class="label-text">子网名称</div>
                        <input nz-input type="text"
                            formControlName="name"
                            maxlength="50"
                            placeholder="请输入子网名称">
                    </label>
                    <div *ngIf="isInvalid(network.get('ovdcNetworkList').get('0').get('name'))"
                        class="form-hint error">
                        <div
                            *ngIf="network.get('ovdcNetworkList').get('0').get('name').hasError('required')">
                            子网名称不能为空
                        </div>
                        <div
                            *ngIf="network.get('ovdcNetworkList').get('0').get('name').hasError('pattern')">
                            子网名称必须以字母开头，只能输入英文、数字、中划线
                        </div>
                        <div
                            *ngIf="network.get('ovdcNetworkList').get('0').get('name').hasError('maxlength')">
                            子网名称长度不能超过{{ network.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label class="w-75">
                        <div class="label-text">子网可用区</div>
                        <nz-select formControlName="resourceRegion" nzPlaceHolder="请选择子网可用区">
                            <nz-option nzCustomContent *ngFor="let item of resourceRegionList" [nzValue]="item.region" [nzLabel]="item.name">
                                <div class="image-item" [title]="item.name">{{ item.name }}</div>
                            </nz-option>
                        </nz-select>
                    </label>
                    <div *ngIf="isInvalid(network.get('ovdcNetworkList').get('0').get('az'))" class="form-hint error">
                        <div *ngIf="network.get('ovdcNetworkList').get('0').get('az').hasError('required')">
                            请选择子网可用区
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <div class="label-text">协议类型</div>
                        <nz-radio-group name="ipVersion" formControlName="ipVersion">
                            <div>
                                <label nz-radio nzValue="IPv4"  style="margin-right: 8px;">IPv4</label>
                            </div>
                        </nz-radio-group>
                    </label>

                </div>
                <div formArrayName="ipScopeList">
                    <div >
                        <div class="field-item required field-item-filled" [formGroupName]="0">
                            <label>
                                <div class="label-text">子网网段</div>
                                <div class="input-filled">
                                    <nz-radio-group name="radioName" (ngModelChange)="radioChange($event)" formControlName="gateway">
                                        <div *ngFor="let item of allItem; let id = index">
                                        <label nz-radio nzValue="{{id}}"  style="margin-right: 8px;"></label>
                                            <input nz-input placeholder="10"
                                                class="ip-item"
                                                type="number"
                                                [disabled]="!item.editable1"
                                                [(ngModel)]="item.value1"
                                                [ngModelOptions]="{standalone: true}"/>
                                            <span class="divider">.</span>
                                            <input nz-input placeholder="0"
                                                class="ip-item"
                                                type="number"
                                                [nzTooltipTitle]="item.range2"
                                                nzTooltipContent="top"
                                                nzTooltipTrigger="focus"
                                                nz-tooltip
                                                [disabled]="!item.editable2"
                                                [(ngModel)]="item.value2"
                                                (ngModelChange)="validateSubNetwork(id)"
                                                [ngModelOptions]="{standalone: true}"/>
                                            <span class="divider">.</span>
                                            <input nz-input placeholder="0"
                                                class="ip-item"
                                                type="number"
                                                [nzTooltipTitle]="item.range3"
                                                nzTooltipContent="top"
                                                nzTooltipTrigger="focus"
                                                nz-tooltip
                                                [disabled]="!item.editable3"
                                                [(ngModel)]="item.value3"
                                                (ngModelChange)="validateSubNetwork(id)"
                                                [ngModelOptions]="{standalone: true}"/>
                                            <span class="divider">.</span>
                                            <input nz-input placeholder="0"
                                                class="ip-item"
                                                type="number"
                                                [disabled]="!item.editable4"
                                                [(ngModel)]="item.value4"
                                                [ngModelOptions]="{standalone: true}"/>
                                            <span class="divider">/</span>
                                            <nz-select
                                                style="width: 80px !important"
                                                [(ngModel)]="item.maskBit"
                                                [ngModelOptions]="{standalone: true}"
                                                (ngModelChange)="maskBitChange($event, id)"
                                                nzPlaceHolder="请选择子网掩码位数">
                                                <nz-option
                                                    *ngFor="let item2 of item.maskBitList"
                                                    [nzValue]="item2"
                                                    [nzLabel]="item2">
                                                </nz-option>
                                            </nz-select>
                                            <div class="form-hint error" *ngIf="subNetworkError && radioID == id.toLocaleString()" style="padding-left: 0px;">
                                                {{ subNetworkError }}
                                            </div>
                                    </div>
                                    </nz-radio-group>
                                </div>

                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>
