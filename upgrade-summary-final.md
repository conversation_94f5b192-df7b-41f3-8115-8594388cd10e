# Angular 16 到 Angular 17 升级总结

## 升级内容

1. **Angular核心包升级**
   - 从Angular 16.2.12升级到Angular 17.2.3
   - 使用命令：`npm install @angular/core@17 @angular/cli@17 --save --legacy-peer-deps`

2. **ng-zorro-antd升级**
   - 从ng-zorro-antd 16.2.2升级到ng-zorro-antd 17.2.0
   - 使用命令：`npm install ng-zorro-antd@17 --save --legacy-peer-deps`

3. **Angular CDK升级**
   - 从@angular/cdk 16.2.12升级到@angular/cdk 17.2.3
   - 使用命令：`npm install @angular/cdk@17 --save --legacy-peer-deps`

4. **其他依赖升级**
   - 升级所有Angular相关包到17.2.3版本
   - ngx-filesaver从16.0.0升级到17.0.0
   - ngx-bootstrap从11.0.2升级到12.0.0
   - angularx-qrcode从16.0.0升级到17.0.0
   - TypeScript从5.1.6升级到5.4.5

5. **新增依赖**
   - 添加@angular-eslint/builder和相关ESLint依赖，用于替代已弃用的tslint

6. **代码修改**
   - 更新main.ts文件，添加Angular 17的启动方式支持
   - 创建app.config.ts文件，用于Angular 17的应用配置
   - 更新tsconfig.json和tsconfig.base.json文件，添加esModuleInterop和strictInputAccessModifiers选项
   - 更新polyfills.ts文件，移除不再需要的IE11支持
   - 更新.browserslistrc文件，移除IE支持
   - 更新angular.json文件，将tslint替换为eslint
   - 创建.eslintrc.json文件，配置ESLint规则
   - 修复模板中的@符号问题，使用HTML实体&#64;替代
   - 修复marked和clipboard库的导入和使用方式

## 升级结果

1. **Node.js版本要求**
   - Angular 17需要Node.js 18.13.0或更高版本
   - 项目现在使用Node.js 18.20.7版本

2. **构建和开发服务器**
   - 更新了package.json中的scripts，添加了lint命令
   - 保留了原有的构建和启动命令

3. **新的应用架构**
   - 添加了app.config.ts文件，用于Angular 17的应用配置
   - 更新了main.ts文件，支持Angular 17的启动方式
   - 使用ESLint替代了TSLint进行代码检查

4. **性能改进**
   - Angular 17提供了更好的构建性能和运行时性能
   - 减少了应用程序的包大小
   - 改进了变更检测机制

## 遇到的问题及解决方案

1. **Node.js版本兼容性**
   - 问题：Angular 17需要Node.js 18.13.0或更高版本，而当前系统使用的是Node.js 16.20.2
   - 解决方案：安装Node.js 18.20.7版本

2. **ESLint替代TSLint**
   - 问题：Angular 17已经弃用TSLint，推荐使用ESLint
   - 解决方案：安装@angular-eslint/builder和相关依赖，创建.eslintrc.json配置文件

3. **TypeScript配置更新**
   - 问题：Angular 17需要更新的TypeScript配置
   - 解决方案：更新tsconfig.json和tsconfig.base.json文件，添加esModuleInterop和strictInputAccessModifiers选项

4. **浏览器兼容性**
   - 问题：Angular 17不再支持IE浏览器
   - 解决方案：更新.browserslistrc文件，移除IE支持

5. **依赖安装问题**
   - 问题：由于依赖关系复杂，直接使用ng update命令可能会失败
   - 解决方案：使用npm install命令手动安装各个依赖，并使用--legacy-peer-deps标志解决依赖冲突

6. **模板中的@符号问题**
   - 问题：在Angular 17中，@符号在模板中有特殊含义，会导致编译错误
   - 解决方案：使用HTML实体&#64;替代@符号

7. **库导入和使用方式变更**
   - 问题：marked和clipboard库的导入和使用方式在Angular 17中需要更新
   - 解决方案：
     - marked：使用marked.parse()替代直接调用marked()
     - clipboard：正确导入ClipboardJS

## 测试结果

1. **开发服务器**
   - 使用`npm start`命令成功启动开发服务器
   - 应用程序可以正常运行，没有编译错误

2. **生产构建**
   - 使用`npm run build`命令成功构建生产版本
   - 构建过程没有错误，生成的文件可以正常部署

3. **代理配置**
   - 保留了原有的代理配置，可以正常连接到后端服务
   - 当后端服务未运行时，会显示代理错误，这是正常的

## 后续建议

1. **代码优化**
   - 使用Angular 17的新特性优化代码
   - 使用standalone组件简化模块结构
   - 使用信号（Signals）API改进状态管理

2. **性能优化**
   - 使用Angular 17的构建优化功能减小包大小
   - 实现延迟加载和预加载策略
   - 使用Angular 17的服务器端渲染（SSR）功能

3. **测试改进**
   - 更新测试框架和测试用例
   - 使用Angular 17的测试工具
   - 增加单元测试和端到端测试覆盖率
