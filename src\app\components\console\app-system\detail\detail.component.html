<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="/console/quota/index">配额管理</a></li>-->
<!--        <li><a routerLink="/console/quota/index">资源总览</a></li>-->
<!--        <li><span>{{dataSource.resourceTypeText}}</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">{{dataSource.resourceTypeText}}详情</h3>
            <div class="on-panel-container">
                <span class="on-panel-tips">已使用：</span>
                <span class="dot dot-title"
                      [ngClass]="{'dot-red': dataSource.usedQuota/dataSource.assignedQuota >= 1, 'dot-yellow': dataSource.usedQuota/dataSource.assignedQuota >= 0.8 && dataSource.usedQuota/dataSource.assignedQuota < 1, 'dot-green': dataSource.usedQuota || dataSource.usedQuota === 0}">{{dataSource.usedQuota}}</span>
                <span class="on-panel-tips">已分配：</span>
                <span class="dot dot-title"
                      [ngClass]="{'dot-red': dataSource.assignedQuota/dataSource.quota >= 1, 'dot-yellow': dataSource.assignedQuota/dataSource.quota >= 0.8 && dataSource.assignedQuota/dataSource.quota < 1, 'dot-green': dataSource.assignedQuota || dataSource.assignedQuota === 0}">{{dataSource.assignedQuota}}</span>
                <span class="on-panel-tips">总配额：</span>
                <span>{{dataSource.quota}}</span>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <div class="pull-right">
                    <button nz-button nzType="primary"
                        [ngClass]="{'disabled': isArchiveUser === 'true'}"
                            (click)="addQuota();">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        新建配额
                    </button>
                </div>
            </div>
            <nz-table
                #quotaData
                [nzShowPagination]="false"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzData]="dataList || []">
                <thead>
                    <tr>
                        <th>账户</th>
                        <th>已使用</th>
                        <th>已分配</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody *ngIf="quotaData.data.length">
                    <tr *ngFor="let item of quotaData.data; trackBy: trackById">
                        <td>{{ item.ownerName }}</td>
                        <td><span class="dot"
                                  [ngClass]="{'dot-red': item.usedQuota/item.quota >= 1, 'dot-yellow': item.usedQuota/item.quota >= 0.8 && item.usedQuota/item.quota < 1, 'dot-green': item.usedQuota || item.usedQuota === 0}">
                                {{item.usedQuota}}
                            </span>
                        </td>
                        <td>{{ item.quota }}</td>
                        <td>
                            <div class="on-table-actions"
                                 [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                     (click)="changeQuota(item);">
                                        <i nzTitle="调制配额"
                                           nzTooltipContent="bottom"
                                           nz-tooltip
                                           nz-icon
                                           nzType="edit"
                                           nzTheme="outline"
                                           class="icon"></i>
                                </div>
<!--                                <div class="on-table-action-item"-->
<!--                                     nz-popconfirm-->
<!--                                     nzTooltipContent="top"-->
<!--                                     nzPopconfirmTitle="确定要删除账户{{item.ownerName}}的配额吗？删除后将使用默认配额"-->
<!--                                     (nzOnConfirm)="deleteQuota(item);"-->
<!--                                     >-->
<!--                                    <i nzTooltipTitle="删除"-->
<!--                                       nzTooltipContent="bottom"-->
<!--                                       nz-tooltip-->
<!--                                       class="icon fa fa-trash-o">-->
<!--                                    </i>-->
<!--                                </div>-->

                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="quotaData.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
        </div>
    </div>
</div>


<nz-modal [(nzVisible)]="editQuotaWindow" nzTitle="配额调整"
          (nzOnCancel)="editQuotaWindow = false" [nzWidth]="500"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submit()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">选择用户：</span>
        <nz-checkbox-group class="action-bar-btn" [(ngModel)]="userList" (ngModelChange)="compare($event)" [disabled]="editId > 0">
        </nz-checkbox-group>
    </div>
    <div class="select-container">
        <span class="select-tips">默认配额：</span>
        <span>{{defaultQuota}}</span>
    </div>
    <div class="select-container">
        <span class="select-tips">设置配额：</span>
        <span><nz-input-number [nzPrecision]=0 [(ngModel)]="editItem.quota" [nzMin]="0" [nzStep]="1">
                        </nz-input-number></span>
    </div>
    </ng-container>
</nz-modal>
