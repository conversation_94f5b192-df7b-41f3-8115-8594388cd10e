<div class="horizontal-column-chart">
  <div class="chart-title">{{title}}</div>
  <div class="chart-container" [style.height.px]="chartHeight">
    <div class="chart-bar" *ngFor="let item of formattedData"
         [style.width]="item.width"
         [style.background-color]="item.color"
         (mouseover)="showTooltip(item, $event)"
         (mouseout)="hideTooltip()">
      <div class="chart-bar-label">{{item.key}}</div>
      <div class="chart-bar-value">{{item.value}}</div>
    </div>
  </div>

  <div class="tooltip" *ngIf="tooltipData"
       [style.top.px]="tooltipData.top"
       [style.left.px]="tooltipData.left">
    <div>{{tooltipData.key}}</div>
    <div>{{tooltipData.value | number}}</div>
  </div>
</div>
