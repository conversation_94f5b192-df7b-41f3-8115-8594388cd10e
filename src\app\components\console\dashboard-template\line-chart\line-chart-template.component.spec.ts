import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';

import { LineChartTemplateComponent } from './line-chart-template.component';

describe('ManagementComponent', () => {
    let component: LineChartTemplateComponent;
    let fixture: ComponentFixture<LineChartTemplateComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            declarations: [LineChartTemplateComponent]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(LineChartTemplateComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
