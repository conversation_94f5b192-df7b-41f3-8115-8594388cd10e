import { Component, OnIni<PERSON>, On<PERSON><PERSON><PERSON>, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 导入bpmn-js查看器
import BpmnViewer from 'bpmn-js/lib/Viewer';

// 导入中文翻译模块
import TranslateModule from 'src/app/utils/bpmn-translate';

@Component({
    selector: 'app-bpmn-view',
    templateUrl: './bpmn-view.component.html',
    styleUrls: ['./bpmn-view.component.less']
})
export class BpmnViewComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('bpmnContainer', { static: false }) bpmnContainer!: ElementRef;

    private bpmnViewer: any;
    public processId: string | null = null;
    public processData: any = null;
    public loading = false;

    // 默认的BPMN XML模板
    private defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private bpmnService: BpmnService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.processId = this.route.snapshot.paramMap.get('id');

        if (this.processId) {
            this.loadProcessData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initBpmnViewer();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.bpmnViewer) {
            this.bpmnViewer.destroy();
        }
    }

    // 加载流程数据
    private async loadProcessData(): Promise<void> {
        if (!this.processId) return;

        try {
            this.loading = true;
            const response = await this.bpmnService.getTestById(Number(this.processId));

            console.log('API响应:', response);

            if (response.success) {
                this.processData = response.data;
                console.log('流程数据:', this.processData);

                // 确定要导入的XML数据
                let xmlToImport = this.defaultBpmnXml;

                if (this.processData) {
                    // 如果processData是字符串，直接使用
                    if (typeof this.processData === 'string') {
                        xmlToImport = this.processData;
                    }
                    // 如果processData是对象且有bpmnXml属性
                    else if (this.processData.bpmnXml) {
                        xmlToImport = this.processData.bpmnXml;
                    }
                    // 如果processData是对象且有xml属性
                    else if (this.processData.xml) {
                        xmlToImport = this.processData.xml;
                    }
                }

                console.log('将要导入的XML:', xmlToImport);

                // 如果BPMN查看器已初始化，导入XML
                if (this.bpmnViewer) {
                    this.importBpmnXml(xmlToImport);
                }
            } else {
                this.msg.error('加载流程数据失败: ' + (response.message || '未知错误'));
                console.error('API返回失败:', response);
            }
        } catch (error) {
            console.error('加载流程数据失败:', error);
            this.msg.error('加载流程数据失败');

            // 如果API调用失败，使用默认XML
            if (this.bpmnViewer) {
                this.importBpmnXml(this.defaultBpmnXml);
            }
        } finally {
            this.loading = false;
        }
    }

    // 初始化BPMN查看器
    private initBpmnViewer(): void {
        if (!this.bpmnContainer) {
            return;
        }

        try {
            // 创建BPMN查看器实例（只读模式）
            this.bpmnViewer = new BpmnViewer({
                container: this.bpmnContainer.nativeElement,
                additionalModules: [
                    TranslateModule
                ]
            });

            // 如果已经有流程数据，导入XML
            if (this.processData) {
                let xmlToImport = this.defaultBpmnXml;

                if (typeof this.processData === 'string') {
                    xmlToImport = this.processData;
                } else if (this.processData.bpmnXml) {
                    xmlToImport = this.processData.bpmnXml;
                } else if (this.processData.xml) {
                    xmlToImport = this.processData.xml;
                }

                this.importBpmnXml(xmlToImport);
            } else {
                // 如果没有数据，先导入默认XML
                this.importBpmnXml(this.defaultBpmnXml);
            }

        } catch (error) {
            console.error('初始化BPMN查看器失败:', error);
            this.msg.error('初始化BPMN查看器失败');
        }
    }

    // 导入BPMN XML
    private async importBpmnXml(xml: string): Promise<void> {
        if (!this.bpmnViewer) {
            console.error('BPMN查看器未初始化');
            return;
        }

        try {
            console.log('开始导入XML:', xml?.substring(0, 200) + '...');

            // 验证XML是否为空或无效
            if (!xml || xml.trim() === '') {
                console.warn('XML为空，使用默认XML');
                xml = this.defaultBpmnXml;
            }

            await this.bpmnViewer.importXML(xml);
            console.log('XML导入成功');

            // 自适应画布大小
            const canvas = this.bpmnViewer.get('canvas');
            canvas.zoom('fit-viewport');

        } catch (error) {
            console.error('导入BPMN XML失败:', error);
            this.msg.error('导入BPMN XML失败: ' + error.message);

            // 如果导入失败，尝试使用默认XML
            if (xml !== this.defaultBpmnXml) {
                console.log('尝试使用默认XML');
                try {
                    await this.bpmnViewer.importXML(this.defaultBpmnXml);
                    const canvas = this.bpmnViewer.get('canvas');
                    canvas.zoom('fit-viewport');
                } catch (defaultError) {
                    console.error('导入默认XML也失败:', defaultError);
                }
            }
        }
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/bpmn']);
    }

    // 缩放控制
    zoomIn(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom(canvas.zoom() + 0.1);
    }

    zoomOut(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom(canvas.zoom() - 0.1);
    }

    zoomFit(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom('fit-viewport');
    }

    zoomReset(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom(1);
    }

    // 下载BPMN文件
    async downloadBpmn(): Promise<void> {
        try {
            const result = await this.bpmnViewer.saveXML({ format: true });
            const xml = result.xml;

            const blob = new Blob([xml], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.bpmn`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载BPMN文件失败:', error);
            this.msg.error('下载BPMN文件失败');
        }
    }

    // 下载SVG图像
    async downloadSvg(): Promise<void> {
        try {
            const result = await this.bpmnViewer.saveSVG();
            const svg = result.svg;

            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.svg`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载SVG图像失败:', error);
            this.msg.error('下载SVG图像失败');
        }
    }
}