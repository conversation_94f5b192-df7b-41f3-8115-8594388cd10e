import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';
import { DashboardComponent } from 'src/app/components/console/dashboard/dashboard.component';
import { LOCALE_ID } from '@angular/core';

@NgModule({
    declarations: [DashboardComponent],
    imports: [CommonModule, SharedModule, DashboardRoutingModule],
    providers: [
        { provide: LOCALE_ID, useValue: 'zh-CN' }
    ],
})
export class DashboardModule {
    constructor(moduleRef: NgModuleRef<DashboardModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
