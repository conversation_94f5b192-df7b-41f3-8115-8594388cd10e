<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="/console/proprietary-network/index">专有网络</a></li>-->
<!--        <li><span>安全组</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入安全组名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div *ngIf="permission('refresh')">
                    <a nz-button nzType="default" class="default"
                       (click)="refresh()">
                        刷&nbsp;新
                    </a>
                </div>
                <div  *ngIf="permission('export')">
                    <a nz-button nzType="primary" class="primary"
                            [ngClass]="{'disabled': isDeleting}"
                            (click)="navToConfig();">
                        <i nz-icon nzType="plus"
                           nzTheme="outline"></i>
                        创建安全组规则
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">安全组</span>
            </div>
            <nz-table #securityGroup
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="sgList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="80"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        *ngFor="let item of securityGroup.data; trackBy: trackById">
                        <td align="left"><a (click)="detail(item)">{{ item.name }}</a><br/>{{item.resourceId}}</td>
                        <td>{{ item.securityGroupRules.length }}</td>
                        <td></td>
                        <!-- <td>{{ item.securityGroupRelation ? item.securityGroupRelation.length : '-' }}</td> -->
                        <td align="left">{{ item.description }}</td>
<!--                        <td></td>-->
                        <td>{{item.entProjectId }}</td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="viewDetail(item);"
                                    [ngClass]="{'disabled': !canViewDetail(item)}">
                                    <i nzTooltipTitle="详情"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon" nz-icon
                                        nzType="search"
                                        nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzPopconfirmTitle="确定要删除该安全组吗？"
                                    [nzCondition]="!canDeleteSg(item)"
                                    (nzOnConfirm)="deleteSg(item);"
                                    [ngClass]="{'disabled': !canDeleteSg(item)}">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="securityGroup.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<app-security-group-detail
        [isVisible]="isVisible"
        [securityItem]="securityItem"
        type="vpc"
        (close)="isVisible = false">
</app-security-group-detail>