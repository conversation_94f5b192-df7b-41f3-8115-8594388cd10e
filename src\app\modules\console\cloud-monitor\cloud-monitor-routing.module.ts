import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MonitorListComponent } from 'src/app/components/console/cloud-monitor/monitor-list.component';
import { MonitorBrandComponent} from 'src/app/components/console/cloud-monitor/brand/monitor-brand.component';
import { MonitorDetailComponent} from 'src/app/components/console/cloud-monitor/brand/detail/monitor-detail.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'monitor-list',
        pathMatch: 'full',
    },
    {
        path: 'monitor-list',
        component: MonitorListComponent,
    },
    {
        path: 'monitor-brand/:brandName',
        component: MonitorBrandComponent,
    },
    {
        path: 'monitor-brand/:brandName/:instanceName',
        component: MonitorDetailComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CloudMonitorRoutingModule {}
