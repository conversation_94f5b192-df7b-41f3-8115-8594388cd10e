<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">容器服务</a></li>-->
<!--        <li><a routerLink="../harbor">Harbor</a></li>-->
<!--        <li><span>详情</span></li>-->
<!--    </ol>-->
  <div class="on-panel">
      <div class="details-container">
          <!--顶部菜单-->
          <div class="details-header">
            <div class="title-container">
                <p>{{ basicInfor[1].value }}</p>
                <span>{{ basicInfor[6].value }}</span>
                <span class="title-des-separate">{{ basicInfor[7].value }}</span>
            </div>
            <div>
                <span class="operate-btn" (click)="deleteItem('harbor', id)">
                    <i class="fa fa-trash-o"></i><span>删除</span>
                </span>
            </div>
          </div>
          <!--内容菜单-->
          <div class="details-menu">
            <nz-tabset [nzTabPosition]="'top'" [nzType]="'card'"
                        (nzSelectChange)="selectMenu($event)"
                        [nzSelectedIndex]="activeContentIndex">
                    <nz-tab *ngFor="let tab of contentMenuOptions"[nzTitle]="tab.title">
                    </nz-tab>
            </nz-tabset>
          </div>
          <!--基本信息-->
          <div class="content-body-item" *ngIf="activeContentIndex === 0">
            <!--基本信息-->
            <section class="info-container">
                <p>基本信息</p>
                <div class="info-details-container">
                    <div class="info-details-item" *ngFor="let item of basicInfor; index as i">
                        <p>{{ item.title }}</p>
                        <!-- <p class="info-text">{{ item.value }}</p> -->
                        <a [href]="'http://' + elasticIp" *ngIf="i == 8" target="_blank">
                          <p class="info-text">{{ item.value }}</p>
                        </a>
                        <p class="info-text"
                            *ngIf="i !== 8"
                            (click)="item.change === true && showChangeBox(i)">
                            {{ item.value }}
                            <span class="fa fa-pencil" *ngIf="item.change === true"></span>
                        </p>
                    </div>
                </div>
            </section>
            <!--主节点配置-->
            <section class="info-container">
                <p>主节点配置</p>
                <div class="info-details-container">
                    <span class="info-details-item" *ngFor="let item of basicInforHostNode; index as i">
                        <p>{{ item.title }}</p>
                        <p class="info-text">{{ item.value }}</p>
                    </span>
                </div>
            </section>
            <!--AccessKey配置-->
            <section class="info-container" *ngIf="this.basicInfor[11].value !== '-'">
                <p>AccessKey配置 
                  <i class="fa fa-pencil-square-o change-icon"
                      nzTitle="修改"
                      (click)="changeAccessKey()"
                      nzPlacement="bottom"
                      nz-tooltip></i>
                </p>
                <div class="info-details-container">
                    <span class="info-details-item" *ngFor="let item of basicInforAccessKey; index as i">
                        <p>{{ item.title }}</p>
                        <p class="info-text {{getClass(item)}}">
                          <i class="fa fa-warning" *ngIf="item.value == '不可用'"></i>
                          {{ item.value }}
                          <span *ngIf="item.value == '不可用'">（请更换可用的AccessKey）</span>
                        </p>
                    </span>
                </div>
            </section>
            <p class="warning-text">*初始用户名：admin，初始密码：Harbor12345</p>
          </div>
          <!--节点管理-->
          <div class="content-body-item" *ngIf="activeContentIndex === 1">
            <!--内容头部-->
            <div class="header">
              <p>节点详情</p>
            </div>
            <!--内容-->
            <nz-table #nodeList
                      [nzBordered]=true
                      [nzItemRender]="nodePageTemplate"
                      [nzFrontPagination]="false"
                      [nzTotal]="nodePage.total"
                      [nzPageIndex]="nodePage.current"
                      [nzPageSize]="nodePage.size"
                      (nzPageIndexChange)="nodePageChange($event)"
                      [nzData]="nodeListData">
              <thead>
              <tr>
                <th>节点</th>
                <th>节点类型</th>
                <th>主机</th>
                <th>部署状态</th>
                <th>电源状态</th>
                <th>配置</th>
                <th>容量</th>
                <th>私有网络</th>
                <th>IP</th>
                <th>创建时间</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let item of nodeList.data">
                <td>{{ item.name }}</td>
                <td>
                  <span>{{getNodeType(item.harborStatus)}}</span>
                </td>
                <td>{{ item.hostName }}</td>
                <td>
                  <span class="dot {{ getStatusClass(item.deployStatus) }}">{{ getStatusText(item.deployStatus, 'deploy') }}</span>
                </td>
                <td>
                  <span class="dot {{ getStatusClass(item.powerStatus) }}"
                      *ngIf="item.deployStatus !== 'INIT' && item.deployStatus !== 'DELETING'">
                      {{ getStatusText(item.powerStatus, 'power') }}
                  </span>
                  <span class="dot {{ getStatusClass(item.deployStatus) }}"
                      *ngIf="item.deployStatus === 'INIT' || item.deployStatus === 'DELETING'">
                      {{ getStatusText(item.deployStatus, 'deploy') }}
                  </span>
                </td>
                <td>{{ item.cpuNum}} 核 {{ item.memoryGb }} G</td>
                <td>{{ item.diskGb }} G</td>
                <td>{{ item.subNetName}}</td>
                <td>{{ item.vpcIp ? item.vpcIp : '无'}}</td>
                <td>{{ getdate(item.createTm)}}</td>
              </tr>
              </tbody>
            </nz-table>
            <ng-template #nodePageTemplate let-type let-page="page">
              <a *ngIf="type === 'pre'">« 上一页</a>
              <a *ngIf="type === 'next'">下一页 »</a>
              <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
          </div>
        </div>
  </div>
</div>
<!-- 创建告警规则 -->
<nz-modal [(nzVisible)]="changeAccessKeyWindow" nzTitle="修改AccessKey"
    (nzOnCancel)="changeAccessKeyWindow = false"
    (nzOnOk)="submitChangeAccessKey()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">AccessKey：</span>
        <nz-select style="width: 300px"
            nzPlaceHolder="请选择"
            [(ngModel)]="accessKeyValue">
            <nz-option *ngFor="let item of accessKeyOption"
                        nzCustomContent
                        [nzLabel]="item.access_key"
                        [nzValue]="item.access_key + '-' + item.secret_key">
                <span nz-tooltip [nzTitle]="titleTemplate"
                                nzPlacement="bottom"
                                nz-tooltip>
                  {{item.access_key}}
                </span>
                <ng-template #titleTemplate>
                  <p>{{'AccessKey：' + item.access_key}}</p>
                  <p>{{'SecretKey：' + item.secret_key}}</p>
                </ng-template>
            </nz-option>
        </nz-select>
    </div>
    </ng-container>
</nz-modal>
<!--修改基本信息弹出层-->
<nz-modal [(nzVisible)]="showChangeBasicWindow" nzTitle="修改集群描述" 
    [nzOkLoading]="isLoading"
    [nzCancelLoading]="isLoading"
    (nzOnCancel)="showChangeBasicWindow = false"
    (nzOnOk)="submitChangeBasic()">
    <ng-container *nzModalContent>
    <div class="select-container">
        <span class="select-tips">集群描述：</span>
        <textarea style="width: 300px; height: 120px"
            nz-input rows="2"
            maxlength="200"
            [(ngModel)]="des"
            placeholder="请输入实例描述(长度200字符以内)">
        </textarea>
    </div>
    </ng-container>
</nz-modal>
<!--加载动画-->
<div class="ant-modal-mask ng-star-inserted" *ngIf="showSpain" ng-reflect-klass="ant-modal-mask">
  <div class="spin-container">
    <ng-template #indicatorTemplate><i nz-icon nzType="sync" [nzSpin]="showSpain"></i> </ng-template>
    <nz-spin nzSimple
             [nzIndicator]="indicatorTemplate"
             nzTip="加载中，请稍后...">
    </nz-spin>
  </div>
</div>
