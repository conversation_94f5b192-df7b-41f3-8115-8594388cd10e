<nz-modal [(nzVisible)]="isVisible"
    nzTitle="配置详情"
    (nzAfterOpen)="vmDetailModalOpened()"
    [nzFooter]="null"
    nzClassName="vm-info-modal"
    (nzOnCancel)="handleCancel()">
    <ng-container *nzModalContent>
    <nz-skeleton
        [nzAvatar]="false"
        [nzActive]="true"
        [nzParagraph]="{rows: 9}"
        [nzLoading]="!vmLoaded"
        [nzTitle]="false">
        <table class="vm-info">
            <tbody>
                <tr>
                    <td width="35%" class="th">付费方式</td>
                    <td>{{ initData.paymentType[groupVm.paymentType] || '-'}}</td>
                </tr>
                <tr>
                    <td class="th">计费方式</td>
                    <td>{{ initData.chargeType[groupVm.chargeType] || '-'}}</td>
                </tr>
                <tr>
                    <td class="th">配置信息</td>
                    <td>{{ getConfigText(groupVm) }}</td>
                </tr>
                <tr>
                    <td class="th">镜像选择</td>
                    <td>{{ getImageName(groupVm.imageId) }}</td>
                </tr>
                <tr>
                    <td class="th">存储空间</td>
                    <td>{{ getTotalDiskCapacityText(groupVm.cloudDiskList) }}</td>
                </tr>
                <tr>
                    <td class="th">专有网络</td>
                    <td>{{ getNetworkName(groupVm.vpcId) }}</td>
                </tr>
                <tr *ngIf="groupVm.isElasticIp">
                    <td class="th">峰值带宽</td>
                    <td>{{ groupVm.elasticIpList[0] && groupVm.elasticIpList[0].bandwidth }}Mbps</td>
                </tr>
                <tr>
                    <td class="th">实例名称</td>
                    <td>{{ groupVm.name || '-'}}</td>
                </tr>
                <tr>
                    <td class="th">主机名称</td>
                    <td>{{ groupVm.hostname || '-'}}</td>
                </tr>
                <tr *ngIf="groupVm.scriptTempName">
                    <td class="th">用户脚本</td>
                    <td>{{ groupVm.scriptTempName || '-'}}</td>
                </tr>
                <tr>
                    <td class="th">配置费用</td>
                    <td><span class="hl">{{ calcServerCost(groupVm) }}</span>元/小时</td>
                </tr>
                <tr *ngIf="groupVm.isElasticIp">
                    <td class="th">流量费用</td>
                    <td><span class="hl">{{ calcNetworkCost(groupVm) }}</span>元/GB</td>
                </tr>
            </tbody>
        </table>
    </nz-skeleton>
    </ng-container>
</nz-modal>