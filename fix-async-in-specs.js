const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// Function to find all .component.spec.ts files recursively
function findSpecFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findSpecFiles(filePath, fileList);
    } else if (file.endsWith('.component.spec.ts') || file === 'app.component.spec.ts') {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to replace async with waitForAsync in a file
async function updateFile(filePath) {
  try {
    let content = await readFile(filePath, 'utf8');
    
    // Replace import statement
    const importReplaced = content.replace(
      /import\s*{([^}]*)async([^}]*)}(\s*)from\s*['"]@angular\/core\/testing['"]/g,
      'import {$1waitForAsync$2}$3from \'@angular/core/testing\''
    );
    
    // Replace function calls
    const functionReplaced = importReplaced.replace(
      /beforeEach\(\s*async\s*\(\s*\(\)\s*=>/g,
      'beforeEach(waitForAsync(() =>'
    );
    
    // Only write to file if changes were made
    if (content !== functionReplaced) {
      await writeFile(filePath, functionReplaced, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
    return false;
  }
}

// Main function
async function main() {
  const rootDir = path.resolve(__dirname, 'src');
  const specFiles = findSpecFiles(rootDir);
  
  console.log(`Found ${specFiles.length} spec files to process`);
  
  let updatedCount = 0;
  
  for (const file of specFiles) {
    const updated = await updateFile(file);
    if (updated) {
      updatedCount++;
      console.log(`Updated: ${file}`);
    }
  }
  
  console.log(`Completed! Updated ${updatedCount} files out of ${specFiles.length}`);
}

main().catch(console.error);
