<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><span>IPSec VPN</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">IPSec VPN</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <div class="pull-right">
                    <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;" (click)="showStatisticsModal()">
                        <i nz-icon nzType="search"></i>
                        信息统计
                    </button>
                </div>
               <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                    [routerLink]="'../ipsec-vpn-config'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建IPSec VPN
                     </a>
                </div>
                <div class="pull-right" *ngIf="isAdmin === 'false'">
                    <a nz-button nzType="primary"
                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                    [routerLink]="isArchiveUser === 'true'? null : '../ipsec-vpn-config-quota'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                        创建IPSec VPN
                     </a>
                </div>
            </div>
            <nz-table #vpnList style="overflow-x: auto"
            [nzItemRender]="renderItemTemplate"
            [nzLoading]="isLoading"
            [nzLoadingDelay]="300"
            [nzFrontPagination]="false"
            [nzTotal]="pager.total"
            [nzPageIndex]="pager.page"
            [nzPageSize]="pager.pageSize"
            (nzPageIndexChange)="pageChanged($event)"
            (nzQueryParams)="onParamsChange($event)"
            [nzData]="vpnListData">
            <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th
                          *ngIf="col.width"
                          nz-resizable
                          nzBounds="window"
                          nzPreview
                          [nzWidth]="col.width"
                          [nzMaxWidth]="300"
                          [nzMinWidth]="60"
                          [nzShowSort]="col.showSort"
                          [nzSortFn]="col.sortFlag"
                          [nzSortOrder]="col.allowSort"
                          [nzColumnKey]="col.ColumnKey"
                          (nzResizeEnd)="onResize($event, col.title)"
                        >
                          {{ col.title }}
                          <nz-resize-handle nzDirection="right">
                            <div class="resize-trigger"></div>
                          </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width" style="min-width: 120px;">
                          {{ col.title }}
                        </th>
                      </ng-container>
                    <!-- <th width="15%">站点名称</th>
                    <th width="15%">本地端点</th>
                    <th width="15%">本地子网</th>
                    <th width="15%">对等端点</th>
                    <th width="15%">对等子网</th>
                    <th width="">已启用站点</th>
                    <th width="15%">操作</th> -->
                </tr>
            </thead>
            <tbody>
                <tr
                    *ngFor="let item of vpnList.data; trackBy: trackById">
                    <td>{{ item.name }}</td>
                    <td>{{ item.localID }}</td>
                    <td>{{ item.localIP }}</td>
                    <td>{{ item.localSubNetwork }}</td>
                    <td>{{ item.otherID }}</td>
                    <td>{{ item.otherIP }}</td>
                    <td>{{ item.otherSubNetwork }}</td>
                    <td>{{ item.cansecretVisible ?item.secretKey : "***"}}
                        <i class="suffix" [title]="item.cansecretVisible ? '隐藏密码' : '查看密码'" nz-icon
                        [nzType]="item.cansecretVisible ? 'eye-invisible' : 'eye'"
                        (click)="item.cansecretVisible = !item.cansecretVisible" style="margin-bottom: 5px;"></i></td>
                    <td>
                        <div class="on-table-actions"
                            [hidden]="busyStatus[item.id]">
                            <div class="on-table-action-item"
                             (click)="showModal(item);">
                                <i nzTitle="隧道配置"
                                nzPlacement="bottom"
                                nz-tooltip nz-icon nzType="cluster"
                                nzTheme="outline" class="icon">
                                </i>
                            </div>
                             <div class="on-table-action-item"
                                (click)="showEditModal(item)">
                                <i nzTitle="编辑"
                                    nzPlacement="bottom"
                                    nz-tooltip
                                    class="icon fa fa-edit"></i>
                            </div>
                            <div class="on-table-action-item"
                                nz-popconfirm
                                nzPlacement="top"
                                [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                [nzTitle]="isArchiveUser === 'true'? null : '确定要删除该IPSecVPN吗？'"
                                (nzOnConfirm)="isArchiveUser === 'true'? null : deletevpn(item);">
                                <i nzTitle="删除"
                                    nzPlacement="bottom"
                                    nz-tooltip
                                    class="icon fa fa-trash-o">
                                </i>
                            </div>
                        </div>
                        <div class="on-table-actions"
                            [hidden]="!busyStatus[item.id]">
                            <div
                                class="action-loading-placeholder">
                                <i class="icon" nz-icon
                                    [nzType]="'loading'"></i>
                                {{ getBusyText(item) }}
                            </div>
                        </div>
                        <!-- 隧道配置对话框 -->
                        <nz-modal [(nzVisible)]="isVisible" nzTitle="安全配置" [nzOkText]="null" nzCancelText="返回" (nzOnCancel)="handleCancel()">
                            <form class="config-content md network-form">
                                <div *ngIf="!item.nsxtSupport">
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">IKE版本：</span>
                                                <span class="label-text">{{item.ikeVersion}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">摘要算法：</span>
                                                <span class="label-text">{{item.digest}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">加密算法：</span>
                                                <span class="label-text">{{item.encryption}}</span>
                                                </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">身份验证：</span>
                                                <span class="label-text">PSK</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">Hellman组：</span>
                                                <span class="label-text">{{item.hellmanGroup}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="item.nsxtSupport">
                                    <h5>IKE 配置文件</h5>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">版本：</span>
                                                <span class="label-text">{{item.ikeConfigurationVersion}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">摘要：</span>
                                                <span class="label-text">{{item.ikeConfigurationDigest ? config.IkeDigestAlgorithmType[item.ikeConfigurationDigest] : ''}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">加密：</span>
                                                <span class="label-text">{{item.ikeConfigurationEncryption ? config.IkeEncryptionAlgorithmType[item.ikeConfigurationEncryption] : ''}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">Diffie-Hellman 组：</span>
                                                <span class="label-text">{{item.ikeConfigurationDhGroup ? config.DhGroupType[item.ikeConfigurationDhGroup] : ''}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <br/>
                                    <h5>通道配置</h5>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">启用完全向前保密：</span>
                                                <span class="label-text">
                                                    <i class="fa fa-check" aria-hidden="true" *ngIf="item.perfectForwardSecrecyEnabled" style="color: #55a532"></i>
                                                    <i class="fa fa-remove" aria-hidden="true" *ngIf="!item.perfectForwardSecrecyEnabled"></i>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">碎片整理策略：</span>
                                                <span class="label-text">{{item.dfPolicy}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">加密：</span>
                                                <span class="label-text">{{item.tunnelConfigurationEncryption ? config.TunnelEncryptionAlgorithmType[item.tunnelConfigurationEncryption] : ''}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">摘要：</span>
                                                <span class="label-text">{{item.tunnelConfigurationDigest ? config.TunnelDigestAlgorithmType[item.tunnelConfigurationDigest] : ''}}</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <div class="field-item">
                                            <label>
                                                <span class="label-text" style="width:150px">Diffie-Hellman 组：</span>
                                                <span class="label-text">{{item.tunnelConfigurationDhGroup ? config.DhGroupType[item.tunnelConfigurationDhGroup] : ''}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </nz-modal>
                    </td>
                </tr>
                <tr [hidden]="vpnList.data.length || !isLoading"
                    class="loading-placeholder">
                    <td colspan="100%"></td>
                </tr>
            </tbody>
        </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [nzWidth]="1000" [(nzVisible)]="statisticsModalVisible"
          [nzMaskClosable]="false" [nzFooter]=null
          [nzTitle]="'信息统计'"
          [nzBodyStyle]="{padding: '8px'}"
          (nzOnCancel)="hideStatisticsModal()">
    <ng-container *nzModalContent>
    <div class="on-panel" style="height:740px;">
        <div class="on-panel-body" style="float: left;width: 20%;border: 2px;border-style: groove;height: 730px;margin-left: 20px;padding:0;">
            <ul class="instance-info" *ngFor="let item of statisticsInfo index as i" style="margin-bottom: 0;">
                <li class="statisticsA unSelected"><a style="margin-left: 20px;"  (click)="showYml(item,i)">{{item.siteId}}</a></li>
            </ul>
        </div>
        <div class="on-panel-body" style="float: left;border: 2px;border-style: groove;height: 730px;width: 70%;margin-left: 50px;">
            <div style="overflow-y: auto;height: 680px;"><pre>{{ymlText}}</pre></div>
        </div>
    </div>
    </ng-container>
</nz-modal>



<app-ipsec-vpn-update-config
        [editVpn]="editVpn"
        [isVisible]="vpnVisible"
        (submit)="reload()"
        (close)="vpnVisible = false">
</app-ipsec-vpn-update-config>
