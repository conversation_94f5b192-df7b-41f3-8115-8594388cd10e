<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">容器服务</a></li>-->
<!--        <li><span>集群管理</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入模型名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
        </div>
        <div class="on-panel-body">
            <div class="container">
                <div class="model-card inactive">
                    <div class="model-header">
<!--                        <div class="qwen-icon">Q</div>-->
                        <div class="model-title">Qwen2.5-7B-Instruct</div>
                        <div class="model-date">2024-09-19</div>
                    </div>


                    <div class="model-tags">
                        <span class="tag chat">chat</span>
                        <span class="tag instruct">instruct</span>
                        <span class="tag code">code</span>
                        <span class="tag en">en</span>
                        <span class="tag zh">zh</span>
                    </div>

                    <div class="model-description">
                        Qwen2.5是Qwen大型语言模型的最新系列。对于Qwen2.5，发布了一些基础语言模型和指令微调语言模型，参数从5亿到70亿不等。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;通义千问</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>

                    </div>
                </div>

                <div class="model-card inactive">
                    <div class="model-header">
<!--                        <div class="glm-icon">G</div>-->
                        <div class="model-title">glm-4-9b-chat</div>
                        <div class="model-date">2024-10-05</div>
                    </div>

                    <div class="model-tags">
                        <span class="tag chat">chat</span>
                        <span class="tag text-generation">text</span>
                        <span class="tag code">code</span>
                        <span class="tag other">other</span>
                        <span class="tag en">en</span>
                        <span class="tag zh">zh</span>
                    </div>

                    <div class="model-description">
                        GLM-4-9b 是智谱 AI 推出的新一代预训练模型 GLM-4 系列中的开源版本。在语义、数学、推理、代码和知识等多方面的数据集测评中，GLM-4-9b 及其人类偏好对齐的版本 GLM-4-9b-Chat 均表现出较高的性能。除了能进行多轮对话，GLM-4-9b-Chat 还具备网页检索、代码执行、自定义工具。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;智谱 AI</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>
                    </div>

                </div>

                <div class="model-card active" (click)="modalVisible = true">
                    <div class="model-header">
<!--                        <div class="deepseek-icon">D</div>-->
                        <div class="model-title">deepseek-coder-6.7b-base</div>
                        <div class="model-date">2024-01-01</div>
                    </div>

                    <div class="model-tags">
                        <span class="tag code">code</span>
                        <span class="tag other">other</span>
                    </div>

                    <div class="model-description">
                        DeepSeek Coder由一系列代码语言模型组成，每个模型都是在27令牌上从头开始训练的，由87%的代码和13%的中英文自然语言组成，提供各种大小的代码模型，从7b到33b版本。每个模型都通过使用16k的窗口大小和额外的预训练在项目级代码语料库上进行预训练，以支持项目级代码的完成和修改。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;deepseek-ai</div>
                        <a class="experience-btn">体&nbsp;&nbsp;&nbsp;验</a>
                    </div>
                </div>

                <div class="model-card inactive">
                    <div class="model-header">
<!--                        <div class="qwen-icon">Q</div>-->
                        <div class="model-title">Qwen2.5-0.5B-Instruct</div>
                        <div class="model-date">2024-09-19</div>
                    </div>

                    <div class="model-tags">
                        <span class="tag chat">chat</span>
                        <span class="tag instruct">instruct</span>
                        <span class="tag code">code</span>
                        <span class="tag en">en</span>
                        <span class="tag zh">zh</span>
                    </div>

                    <div class="model-description">
                        Qwen2.5是Qwen大型语言模型的最新系列。对于Qwen2.5，发布了一些基础语言模型和指令微调语言模型，参数从5亿到70亿不等。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;通义千问</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>
                    </div>
                </div>

                <div class="model-card inactive">
                    <div class="model-header">
<!--                        <div class="baichuan-icon">B</div>-->
                        <div class="model-title">Baichuan2-7B-Chat</div>
                        <div class="model-date">2024-02-26</div>
                    </div>

                    <div class="model-tags">
                        <span class="tag text-generation">text</span>
                        <span class="tag chat">chat</span>
                        <span class="tag other">other</span>
                        <span class="tag en">en</span>
                        <span class="tag zh">zh</span>
                    </div>

                    <div class="model-description">
                        Baichuan 2 是百川智能推出的新一代开源大语言模型，采用 2.6 万亿 Tokens 的高质量语料训练。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;百川智能</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>
                    </div>
                </div>

                <div class="model-card inactive">
                    <div class="model-header">
                        <!--                        <div class="baichuan-icon">B</div>-->
                        <div class="model-title">Baichuan2-13B-Chat</div>
                        <div class="model-date">2024-10-05</div>
                    </div>

                    <div class="model-tags">
                        <span class="tag text-generation">text</span>
                        <span class="tag chat">chat</span>
                        <span class="tag other">other</span>
                        <span class="tag en">en</span>
                        <span class="tag zh">zh</span>
                    </div>

                    <div class="model-description">
                        更高参数规模的对话模型，提供更复杂的语言理解和生成能力，适合需要深度交互和精准响应的场景。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;百川智能</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>
                    </div>
                </div>

                <div class="model-card inactive">
                    <div class="model-header">
<!--                        <div class="stable-icon">S</div>-->
                        <div class="model-title">stable-diffusion-3.5-medium</div>
                        <div class="model-date">2024-11-01</div>
                    </div>

                    <div class="model-tags">
                        <span class="tag text2image">text2image</span>
                        <span class="tag code">code</span>
                        <span class="tag other">other</span>
                        <span class="tag en">en</span>
                        <span class="tag image2image">image2image</span>
                        <span class="tag instruct">instruct</span>
                    </div>

                    <div class="model-description">
                        Stable Diffusion 3.5 Medium 是一个改进的多模态扩散模型（MMDT-x）文本到图像模型，该模型在图像质量、字体消散、复杂提示理解和资源效率方面都有所提升。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;StabilityAI</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>
                    </div>
                </div>

                <div class="model-card inactive">
                    <div class="model-header">
<!--                        <div class="stable-icon">S</div>-->
                        <div class="model-title">stable-diffusion-3.5-large</div>
                        <div class="model-date">2024-11-01</div>
                    </div>


                    <div class="model-tags">
                        <span class="tag text2image">text2image</span>
                        <span class="tag code">code</span>
                        <span class="tag other">other</span>
                        <span class="tag en">en</span>
                        <span class="tag image2image">image2image</span>
                        <span class="tag instruct">instruct</span>
                    </div>

                    <div class="model-description">
                        Stable Diffusion 3.5 Large 是一个基于多模态扩散模型（MMDT-x）的文本到图像生成模型，其在图像质量、消散、复杂提示理解和资源效率方面都有所改进。
                    </div>

                    <div class="model-author">
                        <div class="author-name">&#64;StabilityAI</div>
                        <a href="#" class="experience-btn gray">敬请期待</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="modalVisible" (nzOnCancel)="handleCancel()" [nzFooter]="null"
          (nzOnOk)="download()" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading" [nzWidth]="1000" style="padding: 0">
    <ng-container *nzModalContent>
        <div class="experience-card">
            <div class="experience-space">
                <div class="experience-header">
                    <h2 class="experience-title">deepseek-coder-6.7b-base</h2>
                    <div class="experience-actions">
                    </div>
                </div>

                <div class="experience-model-info">
                    <span>模型：deepseek-ai/deepseek-coder-6.7b-base</span>
                    <a style="cursor: not-allowed"><i class="copy-icon">🔗</i></a>
                </div>

                <p class="experience-description">Deepseek Coder由一系列代码语言模型组成，每个模型都是在2T令牌上从头开始训练的，由87%的代码和13%的中英文自然语言组成。提供各种大小的代码模型，从1B到33B版本。每个模型都通过使用16K的窗口大小和额外的填空任务在项目级代码语料库上进行预训练，以支持项目级代码的完成和填充。在编码能力方面，Deepseek Coder在多种编程语言和各种基准测试的开源代码模型中实现了最先进的性能。</p>
            </div>

            <div class="experience-space">
                <h3 class="welcome-message">欢迎体验 deepseek-coder-6.7b-base</h3>

<!--                <div class="chat-area">-->
<!--                    &lt;!&ndash; 聊天内容将在这里显示 &ndash;&gt;-->
<!--                </div>-->

                <div class="input-section">
                    <textarea class="message-input" [(ngModel)]="text" placeholder="请输入您的问题" rows="3"></textarea>
                </div>

                <div class="input-footer">
                    <div>&nbsp;</div>
                    <span class="word-counter">{{text.length}} / 1000 <button class="send-button" style="margin-left: 15px">➤</button></span>
<!--                    <button class="send-button">➤</button>-->
                </div>
                <p class="disclaimer-text" style="margin:0">AI生成内容仅供参考，不代表平台立场。</p>
            </div>
        </div>
    </ng-container>
</nz-modal>