<div class="table-content">
<!--  <ol class="on-breadcrumb">-->
<!--    <li><a routerLink="../">容器服务</a></li>-->
<!--    <li><a routerLink="../harbor_pub">Harbor(公有)</a></li>-->
<!--    <li><span>详情</span></li>-->
<!--  </ol>-->
  <div class="on-panel">
    <div class="details-container">
      <!--内容菜单-->
      <div class="details-menu">
        <nz-tabset [nzTabPosition]="'top'" [nzType]="'card'" (nzSelectChange)="selectMenu($event)"
          [nzSelectedIndex]="activeContentIndex">
          <nz-tab *ngFor="let tab of contentMenuOptions" [nzTitle]="tab.title">
          </nz-tab>
        </nz-tabset>
      </div>
      <div class="content-body-item" *ngIf="activeContentIndex === 0">
        <nz-collapse [nzBordered]="false">
          <nz-collapse-panel #p *ngFor="let panel of panels; index as i" [nzHeader]="panelHeaderTemplate"
            [nzActive]="panel.active" [ngStyle]="panelStyle" [nzShowArrow]=false
            (nzActiveChange)="selectPanel($event, i)">
            <ng-template #panelHeaderTemplate>
              <div class="panel-container">
                <img [src]="panel.src" alt="镜像图标" class="panel-images">
                <!-- 右侧 -->
                <div class="panel-mid">
                  <div class="panel-mid-item">
                    <span class="title">{{ panel.repoName }}</span>
                  </div>
                  <div class="panel-mid-item">
                    <span class="content">来源：{{ panel.serverAddr || '-' }}</span>
                  </div>
                  <!-- <div class="panel-mid-item">
                    <span class="content">{{ panel.des || '无' }}</span>
                  </div> -->
                </div>
                <div class="panel-right">
                  <div class="panel-right-top" (click)="deleteRepo($event, panel)">
                    <i class="icon fa fa-trash-o"></i>
                    <span>删除</span>
                  </div>
                  <div class="panel-right-bottom">
                    <p>
                      <i nz-icon nzType="cloud-download" nzTheme="outline"></i>
                      <label>下载量：{{panel.pullCount}}</label>
                    </p>
                    <p><i class="fa fa-cloud-upload"></i>最近更新：{{getdate(panel.createTm)}}</p>
                  </div>
                </div>
              </div>
            </ng-template>
            <!-- 展开内容 -->
            <div class="expand-container">
              <nz-tabset (nzSelectChange)="chooseExpandContent($event, i)" [nzSelectedIndex]="panel.tabsetIndex">
                <nz-tab nzTitle="镜像描述">
                  <div class="expand-des" [innerHTML]="panel.innerHTML"></div>
                  <nz-empty *ngIf="!panel.innerHTML" [nzNotFoundContent]="contentTpl"></nz-empty>
                  <ng-template #contentTpl>
                    <span>当前仓库没有描述内容</span>
                  </ng-template>
                </nz-tab>
                <nz-tab nzTitle="Tags">
                  <nz-list [nzDataSource]="panel.tags" [nzRenderItem]="item" [nzItemLayout]="'horizontal'"
                    [nzLoading]="tagLoading">
                    <ng-template #item let-item>
                      <nz-list-item>
                        <nz-list-item-meta [nzTitle]="nzTitle" nzAvatar="../../../../../assets/images/docker2.png"
                          [nzDescription]="nzDes">
                          <ng-template #nzDes>
                            <span class="tag-des">更新大小：{{item.sizeMB}} MB</span>
                            <span class="tag-des">
                              <label nzTooltipPlacement="bottom"
                                [nzTooltipTitle]="item.downloadStr + '/' + item.repoName + ':' + item.tag"
                                nz-tooltip>下载地址：{{item.downloadStr + '/' + item.repoName + ':' + item.tag || '-'}}
                              </label>
                              <i nz-icon nzType="copy" data-clipboard-text="" class="copyTxt" *ngIf="item.downloadStr"
                                nzTooltipPlacement="bottom" nzTooltipTitle="复制" nz-tooltip (click)="copyTxt(item)"
                                nzTheme="outline"></i>
                            </span>
                            <div class="tag-right">
                              <i class="icon fa fa-trash-o" nzTooltipPlacement="bottom" nzTooltipTitle="删除" nz-tooltip
                                (click)="deleteTag(item)"></i>
                              <p>{{getdate(item.updateTm)}}</p>
                            </div>
                          </ng-template>
                          <ng-template #nzTitle>
                            <a href="javascript:void(0)">{{ item.tag }}</a>
                          </ng-template>
                        </nz-list-item-meta>
                      </nz-list-item>
                    </ng-template>
                  </nz-list>
                </nz-tab>
              </nz-tabset>
            </div>
          </nz-collapse-panel>
        </nz-collapse>
        <div class="page-box">
          <nz-pagination [nzPageIndex]="page.current" [nzTotal]="page.total" (nzPageIndexChange)="pageChange($event)"
            [nzItemRender]="renderItemTemplate"></nz-pagination>
          <ng-template #renderItemTemplate let-type let-page="page">
            <a *ngIf="type === 'pre'">« 上一页</a>
            <a *ngIf="type === 'next'">下一页 »</a>
            <a *ngIf="type === 'page'">{{ page }}</a>
          </ng-template>
        </div>
      </div>
      <div class="content-body-item" *ngIf="activeContentIndex === 1">
        <!--内容头部-->
        <div class="header">
          <p>项目用户管理</p>
          <div class="pull-right">
            <button nz-button nzType="primary" (click)="showAddOrUpdateUser()">
              <i nz-icon nzType="plus" nzTheme="outline"></i>
              新增用户
            </button>
          </div>
        </div>
        <nz-table #tableList [nzPageSize]=99999 [nzShowPagination]=false [nzData]="userData">
          <thead>
            <tr>
              <th width="20%">用户名</th>
              <th>角色</th>
              <th>更多操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of tableList.data; index as i">
              <td width="20%">
                <span style="word-break: break-all;">{{data.name || "-"}}</span>
              </td>
              <td>{{roleType(data.roleType) || "-"}}</td>
              <td>
                <div class="on-table-actions">
                  <div class="on-table-action-item" [ngClass]="{'disabled': !canOperate(data)}"
                    (click)="showAddOrUpdateUser(data)">
                    <i nzTitle="修改" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-wrench"></i>
                  </div>
                  <div class="on-table-action-item" nz-popconfirm nzTooltipContent="top" [nzCondition]="!canOperate(data)"
                    [ngClass]="{'disabled': !canOperate(data)}" nzTitle="确定要删除该用户吗？" (nzOnConfirm)="deleteUser(data);">
                    <i nzTitle="删除" nzTooltipContent="bottom" nz-tooltip class="icon fa fa-trash-o"></i>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </div>
  </div>
</div>
<!--创建命名空间弹出层-->
<nz-modal [(nzVisible)]="showOperateUserWindow" nzTitle="{{ createOrUpdateUserTitle }}"
  (nzOnCancel)="showOperateUserWindow = false" [nzOkLoading]="isLoading" [nzCancelLoading]="isLoading"
  (nzOnOk)="submitAddOrUpdateUser()">
  <ng-container *nzModalContent>
  <div class="select-container">
    <span class="select-tips">用户名：</span>
    <input nz-input style="width: 300px;" maxlength="63" [disabled]="!ifCreateUser || isLoading"
      [(ngModel)]="userName" />
  </div>
  <div class="select-container">
    <span class="select-tips">权限：</span>
    <nz-select style="width: 300px;" [disabled]="isLoading" [(ngModel)]="selectRoleType">
      <nz-option *ngFor="let option of selectRoleTypeList" [nzValue]="option.value" [nzLabel]="option.text">
      </nz-option>
    </nz-select>
  </div>
  </ng-container>
</nz-modal>