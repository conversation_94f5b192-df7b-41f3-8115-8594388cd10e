import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CostRoutingModule } from './cost-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';
import { UsageRecordComponent} from 'src/app/components/console/cost/record/usage-record.component';
import { CostCenterComponent} from 'src/app/components/console/cost/cost-center/cost-center.component';
import {DashboardTemplateModule} from "src/app/modules/console/dashboard-template/dashboard-template.module";

@NgModule({
    declarations: [UsageRecordComponent, CostCenterComponent],
    imports: [CommonModule, SharedModule, CostRoutingModule, DashboardTemplateModule]
})
export class CostModule {
    constructor(moduleRef: NgModuleRef<CostModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
