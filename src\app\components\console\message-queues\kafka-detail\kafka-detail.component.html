<div class="table-content">
    <ol class="on-breadcrumb">
        <li><a routerLink="../">消息队列</a></li>
        <li><a routerLink="../">Kafka</a></li>
        <li><span>详情</span></li>
    </ol>
    <div class="on-panel">
        <div class="details-container">
            <!--顶部菜单-->
            <div class="details-header">
                <div class="title-container">
                    <p>{{ basicInfor[0].value }}</p>
                    <span>{{ basicInfor[5].value }}</span>
                    <span class="title-des-separate">{{ basicInfor[6].value }}</span>
                </div>
                <div>
                    <span class="operate-btn" (click)="deleteKafka()">
                        <i class="fa fa-trash-o"></i><span>删除</span>
                    </span>
                    <!-- <span class="operate-btn" (click)="deploy()">
                        <i class="fa fa-upload"></i><span>部署</span>
                    </span> -->
                </div>
            </div>
            <!--内容菜单-->
            <div class="details-menu">
                <nz-tabset [nzTabPosition]="'top'" [nzType]="'card'"
                            (nzSelectChange)="selectMenu($event)"
                            [nzSelectedIndex]="activeContentIndex">
                        <nz-tab *ngFor="let tab of contentMenuOptions"[nzTitle]="tab.title">
                        </nz-tab>
                </nz-tabset>
            </div>
            <!--基本信息-->
            <div class="content-body-item" *ngIf="activeContentIndex === 0">
                <!--基本信息-->
                <section class="info-container">
                    <p>基本信息</p>
                    <div class="info-details-container">
                        <div class="info-details-item" *ngFor="let item of basicInfor; index as i">
                            <p>{{ item.title }}</p>
                            <p class="info-text">{{ item.value }}</p>
                        </div>
                    </div>
                </section>
                <!--其他信息-->
                <section class="info-container">
                    <p>其他信息</p>
                    <div class="info-details-container">
                        <span class="info-details-item" *ngFor="let item of basicInfor2; index as i">
                            <p>{{ item.title }}</p>
                            <p class="info-text">{{ item.value }}</p>
                        </span>
                    </div>
                </section>
            </div>
            <!--topic管理-->
            <div class="content-body-item" *ngIf="activeContentIndex === 1">
                <!--内容头部-->
                <div class="header">
                    <p>Topic管理</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="showAddTopic()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            创建Topic
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #topicList
                            [nzBordered]=true
                            [nzItemRender]="topicPageTemplate"
                            [nzFrontPagination]="false"
                            [nzTotal]="topicPage.total"
                            [nzPageIndex]="topicPage.current"
                            [nzPageSize]="topicPage.size"
                            (nzPageIndexChange)="topicPageChange($event)"
                            [nzData]="topicListData">
                    <thead>
                    <tr>
                        <th>Topic</th>
                        <th>实例</th>
                        <th>服务状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of topicList.data">
                        <td>{{ item.name }}</td>
                        <td>{{ instanceName }}</td>
                        <td>
                            <span class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status) }}</span>
                        </td>
                        <td>{{ getdate(item.createTm) }}</td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="showListWindow('topic', item.instanceId, item.name);">
                                    <i nzTitle="查看分区状态"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-search"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该Topic吗？"
                                    (nzOnConfirm)="deleteTopic(item);">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #topicPageTemplate let-type let-page="page">
                    <a *ngIf="type === 'prev'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <!--消费者组管理-->
            <div class="content-body-item" *ngIf="activeContentIndex === 2">
                <!--内容头部-->
                <div class="header">
                    <p>消费者组管理</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="showAddConsumerGroup()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            创建消费者组
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #consumerGroupList
                            [nzBordered]=true
                            [nzItemRender]="consumerGroupTemplate"
                            [nzFrontPagination]="false"
                            [nzTotal]="consumerGroupPage.total"
                            [nzPageIndex]="consumerGroupPage.current"
                            [nzPageSize]="consumerGroupPage.size"
                            (nzPageIndexChange)="consumerGroupChange($event)"
                            [nzData]="consumerGroupListData">
                    <thead>
                    <tr>
                        <th width="20%">消费者组名称</th>
                        <th width="20%">实例</th>
                        <th>服务状态</th>
                        <th width="20%">备注</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of consumerGroupList.data">
                        <td>{{ item.groupId ? item.groupId : '-' }}</td>
                        <td>{{ instanceName ? instanceName : '-' }}</td>
                        <td>
                            <span class="dot {{ getStatusClass(item.status) }}">{{ getStatusText(item.status) }}</span>                            
                        </td>
                        <td>{{ item.des ? item.des : '-'  }}</td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="showListWindow('consumer', item.instanceId, item.groupId);">
                                    <i nzTitle="查看消息堆积"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-search"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该消费者组吗？"
                                    (nzOnConfirm)="deleteConsumerGroup(item);">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>

                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #consumerGroupTemplate let-type let-page="page">
                    <a *ngIf="type === 'prev'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
            <!--监控管理-->
            <div class="content-body-item" *ngIf="activeContentIndex === 3">
                <!--内容头部-->
                <div class="header">
                    <p>监控</p>
                    <div class="pull-right">
                        <button nz-button nzType="primary" (click)="showAddMonitor()">
                            <i nz-icon nzType="plus"
                                nzTheme="outline"></i>
                            新增监控
                        </button>
                    </div>
                </div>
                <!--内容-->
                <nz-table #monitorList
                        [nzBordered]=true
                        [nzItemRender]="renderItemTemplate"
                        [nzFrontPagination]="false"
                        [nzTotal]="monitorPage.total"
                        [nzPageIndex]="monitorPage.current"
                        [nzPageSize]="monitorPage.size"
                        (nzPageIndexChange)="pageChange($event)"
                        [nzData]="monitorListData">
                    <thead>
                    <tr>
                        <th width="20%">监控名称</th>
                        <th>监控类型</th>
                        <th width="20%">监控对象</th>
                        <th>阈值</th>
                        <th>通知方式</th>
                        <th>告警邮箱地址</th>
                        <!-- <th>告警电话号码</th> -->
                        <th>状态</th>
                        <th>修改时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let item of monitorList.data">
                        <td>{{ item.monitorName ? item.monitorName : '-' }}</td>
                        <td>{{ item.monitorType }}</td>
                        <td>{{ item.extraInfo ? item.extraInfo : '-' }}</td>
                        <td>{{ item.rule }}  {{ item.threshold }}</td>
                        <td>{{ item.noticeType }}</td>
                        <td>{{ item.email ? item.email : '-' }}</td>
                        <!-- <td>{{ item.telNum ? item.telNum : '-' }}</td> -->
                        <td>
                            <span class="dot {{ getStatusClass(item.status) }}">{{ getWarningStatusText(item.status) }}</span>                            
                        </td>
                        <td>{{ getdate(item.updateTm) }}</td>
                        <!--更多操作-->
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    *ngIf="item.status === 'silence'"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要恢复该告警监控吗？"
                                    (nzOnConfirm)="silence('active', item)">
                                    <i nzTitle="恢复告警"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-bell-o"></i>
                                </div>
                                <div class="on-table-action-item"
                                    *ngIf="item.status === 'active'"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要静默该告警监控吗？"
                                    (nzOnConfirm)="silence('silence', item)">
                                    <i nzTitle="静默告警"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-bell-slash-o"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    nzTitle="确定要删除该告警监控吗？"
                                    (nzOnConfirm)="deleteMonitor(item)">
                                    <i nzTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o"></i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </nz-table>
                <ng-template #renderItemTemplate let-type let-page="page">
                    <a *ngIf="type === 'prev'">« 上一页</a>
                    <a *ngIf="type === 'next'">下一页 »</a>
                    <a *ngIf="type === 'page'">{{ page }}</a>
                </ng-template>
            </div>
        </div>
    </div>
</div>
<!--创建topic弹出层-->
<nz-modal [(nzVisible)]="showCreateTopicWindow"
        nzTitle="创建Topic"
        [nzOkLoading]="isLoading"
        [nzCancelLoading]="isLoading"
        (nzOnCancel)="showCreateTopicWindow = false"
        (nzOnOk)="submitAddTopic()">
    <ng-container *nzModalContent>
    <div class="popup-window-body-middle">
        <div class="radio-container input-container">
            <div class="form-label-tips-container">
                <p [ngStyle]="{'color':topicNameCheck ? '#999': '#E01B2F'} ">
                    1、可包含数字、大小写字母、“.”、“_”或“-”，长度不超过64个字符
                </p>
                <p>2、一旦创建后不能再修改Topic名称</p>
            </div>
            <div class="select-container">
                <span class="select-tips">Topic名称：</span>
                <nz-input-group [nzSuffix]="suffixTemplate" style="width: 300px" >
                    <input type="text" nz-input
                           [(ngModel)]="topicNameValue"
                           (blur)="checkTopicName()"
                           (keyup.enter)="submitAddTopic()"
                           placeholder="Topic名称" />
                </nz-input-group>
                <ng-template #suffixTemplate><i
                        nz-icon
                        nz-tooltip
                        class="ant-input-clear-icon"
                        nzTheme="fill"
                        nzType="close-circle"
                        *ngIf="topicNameValue"
                        (click)="topicNameValue = null"></i>
                </ng-template>
            </div>
        </div>
    </div>
    </ng-container>
</nz-modal>
<!--创建消费者组弹出层-->
<nz-modal [(nzVisible)]="showCreateConsumerGroupWindow"
        nzTitle="创建消费者组"
        [nzOkLoading]="isLoading"
        [nzCancelLoading]="isLoading"
        (nzOnCancel)="showCreateConsumerGroupWindow = false"
        (nzOnOk)="submitAddConsumerGroup()">
    <ng-container *nzModalContent>
    <div class="popup-window-body-middle">
        <div class="radio-container input-container">
            <div class="form-label-tips-container">
                <p [ngStyle]="{'color':consumerGroupNameCheck ? '#999': '#E01B2F'} ">
                    1、可包含数字、大小写字母、“.”、“_”或“-”，长度不超过64个字符
                </p>
                <p>2、一旦创建后不能再修改消费者组名称</p>
                <p [ngStyle]="{'color':consumerGroupDesCheck ? '#999': '#E01B2F'} ">
                    3、备注只能包含中文、大小写英文、数字、下划线，长度不超过64个字符</p>
            </div>
            <div class="select-container">
                <span class="select-tips">消费者组名称：</span>
                <nz-input-group [nzSuffix]="suffixTemplate" style="width: 300px" >
                    <input type="text" nz-input
                           [(ngModel)]="consumerGroupNameValue"
                           (blur)="checkConsumerGroupName()"
                           placeholder="消费者组名称" />
                </nz-input-group>
                <ng-template #suffixTemplate><i
                        nz-icon
                        nz-tooltip
                        class="ant-input-clear-icon"
                        nzTheme="fill"
                        nzType="close-circle"
                        *ngIf="consumerGroupNameValue"
                        (click)="consumerGroupNameValue = null"></i>
                </ng-template>
            </div>
            <div class="select-container">
                <span class="select-tips">备注：</span>
                <nz-input-group [nzSuffix]="suffixTemplate" style="width: 300px" >
                    <input type="text" nz-input
                           [(ngModel)]="consumerGroupDesValue"
                           (input)="checkConsumerGroupDes()"
                           placeholder="备注(非必填)" />
                </nz-input-group>
                <ng-template #suffixTemplate><i
                        nz-icon
                        nz-tooltip
                        class="ant-input-clear-icon"
                        nzTheme="fill"
                        nzType="close-circle"
                        *ngIf="consumerGroupDesValue"
                        (click)="consumerGroupDesValue = null"></i>
                </ng-template>
            </div>
        </div>
    </div>
    </ng-container>
</nz-modal>
<!--分区状态框-->
<nz-modal [(nzVisible)]="showTopicTypeWindow"
          nzTitle="分区状态"
          [nzCancelText]=null
          (nzOnCancel)="showTopicTypeWindow = false"
          nzOkText="关闭"
          (nzOnOk)="showTopicTypeWindow = false">
    <ng-container *nzModalContent>
    <div class="total-container">
        <span class="total-title">当前服务器上消息总量：</span>
        <span class="total-txt">{{ topicTotalMsgCount }}</span>
    </div>
    <div class="popup-window-body-large">
        <nz-table #topicPartitionOffsetList
                  [nzBordered]=true
                  [nzPageSize]=99999
                  [nzShowPagination]=false
                  [nzData]="topicPartitionOffset">
            <thead>
            <tr>
                <th>分区ID</th>
                <th>最小位点</th>
                <th>最大位点</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let item of topicPartitionOffsetList.data">
                <td>{{ item.partitionId }}</td>
                <td>{{ item.beginningOffset }}</td>
                <td>{{ item.endOffset }}</td>
            </tr>
            </tbody>
        </nz-table>
    </div>
    </ng-container>
</nz-modal>
<!--消息堆积-->
<nz-modal [(nzVisible)]="showConsumerGroupWindow"
          nzTitle="消息堆积"
          [nzCancelText]=null
          (nzOnCancel)="showConsumerGroupWindow = false"
          nzOkText="关闭"
          (nzOnOk)="showConsumerGroupWindow = false">
    <ng-container *nzModalContent>
    <div class="total-container">
        <span class="total-title">当前服务器上消息堆积量：</span>
        <span class="total-txt">{{ consumerTotalMsgCount }}</span>
    </div>
    <div class="popup-window-body-large">
        <nz-table #consumerGroupPartitionOffsetList
                  [nzBordered]=true
                  [nzPageSize]=99999
                  [nzShowPagination]=false
                  [nzData]="consumerGroupPartitionOffset">
            <thead>
            <tr>
                <th>Topic</th>
                <th>分区ID</th>
                <th>消息量</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let item of consumerGroupPartitionOffsetList.data">
                <td>{{ item.topic }}</td>
                <td>
                    <div class="list-item-row">0</div>
                    <div class="list-item-row">1</div>
                    <div class="list-item-row">2</div>
                </td>
                <td>
                    <div class="list-item-row">{{ item.partitionInfos[0] }}</div>
                    <div class="list-item-row">{{ item.partitionInfos[1] }}</div>
                    <div class="list-item-row">{{ item.partitionInfos[2] }}</div>
                </td>
            </tr>
            </tbody>
        </nz-table>
    </div>
    </ng-container>
</nz-modal>
<!--创建告警规则弹出层-->
<nz-modal [(nzVisible)]="showCreateMonitorWindow" nzTitle="新增告警监控"
        [nzOkLoading]="isLoading"
        [nzCancelLoading]="isLoading"
        (nzOnCancel)="showCreateMonitorWindow = false"
        (nzOnOk)="submitAddWarningRule()">
    <ng-container *nzModalContent>
    <!--名称-->
    <div class="select-container">
        <span class="select-tips">告警监控名称：</span>
        <nz-input-group [nzSuffix]="suffixTemplate" style="width: 300px" >
            <input type="text" nz-input 
                [(ngModel)]="warningRuleName" 
                (blur)="checkWarningRuleName()" 
                placeholder="告警监控名称" />
        </nz-input-group>
        <ng-template #suffixTemplate><i
            nz-icon
            nz-tooltip
            class="ant-input-clear-icon"
            nzTheme="fill"
            nzType="close-circle"
            *ngIf="warningRuleName"
            (click)="warningRuleName = null"></i>
        </ng-template>
        <p class="warning-text"
            *ngIf="!warningRuleNameCheck"
            style="margin-left: 120px;margin-bottom: 10px;">*名称在64字符以内！</p>
    </div>
    <!--监控类型-->
    <div class="select-container">
        <span class="select-tips">告警监控类型：</span>
        <nz-select style="width: 300px;" [(ngModel)]="selectWarningRuleType" (ngModelChange)="changeWarningRuleType()">
            <nz-option *ngFor="let option of selectWarningRuleTypeList" [nzValue]="option.id" [nzLabel]="option.name">
            </nz-option>
        </nz-select>
    </div>
    <!--监控对象-->
    <div class="select-container" *ngIf="!warningRuleTypeDisk">
        <span class="select-tips">告警监控对象：</span>
        <nz-select style="width: 300px;" [(ngModel)]="selectWarningRuleObject">
            <nz-option *ngFor="let option of selectWarningRuleObjectList" [nzValue]="option.id" [nzLabel]="option.name">
            </nz-option>
        </nz-select>
    </div>
    <!--阈值-->
    <div class="select-container">
        <span class="select-tips">阈值：</span>
        <nz-select style="width: 80px;" [(ngModel)]="selectLimitType">
            <nz-option nzValue="<" nzLabel="<"></nz-option>
            <nz-option nzValue=">" nzLabel=">"></nz-option>
        </nz-select>
        <!--阈值单位 百分比(磁盘)-->
        <nz-input-number style="width: 80px; margin-left: 10px" *ngIf="warningRuleTypeDisk" [(ngModel)]="limitValueDisk"
                         [nzMin]="1" [nzMax]="100" [nzStep]="1" [nzFormatter]="formatterPercent" [nzParser]="parserPercent">
        </nz-input-number>
        <!--阈值单位 条(topic、用户组)-->
        <nz-input-number style="width: 80px; margin-left: 10px" *ngIf="!warningRuleTypeDisk" [(ngModel)]="limitValue"
                         [nzMin]="1" [nzStep]="1">
        </nz-input-number>
        <nz-select style="width: 80px; margin-left: 10px" [(ngModel)]="limitValueUnitNotDisk" *ngIf="!warningRuleTypeDisk">
            <nz-option nzValue="条" nzLabel="条"></nz-option>
        </nz-select>
    </div>
    <!--通知方式-->
    <div class="select-container">
        <span class="select-tips select-checkbox">告警通知方式：</span>
        <nz-checkbox-wrapper style="width: 300px;" (nzOnChange)="selectWarningRuleWay($event)">
            <div nz-row>
                <div nz-col><label nz-checkbox nzValue="console" [ngModel]="true" [nzDisabled]="true">控制台消息通知</label></div>
                <br/>
                <div nz-col>
                    <label nz-checkbox nzValue="mail" [ngModel]="showInputEmail">邮箱</label>
                    <input nz-input placeholder="邮箱地址"
                           style="width: 240px"
                           *ngIf="showInputEmail"
                           [(ngModel)]="warningRuleEmail"
                           (blur)="checkEmail()"/>
                    <p class="warning-tips-text" *ngIf="!warningRuleEmailCheck">**邮箱地址不规范**</p>
                </div>
                <br/>
                <!-- <div nz-col>
                    <label nz-checkbox nzValue="message" [ngModel]="showInputTel">短信</label>
                    <input nz-input placeholder="手机号码"
                           style="width: 240px"
                           *ngIf="showInputTel"
                           [(ngModel)]="warningRuleTel"
                           (blur)="checkTel()"/>
                    <p class="warning-tips-text" *ngIf="!warningRuleTelCheck">**手机号码不规范**</p>
                </div> -->
            </div>
        </nz-checkbox-wrapper>
    </div>
    </ng-container>
</nz-modal>
