<nz-modal [(nzVisible)]="isVisible"
    nzTitle="选择用户"
    [nzOkLoading]="okLoading"
    [nzMaskClosable]="false"
    [nzBodyStyle]="{padding: '24px 8px 8px 8px'}"
    (nzAfterOpen)="strategyModalOpened()"
    [nzWidth]="520"
    (nzOnCancel)="handleCancel()" (nzOnOk)="handleOk()">
    <ng-container *nzModalContent>
    <section class="field-section">
        <div class="order_search">
            <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                <input type="text" [(ngModel)]="searchUserName" nz-input placeholder="输入关键字搜索" (keydown.enter)="searchUser()" (ngModelChange)="searchUser()" />
              </nz-input-group>
              <ng-template #suffixIconButton>
                <button nz-button nzType="primary" (click)="searchUser()" nzSearch><i nz-icon nzType="search"></i></button>
              </ng-template>
        </div>
        <div class="field-group">
            <nz-collapse nzAccordion class="order-accordion" [nzBordered]="false">
                <nz-collapse-panel *ngFor="let item of pageUserList;let key = index"  [nzHeader]="item.v" [nzActive]="item.active">
                        <nz-radio-group *ngFor="let item1 of item.list" [(ngModel)]="user" (ngModelChange)="checkedUserChange(this.pageIndex)">
                            <label nz-radio nzValue="{{item1.id+':'+item1.name+':'+ key}}">{{item1.name}}</label>
                        </nz-radio-group>
                </nz-collapse-panel> 
            </nz-collapse>
          
        </div>
        
        <div class="text-center" style="padding: 20px 0 15px 0;">
            <nz-pagination [(nzPageIndex)]="pageIndex" [nzTotal]="total" [nzPageSize]="8"
            (nzPageIndexChange)="PageIndexChange($event)" [nzShowQuickJumper]="false"></nz-pagination>
        </div>
    </section>
    </ng-container>
</nz-modal>