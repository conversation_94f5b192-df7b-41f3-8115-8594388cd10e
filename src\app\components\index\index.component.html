
<div class="flex-col bg-f5f7fc">
    <div class="header headerScroll">
        <div class="flex-row items-center">
            <div class="logo">
                <img class="item" src="./assets/images/logo/{{logo}}.png" style="height:38px;"/>
            </div>
<!--                        <div class="flex-row navList" v-for="(item,index) in navItems" :key="index">-->
<!--                            <router-link v-if="item.target == '_self'" :to="item.path" :target="item.target">{{item.name}}</router-link>-->
<!--                            <a v-else :href="item.path" :target="item.target">{{item.name}}</a>-->
<!--                        </div>-->
        </div>
        <div class="flex-row items-center">
            <div *ngIf="isLogin" class="login-info">
                <a (click)="toConsole()"><i class="icon-Connection-trends iconfont icon" style=""></i>控制台</a>
                <div class="dropdown" role="presentation">
                    <span nz-dropdown style="display: flex"
                          nzOverlayClassName="nav-dropdown"
                          nzPlacement="bottomRight"
                          class="dropdown-toggle"
                          [nzDropdownMenu]="menu">
                        <nz-badge >
                            <a><img src="./assets/images/account.png"></a>
                        </nz-badge>
                    </span>
                    <nz-dropdown-menu
                        #menu="nzDropdownMenu">
                        <ul nz-menu>
                            <li class="menu-item" nz-menu-item>
<!--                                {{username }}-->
                                <a href="javascript:void(0);" (click)="logout()">退出账号</a>
                            </li>
                        </ul>
                    </nz-dropdown-menu>
                </div>
            </div>
            <a *ngIf="!isLogin" href="javascript:void(0)" class="ml-40 primary" style="display: flex;align-content: center" (click)="toConsole()">登&nbsp;&nbsp;&nbsp;录</a>
        </div>
    </div>
    <div class="container-full bg-1">
        <div class="container h-full flex-col items-start justify-start">
            <span class="fs-36 fc-main mt-160 -ml-25"><span class="fc-primary">「 </span>智领云时代，AI赋能企业数字化转型<span class="fc-primary"> 」</span></span>
            <span class="fs-24 fc-sub mt-30">—— 您的智能混合云管理平台！” </span>
            <a href="javascript:void(0)" (click)="toConsole()" class="primary shadow text-center w-140 h-40 mt-120">开始使用</a>
        </div>
    </div>
    <div class="container-full bg-2">
        <div class="container flex-col items-center">
            <span class="mt-80 fs-36">产品<span class="fc-primary">介绍</span></span>
            <span class="fc-sub fs-24 mt-20">面向公有云和私有云等多种应用场景，打造的一款专业的、稳定的、高效的、易用的AI智能混合云管理平台</span>
        </div>
        <div class="container flex-row justify-between items-center mt-80">
            <div class="itd-2-box flex-col items-start justify-start">
                <span class="fs-20 fc-main line-h-30">统一管理</span>
                <span class="fs-20 fc-primary line-h-30">提升效率</span>
                <span class="fs-14 fc-sub line-h-20 text-left mt-40">多云资源一站式管理与调度，避免跨平台管理的复杂性</span>
                <img src="./assets/images/index/tygl.png" class="size-180"/>
            </div>
            <div class="itd-2-box flex-col items-start justify-start">
                <span class="fs-20 fc-main line-h-30">智能化运维</span>
                <span class="fs-20 fc-primary line-h-30">降低成本</span>
                <span class="fs-14 fc-sub line-h-20 text-left mt-40">通过AI技术实现自动化运维与优化，减少运维成本和人工干预</span>
                <img src="./assets/images/index/znhyw.png" class="size-180"/>
            </div>
            <div class="itd-2-box flex-col items-start justify-start">
                <span class="fs-20 fc-main line-h-30">精细化计量</span>
                <span class="fs-20 fc-primary line-h-30">优化开支</span>
                <span class="fs-14 fc-sub line-h-20 text-left mt-40">精准的计量与费用分析，让企业清晰了解每一笔云支出的去向</span>
                <img src="./assets/images/index/jxhjl.png" class="size-180"/>
            </div>
            <div class="itd-2-box flex-col items-start justify-start">
                <span class="fs-20 fc-main line-h-30">个性化支持</span>
                <span class="fs-20 fc-primary line-h-30">助力创新</span>
                <span class="fs-14 fc-sub line-h-20 text-left mt-40">AI大模型的支持使得企业能够更加灵活地进行数据分析与业务创新</span>
                <img src="./assets/images/index/gxhzc.png" class="size-180"/>
            </div>
            <div class="itd-2-box flex-col items-start justify-start">
                <span class="fs-20 fc-main line-h-30">知识库支持</span>
                <span class="fs-20 fc-primary line-h-30">提升技能</span>
                <span class="fs-14 fc-sub line-h-20 text-left mt-40">内置的智能知识库帮助企业快速培养专业的云计算与AI运维团队</span>
                <img src="./assets/images/index/zskzc.png" class="size-180"/>
            </div>
        </div>
    </div>
    <div class="container-full bg-3 p-r flex-col">
        <div class="container flex-col items-center">
            <span class="mt-80 fs-36">产品<span class="fc-primary">功能</span></span>
        </div>
        <div class="container flex-row mt-80 items-center justify-between">
            <div class="fn-1-box fn-1-bg flex-col items-start">
                <span class="fs-18-fat-0 fc-white">智能运维</span>
                <span class="fc-white line-h-20 mt-20 text-left">通过对系统运行数据的实时监测、分析和预测，以及自动化的故障处理和优化建议，帮助企业降低运维成本，提高系统可靠性和稳定性，提升业务的连续性和竞争力</span>
            </div>
            <div class="fn-1-box fn-2-bg flex-col items-start">
                <span class="fs-18-fat-0 fc-white">资源管理</span>
                <span class="fc-white line-h-20 mt-20 text-left">对企业混合云的资源进行全面、高效、精准的管理和维护，确保云服务的稳定运行，提升企业数字化业务的可靠性和竞争力</span>
            </div>
            <div class="fn-1-box fn-3-bg flex-col items-start">
                <span class="fs-18-fat-0 fc-white">云知识库</span>
                <span class="fc-white line-h-20 mt-20 text-left">成为企业IT部门和相关业务团队的强大智力支持，帮助他们快速解决问题、提升工作效率、推动企业的数字化转型和创新发展</span>
            </div>
        </div>
        <div class="container flex-col">
            <div class="fn-box w-full h-55 flex-row items-center justify-center mt-50">
                <span class="fs-16-fat fc-primary">AI智能混合云管平台</span>
            </div>
            <div class="flex-row justify-between items-start">
                <div class="flex-col items-center justify-center">
                    <div class="fn-box w-328 h-198 flex-col mt-38">
                        <div class="fn-3-title"><span>欢迎页面</span></div>
                        <span class="text-box w-full flex-row items-center justify-center mt-20">启动面板</span>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">系统概览</span>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">Chat BI</span>
                    </div>
                    <div class="fn-box w-328 h-400 flex-col mt-38">
                        <div class="fn-3-title"><span>审计日志</span></div>
                        <span class="text-box w-full flex-row items-center justify-center mt-20">工单维护</span>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">账号映射</span>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">多云审计</span>
                        <div class="text-box h-150 w-full flex-row-wrap justify-between p-12 mt-8">
                            <span class="text-box-p flex-row items-center justify-center w-128 {{i < 2 ? '' : 'mt-12'}}" *ngFor="let text of fnText;let i = index">{{text}}</span>
                        </div>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">运维报告</span>
                    </div>
                </div>
                <div class="flex-col items-center justify-center">
                    <div class="fn-box w-572 h-150 flex-col mt-38">
                        <div class="fn-3-title"><span>资源管理</span></div>
                        <div class="flex-row-wrap justify-between mt-20">
                            <span class="text-box w-260 flex-row items-center justify-center">虚拟机申请</span>
                            <span class="text-box w-260 flex-row items-center justify-center">资源回收</span>
                            <span class="text-box w-260 flex-row items-center justify-center mt-8">资源变更</span>
                            <span class="text-box w-260 flex-row items-center justify-center mt-8">资源架构</span>
                        </div>
                    </div>
                    <div class="fn-box w-572 h-217 flex-col mt-38">
                        <div class="fn-3-title"><span>AI智能运维服务</span></div>
                        <div class="flex-row-wrap justify-between mt-20">
                            <span class="text-box w-260 flex-row items-center justify-center">7*24智能运维客服</span>
                            <span class="text-box w-260 flex-row items-center justify-center">AI多模态故障识别</span>
                            <span class="text-box w-260 flex-row items-center justify-center mt-8">AI智能资源规划</span>
                            <span class="text-box w-260 flex-row items-center justify-center mt-8">AI智能异常检测</span>
                            <span class="text-box w-260 flex-row items-center justify-center mt-8">AI智能预警告警</span>
                            <span class="text-box w-260 flex-row items-center justify-center mt-8">AI运维智能体</span>
                        </div>
                    </div>
                    <div class="flex-row w-572 justify-between mt-38">
                        <div class="flex-col fn-box w-178 h-194">
                            <div class="fn-3-title"><span>云管AI大模型</span></div>
                            <span class="text-box w-full flex-row items-center justify-center mt-20">AI私有化模型</span>
                            <span class="text-box w-full flex-row items-center justify-center mt-8">AI通用模型</span>
                            <span class="text-box w-full flex-row items-center justify-center mt-8">AI模型管理</span>
                        </div>
                        <div class="flex-col fn-box w-178 h-194">
                            <div class="fn-3-title"><span>AI大模型运维</span></div>
                            <span class="text-box w-full flex-row items-center justify-center mt-20">AI运维智能体</span>
                            <span class="text-box w-full flex-row items-center justify-center mt-8">AI工作流</span>
                            <span class="text-box w-full flex-row items-center justify-center mt-8">API服务</span>
                        </div>
                        <div class="flex-col fn-box w-178 h-194">
                            <div class="fn-3-title"><span>AI企业级IT</span></div>
                            <span class="text-box w-full flex-row items-center justify-center mt-20">RAG</span>
                            <span class="text-box w-full flex-row items-center justify-center mt-8">数据标注</span>
                            <span class="text-box w-full flex-row items-center justify-center mt-8">知识训练</span>
                        </div>
                    </div>
                </div>
                <div class="flex-col items-center justify-center">
                    <div class="fn-box w-260 h-150 flex-col mt-38">
                        <div class="fn-3-title"><span>计量管理</span></div>
                        <span class="text-box w-full flex-row items-center justify-center mt-20">容量管理</span>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">容量报告</span>
                    </div>
                    <div class="fn-box w-260 h-450 flex-col mt-38">
                        <div class="fn-3-title"><span>监控管理</span></div>
                        <span class="text-box w-full flex-row items-center justify-center mt-20">KPI指标管理</span>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">多云监控</span>
                        <div class="text-box h-244 w-full flex-row-wrap justify-between p-12 mt-8">
                            <span class="text-box-p flex-row items-center justify-center w-full {{i == 0 ? '' : 'mt-8'}}" *ngFor="let text of fnText2;let i = index">{{text}}</span>
                        </div>
                        <span class="text-box w-full flex-row items-center justify-center mt-8">统一监控采集</span>
                    </div>
                </div>
            </div>
            <div class="fn-box flex-col mt-38 w-full h-150 justify-center items-center">
                <div class="fn-3-title w-500"><span>系统管理</span></div>
                <div class="flex-row-wrap justify-between items-start">
<!--                                <span class="text-box w-284 flex-row items-center justify-center" :class="i<4 ?'mt-20':'mt-8'" v-for="(text,i) in fnText3" :key="i">{{text}}</span>-->
                    <span class="text-box w-260 flex-row items-center justify-center {{i < 4 ? 'mt-20' : 'mt-8'}}" *ngFor="let text of fnText3;let i = index">{{text}}</span>
                </div>
            </div>
            <div class="flex-row w-full h-55 justify-center items-center fn-3-x mt-20">
                <span>资源适配器</span>
            </div>
        </div>
    </div>
    <div class="value-box">
        <div class="container value-bg mt-85 flex-col p-50">
            <div class="btn-white">
                <span class="fc-primary fs-16">价值定位</span>
            </div>
            <div class="flex-row mt-60 items-start justify-start">
                <div class="flex-col w-440 ustify-start items-start">
                    <div *ngFor="let text of valueText;let i = index">
                        <span class="flex-row items-center mt-18" *ngIf="i < 4"><img src="./assets/images/index/icon-right.png" class="icon-24" /><span class="ml-20 fc-white fs-20">{{text}}</span></span>
                    </div>

                </div>
                <div class="flex-col w-440 justify-start items-start" v-if="list.length > 4">
                    <div *ngFor="let text of valueText;let i = index">
                        <span class="flex-row items-center mt-18" *ngIf="i >= 4"><img src="./assets/images/index/icon-right.png" class="icon-24" /><span class="ml-20 fc-white fs-20">{{text}}</span></span>
                    </div>

                </div>
            </div>
        </div>
    </div>

<!--    <div class="w-full flex-col">-->
<!--        <div class="flex-row flex-center mt-60">-->
<!--            <div class="line"></div>-->
<!--            <span class="fc-main fs-28 ml-50 mr-50">渠道合作企业</span>-->
<!--            <div class="line"></div>-->
<!--        </div>-->
<!--        <div class="slider-container mt-40" #sliderContainer>-->
<!--            <div class="slider mt-15"  #slider>-->
<!--                &lt;!&ndash; 图片列表 &ndash;&gt;-->
<!--                <img *ngFor="let image of images;let i = index" src="./assets/images/logo/{{image.src}}" (load)="onImageLoad()"/>-->
<!--                &lt;!&ndash; 为了实现无缝滚动，复制图片列表 &ndash;&gt;-->
<!--                <img *ngFor="let image of images;let i = index" src="./assets/images/logo/{{image.src}}" (load)="onImageLoad()"/>-->
<!--            </div>-->
<!--            <div class="leftMark"></div>-->
<!--            <div class="rightMark"></div>-->
<!--        </div>-->
<!--    </div>-->

    <div class="regbox-full">
        <div class="container flex-col items-center justify-center">
            <span class="fs-28 fc-white mt-60"></span>
            <a href="javascript:void(0)" (click)="toConsole()" class="reg-btn-footer items-center justify-center fs-18 mt-30">开始使用</a>
        </div>
    </div>

<!--    <div class="container flex-col mt-60 bt-60 foot-content">-->
<!--        <div class="container flex-row justify-between items-start">-->
<!--            <div class="flex-col footer-item foot-left">-->
<!--                <div><img src="./assets/images/logo/{{logo}}.png" style="height:58px"/></div>-->
<!--                <div class="foot-text">零门槛无需编码，极速拥有专属 DeepSeek 模型</div>-->
<!--            </div>-->
<!--            <div class="flex-row justify-between foot-right">-->
<!--                <div class="flex-col text-left footer-item">-->
<!--                    <span class="font-main">产品</span>-->
<!--                    <router-link to='/AITraining' class="mt-30 document">新一代人工智能客服</router-link>-->
<!--                </div>-->
<!--                <div class="flex-col text-left footer-item">-->
<!--                    <span class="font-main">资源</span>-->
<!--                    <a href="https://help.redbearai.com/" target="_blank" class="mt-30 pl-0">文档</a>-->
<!--                    &lt;!&ndash; <span class="mt-20">博客</span> &ndash;&gt;-->
<!--                    <a href="https://help.redbearai.com/aiprb-introduce/contant-us.html" target="_blank" class="mt-20 pl-0">服务支持</a>-->
<!--                    &lt;!&ndash; <span class="mt-20">产品路线图</span> &ndash;&gt;-->
<!--                </div>-->
<!--                <div class="flex-col text-left footer-item">-->
<!--                    <span class="font-main">公司</span>-->
<!--                    <router-link to="/document?name=service" class="mt-30 document">服务条款</router-link>-->
<!--                    <router-link to="/document?name=privacy" class="mt-20 document">隐私政策</router-link>-->
<!--                    <a href="http://help.redbearai.com/aiprb-introduce/about-us.html" target="_blank" class="mt-20 document">联系我们</a>-->
<!--                </div>-->
<!--            </div>-->

<!--        </div>-->
<!--        <div class="container copyright mt-40 flex-row items-center justify-between">-->
<!--            <span><a href="" target="_blank" class="pl-0 fs-14"></a></span>-->
<!--            <div class="footer-nav">-->
<!--                <a href="https://help.redbearai.com/" target="_blank">帮助</a>-->
<!--                <a href="https://help.redbearai.com/aiprb-introduce/contant-us.html" target="_blank">举报</a>-->
<!--                <a href="https://help.redbearai.com/aiprb-introduce/contant-us.html" target="_blank">用户反馈</a>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
</div>
