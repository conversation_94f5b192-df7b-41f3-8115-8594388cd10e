<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">云服务器</a></li>-->
<!--        <li><span>自定义镜像</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline"
                  (ngSubmit)="search()">
                <nz-input-group nzSearch
                                [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="根据名称搜索" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                        nzType="search"></i></button>
                </ng-template>
            </form>
            <!--
            <div class="right-button-group">
            <div class="pull-right">
                <a nz-button nzType="primary"
                [ngClass]="{'disabled': isArchiveUser === 'true'}"
                [routerLink]="isArchiveUser === 'true'? null : '../instance-config'">
                    <i nz-icon nzType="plus"
                        nzTheme="outline"></i>
                    添加到私有镜像
                </a>
            </div></div> -->
            <!-- <p style="color:#999999">指云主机可以选择的运行环境模板，一般包括操作系统和预装的软件，为客户提供包含基本操作系统的镜像，用户可以在此基础上整合具体的软件环境，创建自己的自定义镜像。</p> -->
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">自定义镜像</span>
            </div>
            <nz-table #monitors style="overflow-x: auto"
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                (nzQueryParams)="onParamsChange($event)"
                [nzData]="mirrorList">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            nzBreakWord="true"
                            [nzWidth]="col.width"
                            [nzMaxWidth]="500"
                            [nzMinWidth]="80"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                            >
                            {{ col.title }}
                            <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                            </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                            {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of monitors.data">
                        <td>{{ item.name }}</td>
                        <td>{{ item.mirrorSize || '-'}}</td>
                        <td>{{ item.diskSize || '-'}}</td>
                        <td>{{ item.cloneFrom || '-'}}</td>
                        <td>
                            <div class="on-table-actions" *ngIf="permission('delete')"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzTooltipContent="top"
                                    [nzPopconfirmTitle]="isArchiveUser === 'true' ? null : '确定要删除该自定义镜像规则吗？'"
                                    [nzCondition]="!canDeleteMrRule(item)"
                                    [ngClass]="{'disabled': isArchiveUser === 'true'}"
                                    (nzOnConfirm)="isArchiveUser === 'true' ? null : deleteMonitor(item);">
                                    <i nzTooltipTitle="删除"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                                <!-- <div class="on-table-action-item"
                                [ngClass]="{'disabled': isArchiveUser === 'true'}">
                                <a class="on-table-action-item"
                                    [routerLink]="['../detail', item.id]">
                                    <i nzTooltipTitle="共享"
                                        nzTooltipContent="bottom"
                                        nz-tooltip
                                        nz-icon
                                        nzType="cluster"
                                        nzTheme="outline"
                                        class="icon"></i>
                                </a>
                                </div> -->
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="monitors.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
