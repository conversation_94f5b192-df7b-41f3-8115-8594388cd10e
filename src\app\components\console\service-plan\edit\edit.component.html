<nz-modal
    [(nzVisible)]="isVisible"
    [nzTitle]="isEdit ? '编辑服务计划' : '创建服务计划'"
    [nzOkText]="isEdit ? '更新' : '创建'"
    [nzCancelText]="'取消'"
    [nzOkLoading]="isSubmitting"
    (nzOnCancel)="handleCancel()"
    (nzOnOk)="submitForm()"
    [nzWidth]="600"
    [nzBodyStyle]="{padding: '0 24px'}">
    <ng-container *nzModalContent>
    <form [formGroup]="servicePlanForm" class="config-content md network-form dialog">
        <section class="field-section">
            <div class="field-title">
                <div></div>
                基本信息
            </div>
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <div class="label-text">名称</div>
                        <input nz-input type="text"
                            formControlName="name"
                            placeholder="请输入服务计划名称">
                    </label>
                    <div *ngIf="servicePlanForm.get('name').dirty && servicePlanForm.get('name').errors" class="form-hint error">
                        <div *ngIf="servicePlanForm.get('name').errors.required">
                            名称不能为空
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <div class="label-text">类型</div>
                        <nz-select formControlName="servicePlanType" nzPlaceHolder="请选择服务计划类型">
                            <nz-option nzValue="" nzLabel="请选择"></nz-option>
                            <nz-option *ngFor="let option of servicePlanTypeOptions" [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                        </nz-select>
                    </label>
                    <div *ngIf="servicePlanForm.get('servicePlanType').dirty && servicePlanForm.get('servicePlanType').errors" class="form-hint error">
                        <div *ngIf="servicePlanForm.get('servicePlanType').errors.required">
                            类型不能为空
                        </div>
                    </div>
                </div>
                <div class="field-item" *ngIf="isEdit">
                    <label>
                        <div class="label-text">服务编码</div>
                        <input nz-input type="text"
                            formControlName="servicePlanCode"
                            placeholder="系统自动生成"
                            disabled>
                    </label>
                </div>
<!--                <div class="field-item">-->
<!--                    <label>-->
<!--                        <div class="label-text">区域</div>-->
<!--                        <nz-select-->
<!--                            formControlName="regionsStr"-->
<!--                            nzMode="multiple"-->
<!--                            nzPlaceHolder="请选择区域"-->
<!--                            style="width: 100%;"-->
<!--                            [nzDropdownMatchSelectWidth]="false"-->
<!--                            [nzMaxTagCount]="3"-->
<!--                            nzMaxTagPlaceholder="{{'+ ${ sliceTags.length } 个区域'}}">-->
<!--                            <nz-option nzValue="region1" nzLabel="区域1"></nz-option>-->
<!--                            <nz-option nzValue="region2" nzLabel="区域2"></nz-option>-->
<!--                            <nz-option nzValue="region3" nzLabel="区域3"></nz-option>-->
<!--                        </nz-select>-->
<!--                    </label>-->
<!--                </div>-->
                <div class="field-item required">
                    <label>
                        <div class="label-text">价格</div>
                        <nz-input-number formControlName="price" [nzMin]="0" [nzStep]="1" [nzPrecision]="2" style="width: 20%"></nz-input-number>
                    </label>
                    <div *ngIf="servicePlanForm.get('price').dirty && servicePlanForm.get('price').errors" class="form-hint error">
                        <div *ngIf="servicePlanForm.get('price').errors.required">
                            价格不能为空
                        </div>
                        <div *ngIf="servicePlanForm.get('price').errors.min">
                            价格不能小于0
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <label>
                        <div class="label-text">生效日期 ~ 到期日期</div>
                        <nz-date-picker
                            formControlName="autoEffectiveDate"
                            nzFormat="yyyy-MM-dd HH:mm:ss"
                            [nzShowTime]="{ nzFormat: 'HH:mm:ss' }"
                            style="width: 47%">
                        </nz-date-picker>
                        &nbsp;&nbsp;~&nbsp;&nbsp;
                        <nz-date-picker
                            formControlName="autoExpiryDate"
                            nzFormat="yyyy-MM-dd HH:mm:ss"
                            [nzShowTime]="{ nzFormat: 'HH:mm:ss' }"
                            style="width: 47%">
                        </nz-date-picker>
                    </label>
                </div>
            </div>
        </section>

        <section class="field-section">
            <div class="field-title">
                <div></div>
                服务组件
            </div>
            <div class="service-plan-header">
                <div class="right-button">
                    <button nz-button nzType="primary" (click)="addServicePlanItem()">
                        <i nz-icon nzType="plus"></i>新增服务组件
                    </button>
                </div>
            </div>

            <div class="field-group" formArrayName="servicePlanItems">
                <div *ngFor="let item of servicePlanItems.controls; let i = index" [formGroupName]="i" class="service-plan-item">
                    <div class="service-plan-item-row">
                        <div class="field-item required name-item">
                            <label>
                                <div class="label-text">名称</div>
                                <input nz-input type="text"
                                    formControlName="name"
                                    placeholder="请输入组件名称">
                            </label>
                            <div *ngIf="item.get('name').dirty && item.get('name').errors" class="form-hint error">
                                <div *ngIf="item.get('name').errors.required">
                                    名称不能为空
                                </div>
                            </div>
                        </div>
                        <div class="field-item required type-item">
                            <label>
                                <div class="label-text">类型</div>
                                <nz-select formControlName="servicePlanItemType" nzPlaceHolder="请选择组件类型">
                                    <nz-option nzValue="" nzLabel="请选择"></nz-option>
                                    <nz-option *ngFor="let option of servicePlanItemTypeOptions" [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                </nz-select>
                            </label>
                            <div *ngIf="item.get('servicePlanItemType').dirty && item.get('servicePlanItemType').errors" class="form-hint error">
                                <div *ngIf="item.get('servicePlanItemType').errors.required">
                                    类型不能为空
                                </div>
                            </div>
                        </div>
                        <div class="field-item subtype-item">
                            <label>
                                <div class="label-text">子类型</div>
                                <nz-select
                                    formControlName="servicePlanItemSubType"
                                    nzPlaceHolder="请选择子类型"
                                    [nzDisabled]="item.get('servicePlanItemType').value !== 'DISK_GB'">
                                    <nz-option nzValue="" nzLabel="请选择"></nz-option>
                                    <nz-option *ngFor="let option of servicePlanItemSubTypeOptions" [nzValue]="option.value" [nzLabel]="option.label"></nz-option>
                                </nz-select>
                            </label>
                        </div>
                        <div class="field-item required amount-item">
                            <label>
                                <div class="label-text">数量</div>
                                <nz-input-number formControlName="amount" [nzMin]="1" [nzStep]="1" style="width: 100%"></nz-input-number>
                            </label>
                            <div *ngIf="item.get('amount').dirty && item.get('amount').errors" class="form-hint error">
                                <div *ngIf="item.get('amount').errors.required">
                                    数量不能为空
                                </div>
                                <div *ngIf="item.get('amount').errors.min">
                                    数量不能小于1
                                </div>
                            </div>
                        </div>
                        <div class="field-item action-item">
                            <div class="on-table-action-item">
                                <i nzTooltipTitle="删除"
                                   nzTooltipPlacement="bottom"
                                   nz-tooltip
                                   class="icon fa fa-trash-o"
                                   (click)="removeServicePlanItem(i)">
                                </i>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="servicePlanItems.length === 0" class="empty-items">
                    <span>暂无服务组件，请点击"新增服务组件"按钮添加</span>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>
