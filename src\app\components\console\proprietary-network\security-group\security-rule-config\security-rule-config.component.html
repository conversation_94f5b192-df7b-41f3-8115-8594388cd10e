<nz-modal [(nzVisible)]="isVisible" [nzMaskClosable]="false"
    [nzTitle]="isEdit ? '编辑规则' : '添加规则'"
    [nzOkText]="isEdit ? '保存' : '添加'"
    [nzOkLoading]="_isLoading"
    [nzWidth]="600"
    [nzBodyStyle]="{padding: '0 24px'}"
    (nzAfterOpen)="setInitData()"
    (nzOnCancel)="handleCancel()"
    (nzOnOk)="addRule()">
    <ng-container *nzModalContent>
    <form [formGroup]="rule" class="config-content md">
        <section class="field-section">
            <div class="field-group">
                <div class="field-item required">
                    <label>
                        <span
                            class="label-text">方向</span>
                        <nz-select
                            *ngIf="!isEditing"
                            formControlName="direction"
                            (ngModelChange)="directionChange($event)"
                            nzPlaceHolder="请选择方向">
                            <nz-option
                                *ngFor="let item of directionList"
                                [nzValue]="item.value"
                                [nzLabel]="item.label">
                            </nz-option>
                        </nz-select>
                        <span *ngIf="isEditing && rule.value.direction === 'in'">入方向</span>
                        <span *ngIf="isEditing && rule.value.direction === 'out'">出方向</span>
                    </label>
                </div>
                <div class="field-item required">
                    <label for="">
                        <span
                            class="label-text">协议/应用</span>
                        <nz-select
                            (ngModelChange)="protocolChange($event)"
                            formControlName="protocol"
                            nzPlaceHolder="请选择协议/应用">
                            <nz-option
                                *ngFor="let item of protocolList"
                                [nzValue]="item"
                                [nzLabel]="item">
                            </nz-option>
                        </nz-select>
                        
                    </label>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">名称</span>
                        <input nz-input type="text"
                            formControlName="description"
                            maxlength="50"
                            placeholder="请输入名称">
                    </label>
                    <div *ngIf="isInvalid(rule.get('description'))"
                        class="form-hint error">
                        <div
                            *ngIf="rule.get('description').hasError('required')">
                            名称不能为空
                        </div>
                        <div
                            *ngIf="rule.get('description').hasError('pattern')">
                            名称必须以字母开头，只能输入英文、数字、中划线
                        </div>
                        <div
                            *ngIf="rule.get('description').hasError('maxlength')">
                            名称长度不能超过{{ rule.get('name').errors.maxlength.requiredLength }}个字符
                        </div>
                    </div>
                </div>
                <div class="field-item" *ngIf="showProt">
                    <label>
                        <span class="label-text">端口范围</span>
                        <input nz-input type="text"
                            formControlName="sourcePortRange"
                            maxlength="50"
                            placeholder="1 ~ 65535，留空则允许任意端口">
                    </label>
                    <div *ngIf="isInvalid(rule.get('sourcePortRange'))"
                        class="form-hint error">
                        <div
                            *ngIf="rule.get('sourcePortRange').hasError('maxlength')">
                            端口字段长度不能超过{{ rule.get('sourcePortRange').errors.maxlength.requiredLength }}个字符
                        </div>
                        <div
                            *ngIf="rule.get('sourcePortRange').hasError('portError')">
                            端口不符合格式
                        </div>
                        <div
                            *ngIf="rule.get('sourcePortRange').hasError('portRangeError')">
                            端口超出范围：1 ~ 65535
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">源地址</span>
                        <span class="dib">
                            <nz-input-group nzCompact>
                                <nz-select
                                    style="width: 120px;"
                                    [(ngModel)]="sourceType"
                                    [ngModelOptions]="{standalone: true}"
                                    nzPlaceHolder="源地址类型">
                                    <nz-option
                                        *ngFor="let item of sourceTypeList"
                                        [nzValue]="item.value"
                                        [nzLabel]="item.label">
                                    </nz-option>
                                </nz-select>
                                <input type="text"
                                    *ngIf="sourceType === 'ip'"
                                    nz-input
                                    formControlName="sourceIp"
                                    placeholder="例： *************/24"
                                    style="width: 161px;" />
                            </nz-input-group>
                        </span>
                    </label>
                    <div *ngIf="isInvalid(rule.get('sourceIp'))"
                        class="form-hint error">
                        <div
                            *ngIf="rule.get('sourceIp').hasError('required')">
                            源地址不能为空
                        </div>
                        <div
                            *ngIf="rule.get('sourceIp').hasError('maxlength')">
                            源地址字段长度不能超过{{ rule.get('sourceIp').errors.maxlength.requiredLength }}个字符
                        </div>
                        <div
                            *ngIf="rule.get('sourceIp').hasError('noMaskError')">
                            请填写源地址IP后缀
                        </div>
                        <div
                            *ngIf="rule.get('sourceIp').hasError('ipError')">
                            源地址IP不符合格式
                        </div>
                        <div
                            *ngIf="rule.get('sourceIp').hasError('maskError')">
                            源地址IP后缀超出范围：0 ~ 32
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </form>
    </ng-container>
</nz-modal>
