<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">运维管理</a></li>-->
<!--        <li><a routerLink="../permission"><span>权限管理</span></a></li>-->
<!--        <li><span>默认权限</span></li>-->
<!--    </ol>-->

    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form [formGroup]="searchForm" nzLayout="inline" (ngSubmit)="search()" style="width: 100%;display: flex;justify-content: space-between">
                <div>
                    <input type="text" autocomplete="off" formControlName="keyword"
                           nz-input placeholder="请输入任务名称" style="width: 220px; margin-right: 10px;" />
                    <nz-select formControlName="servicePlanType" nzShowSearch nzPlaceHolder="请选择类型" style="margin-left: 10px;width:150px; " (ngModelChange)="getDataList()">
                        <nz-option *ngFor="let item of servicePlanTypeList" [nzValue]="item.key"
                                   [nzLabel]="item.value">
                        </nz-option>
                    </nz-select>
                </div>
                <div >
                    <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;">
                        <i nz-icon nzType="search"></i>
                        查询
                    </button>
                    <button nz-button nzType="primary" (click)="reset()" style=" margin-left: 10px;">
                        <i nz-icon nzType="reload"></i>
                        重置
                    </button>
                </div>
            </form>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">实例账单</span>
            </div>
            <nz-table #tableList [nzLoading]="isLoading"
                      [nzItemRender]="renderItemTemplate"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                [nzData]="tableData">
                <thead>
                    <tr>
                        <ng-container *ngFor="let col of cols">
                            <th
                                    *ngIf="col.width"
                                    nz-resizable
                                    nzBounds="window"
                                    nzPreview
                                    [nzWidth]="col.width"
                                    [nzMaxWidth]="400"
                                    [nzMinWidth]="60"
                                    [nzShowSort]="col.showSort"
                                    [nzSortFn]="col.sortFlag"
                                    [nzSortOrder]="col.allowSort"
                                    [nzColumnKey]="col.ColumnKey"
                                    (nzResizeEnd)="onResize($event, col.title)"
                            >
                                {{ col.title }}
                                <nz-resize-handle nzDirection="right">
                                    <div class="resize-trigger"></div>
                                </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style=" min-width:275px">
                                {{ col.title }}
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data">
                        <td>{{data.instanceName}}</td>
                        <td>{{data.servicePlanTypeText}}</td>
                        <td>{{data.servicePlanStartTime}}</td>
                        <td>{{data.servicePlanEndTime}}</td>
                        <td>{{data.ownerDisplayName}}</td>
                        <td>{{data.actualCost}}</td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item" (click)="viewDetail(data);" [ngClass]="{'disabled': data.enabledStatus}">
                                    <i nzTooltipTitle="使用明细" nzTooltipContent="bottom" nz-tooltip class="icon" nz-icon nzType="search" nzTheme="outline"></i>
                                </div>
                            </div>
                            <div class="on-table-actions" [hidden]="!busyStatus[data.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon [nzType]="'loading'"></i>
<!--                                    {{ getBusyText(data) }}-->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="tableList.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                         let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{  page  }}</a>
            </ng-template>
        </div>
    </div>
</div>

<nz-modal [(nzVisible)]="detailModel" nzTitle=""
            [nzWidth]="1000"
          [nzCancelLoading]="isLoading" [nzClosable]="false"
          [nzFooter]="null">
    <ng-container *nzModalContent>
    <div>
        <div class="content-base">
            <div class="content-square gap_0">
                <div class="header mb_10 flex flex_sb">实例账单明细
                    <div>
                        <a nz-button class="default" (click)="detailModel=false">
                            关&nbsp;闭
                        </a>
                    </div>
                </div>
                <div class="row card">
                    <div class="column p_8 pl_20 flex_1">
                        名称
                    </div>
                    <div class="column p_8 pl_20 bg-white flex_3">
                        {{viewData.instanceName}}
                    </div>
                    <div class="column p_8 pl_20 flex_1">
                        所有者
                    </div>
                    <div class="column p_8 pl_20 bg-white flex_3">
                        {{viewData.ownerDisplayName}}
                    </div>
                </div>
                <div class="row card">
                    <div class="column pl_20 p_8 flex_1">
                        类型
                    </div>
                    <div class="bg-white pl_20 p_8 bg-white flex_3">
                        {{viewData.servicePlanTypeText}}
                    </div>
                    <div class="column p_8 pl_20 flex_1">
                        实际金额
                    </div>
                    <div class="column p_8 pl_20 bg-white flex_3">
                        {{viewData.actualCost}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="detail-model">
        <nz-table #detailList [nzLoading]="isLoading"
                  [nzLoadingDelay]="300" nzSize="small"
                  [nzFrontPagination]="false" nzBordered
                  [nzData]="detailData" #fixedTable
                  (nzQueryParams)="onDetailDataChange($event)"
                  [nzScroll]="{ y: '350px' }"
        >
            <thead>
            <tr>
                <ng-container *ngFor="let col of detailCols">
                    <th
                            *ngIf="col.width"
                            nz-resizable
                            nzBounds="window"
                            nzPreview
                            [nzWidth]="col.width"
                            [nzMaxWidth]="400"
                            [nzMinWidth]="60"
                            [nzShowSort]="col.showSort"
                            [nzSortFn]="col.sortFlag"
                            [nzSortOrder]="col.allowSort"
                            [nzColumnKey]="col.ColumnKey"
                            (nzResizeEnd)="onResize($event, col.title)"
                    >
                        {{ col.title }}
                        <nz-resize-handle nzDirection="right">
                            <div class="resize-trigger"></div>
                        </nz-resize-handle>
                    </th>
                    <th *ngIf="!col.width" style=" min-width:275px">
                        {{ col.title }}
                    </th>
                </ng-container>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let data of detailList.data">
                <td>{{data.billingCycle}}</td>
                <td>{{data.servicePlanStartTime}} ~ {{data.servicePlanEndTime}}</td>
                <td>{{data.originalCost}}</td>
                <td>{{data.discount === 100 ? '-' : (data.discount + '%')}}</td>
                <td>{{data.actualCost}}</td>
            </tr>
            <tr [hidden]="tableList.data.length || !isLoading"
                class="loading-placeholder">
                <td colspan="100%"></td>
            </tr>
            </tbody>
        </nz-table>
    </div>
    </ng-container>
</nz-modal>