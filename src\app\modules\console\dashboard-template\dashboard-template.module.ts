import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DashboardTemplateRoutingModule } from './dashboard-template-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';
import { LineChartTemplateComponent } from 'src/app/components/console/dashboard-template/line-chart/line-chart-template.component';
import { ColumnChartTemplateComponent } from 'src/app/components/console/dashboard-template/column-chart/column-chart-template.component';
import { PieChartTemplateComponent } from 'src/app/components/console/dashboard-template/pie-chart/pie-chart-template.component';
import { HorizontalColumnChartTemplateComponent } from 'src/app/components/console/dashboard-template/horizontal-column-chart-template/horizontal-column-chart-template.component';
import { MapChartTemplateComponent } from 'src/app/components/console/dashboard-template/map-chart-template/map-chart-template.component';
import { RingChartTemplateComponent } from 'src/app/components/console/dashboard-template/ring-chart-template/ring-chart-template.component';
import { WaveChartTemplateComponent } from 'src/app/components/console/dashboard-template/wave-chart-template/wave-chart-template.component';
import { LOCALE_ID } from '@angular/core';

@NgModule({
    declarations: [LineChartTemplateComponent, ColumnChartTemplateComponent, PieChartTemplateComponent, HorizontalColumnChartTemplateComponent, MapChartTemplateComponent, RingChartTemplateComponent, WaveChartTemplateComponent],
    imports: [CommonModule, SharedModule, DashboardTemplateRoutingModule],
    providers: [
        {provide: LOCALE_ID, useValue: 'zh-CN'}
    ],
    exports: [
        LineChartTemplateComponent,
        ColumnChartTemplateComponent,
        PieChartTemplateComponent,
        HorizontalColumnChartTemplateComponent,
        MapChartTemplateComponent,
        RingChartTemplateComponent,
        WaveChartTemplateComponent
    ]
})
export class DashboardTemplateModule {
    constructor(moduleRef: NgModuleRef<DashboardTemplateModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
