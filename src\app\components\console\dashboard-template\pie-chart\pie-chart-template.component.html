<div style="display: flex; height: 100%;">
    <div style="flex: 1; padding-left: 10px; display: flex; flex-direction: column;">
        <div style="text-align: left; font-weight: bold; padding-left: 10px; font-size: 12px">{{ title }}</div>
        <div style=" justify-content: center;height:100%;display: flex;flex-direction: column">
        <div *ngFor="let item of legendData" style="margin-bottom: 5px;display:flex;flex-direction: row;font-size: 12px;align-items: center">
            <span [style.background-color]="item.color" style="display: inline-block; width: 10px; height: 10px; margin-right: 5px; vertical-align: middle;border-radius: 50%"></span>
            <span style="flex:2;margin-left: 10px">{{ item.name }}</span>
            <span style="flex:1;">{{ item.value }}{{ item.unit }}</span>
            <span style="flex:1">({{ item.percent }}%)</span>
        </div>
        </div>
    </div>
    <div style="flex: 1;">
        <div echarts [options]="chartOptions" [merge]="updateOptions" [style.height.px]="chartHeight"></div>
    </div>
</div>
