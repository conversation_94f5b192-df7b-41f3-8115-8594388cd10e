<nz-modal [nzWidth]="800" [(nzVisible)]="isVisible" nzTitle="弹性公网IP列表" [nzFooter]=null [nzMaskClosable]="okLoading ? false : true"
    [nzOkLoading]="okLoading" (nzOnCancel)="handleCancel()">
    <ng-container *nzModalContent>
    <div class="action-bar clearfix" style="margin: -10px 0 10px">
        <div class="pull-right">
            <button nz-button nzType="primary" (click)="showCreateModal()">
                <i nz-icon nzType="plus" nzTheme="outline"></i>
                创建新绑定
            </button>
        </div>
    </div>
    <div class="on-panel-body" style="max-height:400px;overflow: auto;">
        <nz-table #instances [nzLoading]="false" [nzLoadingDelay]="300" [nzFrontPagination]="false" [nzData]="ipBindingList">
            <thead>
                <tr>
                    <ng-container *ngFor="let col of cols">
                        <th
                          *ngIf="col.width"
                          nz-resizable
                          nzBounds="window"
                          nzPreview
                          nzColumnKey="col.ColumnKey"
                          [nzWidth]="col.width"
                          [nzMaxWidth]="500"
                          [nzMinWidth]="80"
                          [nzShowSort]="col.showSort"
                          [nzSortFn]="col.sortFlag"
                          [nzSortOrder]="col.allowSort"
                          [nzColumnKey]="col.ColumnKey"
                          (nzResizeEnd)="onResize($event, col.title)"
                        >
                          {{ col.title }}
                          <nz-resize-handle nzDirection="right">
                            <div class="resize-trigger"></div>
                          </nz-resize-handle>
                        </th>
                        <th *ngIf="!col.width">
                          {{ col.title }}
                        </th>
                      </ng-container>
                    <!--<th width="25%">云服务器</th>
                    <th>云服务器端口</th>
                    <th>公网IP</th>
                    <th>公网端口</th>
                    <th>网络类型</th>
                    <th>操作</th>-->
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of ipBindingList;">
                    <td>
                        <span *ngIf="item.type === 'OUT'">出方向</span>
                        <span *ngIf="item.type === 'IN'">入方向</span>
                        <span *ngIf="item.type === 'IN_OUT'">出方向</span>
                    </td>
                    <td>
                        {{item.vmName}}
                    </td>
                    <td>
                        {{item.vm_port}}
                    </td>
                    <td>
                        {{item.elasticIpName}}
                    </td>
                    <td>{{item.public_port}}</td>
                    <td>
                        {{item.protocol == 'tcp' ? "TCP" : item.protocol == 'udp' ? "UDP" : "任何"}}
                    </td>
                    <td>
                        <div class="on-table-action-item"
                             nz-popconfirm
                             nzTooltipContent="top"
                             nzPopconfirmTitle="确定要删除公网IP绑定吗？"
                             (nzOnConfirm)="deleteBinding(item);">
                            <i nzTooltipTitle="删除"
                               nzTooltipContent="bottom"
                               nz-tooltip
                               class="icon fa fa-trash-o">
                            </i>
                        </div>
                    </td>
                </tr>
            </tbody>
        </nz-table>
    </div>
    </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="createModalVisible" nzTitle="绑定弹性公网IP" nzOkText="绑定" [nzMaskClosable]="createLoading ? false : true" [nzWidth]="600"
          [nzOkLoading]="createLoading" (nzOnCancel)="createLoading ? false : handleCreateCancel()" [nzCancelDisabled]="createLoading" (nzOnOk)="handleCreateOk()">
    <ng-container *nzModalContent>
    <form [formGroup]="bind" class="config-content sm">
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">虚机名称</span>
                    <span class="lable-span">{{ vmObject.name }}</span>
                </label>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">公网IP </span>
                    <nz-select nzShowSearch formControlName="elasticIpId" nzPlaceHolder="请选择公网IP">
                        <nz-option *ngFor="let item of elasticIpList" [nzValue]="item.id" [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(bind.get('elasticIpId'))" class="form-hint error">
                    <div *ngIf="bind.get('elasticIpId').hasError('required')">
                        请选择要绑定的公网IP
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">类型</span>
                    <nz-select formControlName="type" nzPlaceHolder="请选择类型" (ngModelChange)="bindChange($event)">
                        <nz-option *ngFor="let item of type" [nzValue]="item.name" [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(bind.get('type'))" class="form-hint error">
                    <div *ngIf="bind.get('type').hasError('required')">
                        请选择要绑定的类型
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">协议</span>
                    <nz-select formControlName="protocol" nzPlaceHolder="请选择协议" (ngModelChange)="bindChange($event)">
                        <nz-option *ngFor="let item of protocol" [nzValue]="item.name" [nzLabel]="item.title">
                        </nz-option>
                    </nz-select>
                </label>
                <div *ngIf="isInvalid(bind.get('protocol'))" class="form-hint error">
                    <div *ngIf="bind.get('protocol').hasError('required')">
                        请选择要绑定协议
                    </div>
                </div>
            </div>
            <div class="field-item"
                [ngClass]="{'required': protocolShowRequired}">
                <label>
                    <span class="label-text">云主机端口</span>
                    <!-- <input nz-input type="text" formControlName="vm_port" maxlength="5" placeholder="请输入云主机端口"> -->
                    <input nz-input type="text" formControlName="vm_port"  [disabled]="!protocolShow" [readOnly]="!protocolShow" [(ngModel)]="vm_input" id="vm_port_input">
                    <span class="small tip">端口号范围在0~65535之间</span>
                </label>
                <div *ngIf="isInvalid(bind.get('vm_port'))" class="form-hint error">
                    <div *ngIf="bind.get('vm_port').hasError('required')">
                        云主机端口不能为空
                    </div>
                    <div *ngIf="bind.get('vm_port').hasError('pattern')">
                        端口号范围在0~65535之间
                    </div>
                </div>
            </div>
            <div class="field-item"
                 [ngClass]="{'required': publicShow}">
                <label>
                    <span class="label-text">公网IP端口</span>
                    <!-- <input nz-input type="text" formControlName="public_port" maxlength="5" placeholder="请输入公网IP端口"> -->
                    <input nz-input type="text" formControlName="public_port" [disabled]="!publicShow" [readOnly]="!publicShow" [(ngModel)]="public_input" id="public_input">
                    <span class="small tip">端口号范围在0~65535之间</span>
                </label>
                <div *ngIf="isInvalid(bind.get('public_port'))" class="form-hint error">
                    <div *ngIf="bind.get('public_port').hasError('required')">
                        公网IP端口不能为空
                    </div>
                    <div *ngIf="bind.get('public_port').hasError('pattern')">
                        端口号范围在0~65535之间
                    </div>
                </div>
            </div>
        </div>
    </form>
    </ng-container>
</nz-modal>
